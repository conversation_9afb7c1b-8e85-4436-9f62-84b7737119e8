import baseUrl from '@/utils/baseUrl';
export function getSysElements () {
  let res = [
    // {
    //   code: 'sop0001',
    //   name: '期初库存',
    //   router: 'views/bohStock/table.vue',
    //   type: 'TABLE'
    // },
  ]
  return  res
}

// export function getUrlObject() {
//   // 组件的默认配置接口
//   let object_url = baseUrl.auth
//   let ips = baseUrl.ips
//   let url_object = {
//     default: ips,
//     getViewVersions: object_url, // 获取视图版本集合
//     getLayoutConfigByVersion: object_url, // 根据视图版本ID获取具体视图信息
//     saveViewVersion: object_url, // 保存视图（新增/修改）
//     deleteView: object_url,  // 删除视图
//     saveExpression: object_url, // 表达式保存，用于自定义列和自定义公式
//     delExpression: object_url, // 表达式删除，用于自定义列和自定义公式
//     getImportTemplates: object_url,
//     getImportTemplateCfg: object_url,
//     saveImportTemplate: object_url,
//     excelImport: ips,

//     getPivotDataSource: ips, // 透视表获取数据源集合
//     getEnums: ips, // 获取枚举集合
//     getPivotColumns: ips, // 透视表根据数据源KEY获取对应字段集合
//     getPivotExpressionSource: ips, // 获取透视表透视图的计算公式集合
//     getPivotData: ips, // 获取透视表结果数据
//     getKpiValue: ips, // 获取KPI结果值
//     getChartData: ips, // 获取自定义图标数据
//     getColumnsBySql: ips,// 根据SQL获取字段集合
//     getChartType: ips,// 获取图表类型集合
//     getCalType: ips,// 获取图表类型集合
//     getKpiListData: ips,// 获取已知KPI集合
//     getDataBySql: ips,// 根据SQL获取表格集合
//     executInferface: ips,
//     getTables: ips, // 获取表集合
//     getColumnsByTableName: ips, //根据表获取字段集合
//     saveCustomTable: ips, //保存表数据
//     delCustomTable: ips, //删除表数据
//   }
//   return url_object
// }
export function getUrlObject(moudle) {
  // 组件的默认配置接口
  let object_url = baseUrl.auth;

  let base = moudle ? moudle : 'ips';
  let currentBasePath = baseUrl[base];
  
  let url_object = {
    default: currentBasePath,
    getViewVersions: object_url, // 获取视图版本集合
    getLayoutConfigByVersion: object_url, // 根据视图版本ID获取具体视图信息
    saveViewVersion: object_url, // 保存视图（新增/修改）
    deleteView: object_url,  // 删除视图
    saveExpression: object_url, // 表达式保存，用于自定义列和自定义公式
    delExpression: object_url, // 表达式删除，用于自定义列和自定义公式
    getImportTemplates: object_url,
    getImportTemplateCfg: object_url,
    saveImportTemplate: object_url,
    excelImport: currentBasePath,

    getPivotDataSource: currentBasePath, // 透视表获取数据源集合
    getEnums: currentBasePath, // 获取枚举集合
    getPivotColumns: currentBasePath, // 透视表根据数据源KEY获取对应字段集合
    getPivotExpressionSource: currentBasePath, // 获取透视表透视图的计算公式集合
    getPivotData: currentBasePath, // 获取透视表结果数据
    getKpiValue: currentBasePath, // 获取KPI结果值
    getChartData: currentBasePath, // 获取自定义图标数据
    getColumnsBySql: currentBasePath,// 根据SQL获取字段集合
    getChartType: currentBasePath,// 获取图表类型集合
    getCalType: currentBasePath,// 获取图表类型集合
    getKpiListData: currentBasePath,// 获取已知KPI集合
    getDataBySql: currentBasePath,// 根据SQL获取表格集合
    executInferface: currentBasePath,
    getTables: currentBasePath, // 获取表集合
    getColumnsByTableName: currentBasePath, //根据表获取字段集合
    saveCustomTable: currentBasePath, //保存表数据
    delCustomTable: currentBasePath, //删除表数据
  }
  return url_object
}