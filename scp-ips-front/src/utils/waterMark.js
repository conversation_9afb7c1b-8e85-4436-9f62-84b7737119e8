export function watermark(settings) {
  //默认设置
  var defaultSettings = {
    watermark_txt: 'text',
    watermark_x: 20, //水印起始位置x轴坐标
    watermark_y: 20, //水印起始位置Y轴坐标
    watermark_rows: 6, //水印行数
    watermark_cols: 10, //水印列数
    watermark_x_space: 100, //水印x轴间隔
    watermark_y_space: 100, //水印y轴间隔
    watermark_color: '#ffffff', //水印字体颜色
    watermark_alpha: 1, //水印透明度
    watermark_fontsize: '16px', //水印字体大小
    watermark_font: 'Helvetica Neue', //水印字体
    watermark_fontWeight: 'bold', //水印字体加粗
    watermark_textShadow: '0px 0px 1px #000000',
    watermark_width: 210, //水印宽度
    watermark_height: 80, //水印长度
    watermark_angle: 0, //水印倾斜度数
  }

  var oTemp = document.createDocumentFragment()
  var mask_div = document.createElement('div')
  mask_div.id = 'mask_div'
  mask_div.className = 'mask_div'
  mask_div.appendChild(
    document.createTextNode(
      'BPIM计划集成系统  / ' + settings.watermark_txt || defaultSettings.watermark_txt,
    ),
  )
  mask_div.style.position = 'fixed'
  mask_div.style.left = settings.opened ? '220px':'70px'
  mask_div.style.top = '12px'
  mask_div.style.zIndex = '9999'
  mask_div.style.pointerEvents = 'none' //pointer-events:none  让水印不遮挡页面的点击事件
  mask_div.style.opacity = defaultSettings.watermark_alpha
  mask_div.style.fontSize = defaultSettings.watermark_fontsize
  mask_div.style.fontFamily = defaultSettings.watermark_font
  mask_div.style.fontWeight = defaultSettings.watermark_fontWeight
  mask_div.style.textShadow = defaultSettings.watermark_textShadow
  mask_div.style.color = defaultSettings.watermark_color
  mask_div.style.textAlign = 'right'
  mask_div.style.display = 'block'
  oTemp.appendChild(mask_div)
  document.body.appendChild(oTemp)
}
