<template>
  <div class="container" v-loading="loading">
    <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-row>
        <el-form-item label="表名" prop="dataTableName">
          <el-input
            size='mini'
            v-model="searchForm.dataTableName"
            :placeholder="$t('placeholderInput')"
            clearable
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item label="数据主键" prop="primaryId">
          <el-input
            size='mini'
            v-model="searchForm.primaryId"
            :placeholder="$t('placeholderInput')"
            clearable
            :loading="loading"
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            style="margin-right: 4px"
            type="primary"
            @click="onSearch"
            size="mini"
            icon="el-icon-search"
          >
            查询
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="main">
      <vxe-table
        ref="vxeTable"
        border
        show-overflow
        show-header-overflow
        show-footer-overflow
        auto-resize
        height="auto"
        size="mini"
        :row-config="{ isTree: true, isCurrent: true, isHover: true }"
        :tree-config="{ childrenField: 'children' }"
        :column-config="{ resizable: true, isHover: true }"
        :virtual-y-config="{enabled: true, gt: 0}"
        :cell-config="{ height: 200 }"
        :data="tableData"
      >
        <vxe-column type="seq" width="40" fixed="left"></vxe-column>
        <vxe-column
          v-for="(item, index) in tableColumns"
          :key="item.prop + index"
          :field="item.prop"
          :title="item.label"
          :width="item.width"
          :fixed="item.fixed ? 'left' : ''"
        >
          <template #default="scope">
            <template v-if="item.dataType === 'DATE'">
              {{ getDate(scope.row[item.prop]) }}
            </template>
            <template v-else-if="item.prop === 'beforeData' || item.prop === 'afterData'">
              <div style="width: 100%; height: 180px; overflow: auto;">
                <div v-for="(item, index) in scope.row[item.prop]" :key="item[0]+index" style="width: 100%;">
                  <span style="color: #2395ff;">{{ item[0] }}</span>: {{ item[1] }}
                </div>
              </div>
            </template>
            <template v-else>
              {{ scope.row[item.prop] }}
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      @size-change="onSearch()"
      @current-change="QueryComplete()"
      :page-sizes="[20, 50, 100, 200, 500]"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      layout="total, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
</template>
<script>
import {
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  fetchList
} from "@/api/componentCommon";
import {dropdownEnum} from "@/api/dfpApi/dropdown";
import {getDataApi} from "@/api/dc/rowDataChangeRecord"
import moment from "moment"

export default {
  name: "rowDataChangeRecord",
  components: {},
  props: {
    componentKey: {type: String, default: ""}, // 组件key
    titleName: {type: String, default: ""},
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      // ImportUrl: `${baseUrl.dfp}/supplierSubmission/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "",
      },
      tableColumns: [
        {
          label: '表名',
          prop: 'dataTableName',
          dataType: 'CHARACTER',
          width: '140',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '数据主键',
          prop: 'primaryId',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '变更前数据集',
          prop: 'beforeData',
          dataType: 'CHARACTER',
          width: '240',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '变更后数据集',
          prop: 'afterData',
          dataType: 'CHARACTER',
          width: '240',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '用户名',
          prop: 'operateUser',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '数据版本号',
          prop: 'versionValue',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '创建时间',
          prop: 'createTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '最后更新时间',
          prop: 'modifyTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
      ],
      tableData: [],
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "",
      ScreenColumnVagueData: [],
      pageSize: 100,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      currentPage: 1,
      searchForm: {
        dataTableName: "",
        primaryId: "",
      },
      total: 0
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {
  },
  methods: {
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = {...this.currentUserPolicy, ...params};
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    onSearch() {
      this.currentPage = 1;
      this.QueryComplete();
    },
    // 表格查询数据
    QueryComplete() {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      }

      const data = {
        primaryId: this.searchForm.primaryId,
        dataTableName: this.searchForm.dataTableName,
      }
      this.loading = true
      getDataApi(data, params)
        .then(response => {
          this.loading = false
          if (response.success) {
            let data = response?.data?.content ?? [];
            data.forEach(item => {
              item.beforeData = JSON.parse(item.beforeData || "{}");
              item.beforeData = Object.entries(item.beforeData) ?? [];
              item.afterData = JSON.parse(item.afterData || "{}");
              item.afterData = Object.entries(item.afterData) ?? [];
            });
            this.tableData = data;
            console.log('分页查询成功', data);
            this.total = response?.data?.totalElements ?? 0;
          }
        })
        .catch(error => {
          this.loading = false
          console.log('分页查询异常', error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplete(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {
    },
    // 编辑数据方法
    EditDataFun(tableData) {
    },
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
    },
    DeleteData() {
    },
    RowClick(e) {
      this.$emit('showSql', e)
    },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {
    },
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({enumKeys: enumsKeys.join(',')}).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          console.log('获取枚举值', data);
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },

    getDate(str) {
      let result = '';
      try {
        result = moment(str).format('YYYY-MM-DD hh:mm:ss');
      }catch (e) {

      }
      return result;
    }
  },
};
</script>
<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }

  .show-top-right {
    position: absolute;
    top: 20px;
    right: $gap;

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color:#005ead;
  cursor: pointer;
  p {
    margin: 0;
  }
}
</style>
