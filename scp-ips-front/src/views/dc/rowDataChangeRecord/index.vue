<template>
  <div id="lowCode">
    <yhl-lcdp
      ref="lcdp"
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObject"
      :sysElements="this.getSysElements"
      @loaderComponent="loaderComponent"
      @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <Table
          ref="C001"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C001').name"
        ></Table>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
import Table from './table.vue'
export default {
  name: 'rowDataChangeRecord',
  components: {
    Table,
  },
  data() {
    return {
      componentKey: '',
      customContainers: [],
      collectionId: '',
      routingStepId: '',
    }
  },
  created() {
    this.initParams()
    this.loadCustomContainers()
  },
  methods: {
    initParams() {
      let key = this.$route.path.toUpperCase().replace(/\//g, '_')
      this.componentKey = key
    },
    // 初始化自定义内置容器
    loadCustomContainers() {
      this.customContainers.push({
        id: 'C001',
        position: {
          x: 0,
          y: 0,
          w: 50,
          h: 20,
        },
        name: '行数据变更记录',
        bindElement: {
          type: 'SYS_BUILTIN_PAGE',
          model: 'SYS_BUILTIN_PAGE',
          config: undefined,
        },
      })
    },
    // 自定义页面自动查询方法
    customPageQuery(item, layoutSetConfig) {
      let _item = JSON.parse(JSON.stringify(item))
      if (item.id === 'C001' || item.id === 'C002') {
        if (
          item.bindElement.hasOwnProperty('config') &&
          item.bindElement.config.hasOwnProperty('conf')
        ) {
          _item.bindElement.config.conf.id = layoutSetConfig.conf.version
          _item.bindElement.config.componentId = layoutSetConfig.conf.version
        }
        const params = {
          conf: _item.bindElement.config,
          customExpressions: layoutSetConfig.customExpressions,
        }
        this.$refs[item.id].setParams(params)
        this.$refs[item.id].QueryComplete()
      }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig(item) {
      if (item.id === 'C001' || item.id === 'C002') {
        return this.$refs[item.id].getCurrentUserPolicy()
      }
    },
    customPageResize(item) {
      console.log(item)
      this.$refs[item.id].$refs.yhltable.handleResize()
    },
    loaderComponent(router, id) {
      Promise.resolve(require('@/' + router).default).then((data) => {
        this.$refs.lcdp.setSysObjComponent(data, id)
      })
    },
  },
}
</script>
<style scoped>
#lowCode {
  width: 100%;
  height: 100%;
}
</style>
