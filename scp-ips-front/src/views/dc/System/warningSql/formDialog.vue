<template>
  <div style="display: inline-block">
    <div>
      <el-button size="medium" icon="el-icon-circle-plus-outline" v-debounce="[addForm]"
      >{{ $t("addText") }}
      </el-button>
      <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]"
      >{{ $t("editText") }}
      </el-button>
      <el-button
        size="medium"
        icon="el-icon-edit-outline"
        v-debounce="[testSend]"
        :loading="sendLoading"
      >{{ $t("warning_testSend") }}
      </el-button>
      <el-button
        size="medium"
        icon="el-icon-edit-outline"
        v-debounce="[deleteBatch]"
        :loading="deleteLoading"
      >{{ $t("warning_delete") }}
      </el-button>
    </div>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      custom-class="custom-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="100px"
        size="mini"
        class="form-container"
      >
        <!-- 预警编码和名称 -->
        <el-row type="flex" justify="space-between">
          <el-col :span="8">
            <el-form-item label="预警编码" prop="warningCode">
              <el-input
                size="small"
                v-model="ruleForm.warningCode"
                :placeholder="$t('placeholderInput')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预警描述" prop="warningDescription">
              <el-input
                size="small"
                v-model="ruleForm.warningDescription"
                :placeholder="$t('placeholderInput')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发送方式" prop="sendWay">
              <el-radio-group v-model="ruleForm.sendWay" @change="changeSendWay">
                <el-radio label="1">邮箱</el-radio>
                <el-radio label="2">企微</el-radio>
                <!--                <el-radio label="3">短信</el-radio>-->
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 根据发送方式展示不同联系方式 邮箱-->
        <div v-show="ruleForm.sendWay === '1'">
          <el-row type="flex" justify="space-between">
            <el-col :span="8">
              <el-form-item label="接收者" prop="receiver" :rules="dynamicRules.receiver">
                <el-input
                  size="small"
                  v-model="ruleForm.receiver"
                  :placeholder="$t('placeholderSelect')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="抄送者" prop="carbonCopy">
                <el-input
                  size="small"
                  v-model="ruleForm.carbonCopy"
                  :placeholder="$t('placeholderSelect')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="密送者" prop="blindCarbonCopy">
                <el-input
                  size="small"
                  v-model="ruleForm.blindCarbonCopy"
                  :placeholder="$t('placeholderSelect')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div style="padding-left:100px; margin-top:10px; margin-bottom: 20px; color:red;font-size: 12px;">
            注：邮箱之间采用英文";"分割
          </div>
        </div>
        <!-- 企微 -->
        <div v-show="ruleForm.sendWay === '2'">
          <el-row type="flex" justify="space-between">
            <el-col :span="8">
              <el-form-item label="工号" prop="staffCode" :rules="dynamicRules.staffCode">
                <el-input
                  size="small"
                  v-model="ruleForm.staffCode"
                  :placeholder="$t('placeholderSelect')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div style="padding-left:100px; margin-top:10px; margin-bottom: 20px; color:red;font-size: 12px;">
            注：工号之间采用英文";"分割
          </div>
        </div>
        <!-- 短信 -->
        <div v-show="ruleForm.sendWay === '3'">
          <el-row type="flex" justify="space-between">
            <el-col :span="11">
              <el-form-item label="手机" prop="phone" :rules="dynamicRules.phone">
                <el-input
                  size="small"
                  style="width: 180px"
                  v-model="ruleForm.phone"
                  :placeholder="$t('placeholderSelect')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div style="padding-left:120px; margin-top:10px; margin-bottom: 20px; color:red;font-size: 12px;">
            注：手机号之间采用英文";"分割
          </div>
        </div>

        <!--  状态和cron-->
        <el-row type="flex" justify="space-between">
          <el-col :span="8">
            <el-form-item label="状态" prop="enabled">
              <el-radio v-model="ruleForm.enabled" label="YES"
              ><span style="font-size: 12px">生效</span></el-radio
              >
              <el-radio v-model="ruleForm.enabled" label="NO"
              ><span style="font-size: 12px">失效</span></el-radio
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Cron" prop="pollingTime">
              <el-popover v-model="cronPopover">
                <vueCron
                  @change="changeCron"
                  @close="cronPopover = false"
                  i18n="cn"
                ></vueCron>
                <el-input
                  size="small"
                  v-model="ruleForm.pollingTime"
                  slot="reference"
                  placeholder="请输入定时策略"
                  @click="cronPopover = true"
                ></el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否有数据前提" prop="warnType">
              <el-radio-group v-model="ruleForm.warnType" size="small">
                <el-radio label="1">有</el-radio>
                <el-radio label="0">无</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!--  时间范围  -->
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="生效开始日期" prop="activationDate">
              <el-date-picker
                size="small"
                v-model="ruleForm.activationDate"
                type="datetime"
                style="width: 180px"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="生效结束日期" prop="invalidDate">
              <el-date-picker
                size="small"
                v-model="ruleForm.invalidDate"
                type="datetime"
                style="width: 180px"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="24">
            <el-form-item label="正文内容" prop="textContent">
              <textarea
                style="width: 100%; height: 50px"
                v-model="ruleForm.textContent"
                :placeholder="$t('placeholderInput')"
              ></textarea>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-show="ruleForm.sendWay === '1'">
          <el-row type="flex" justify="space-between">
            <el-col :span="24">
              <el-form-item label="预警内容(sql)" prop="sqlContext">
                <textarea
                  style="width: 100%; height: 50px"
                  v-model="ruleForm.sqlContext"
                  :placeholder="$t('placeholderInput')"
                ></textarea>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer">
        <el-button class="cancelButton" v-debounce="[handleClose]">
          {{ $t("cancelText") }}
        </el-button>
        <el-button class="okButton" v-debounce="[submitForm]">
          {{ $t("okText") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  warningSqlCreate,
  warningSqlDelete,
  warningSqlScenario,
  warningSqlSend,
  warningSqlUpdate,
} from "@/api/dc/warningSql";

export default {
  name: "warningSql",
  components: {},
  props: {
    rowInfo: {type: Object, default: () => ({})},
    selectedRowKeys: {type: Array, default: () => []},
  },
  data() {
    return {
      dialogVisible: false,
      title: "",
      sendLoading: false,
      deleteLoading: false,
      cronPopover: false,
      ruleForm: {
        warningCode: "",
        warningDescription: "",
        receiver: "",
        sendWay: "1",
        staffCode: "",
        phone: "",
        carbonCopy: "",
        blindCarbonCopy: "",
        enabled: "YES",
        activationDate: "",
        invalidDate: "",
        pollingTime: "",
        warnType: "1",
        // databaseTenant: '',
        sqlContext: "",
        textContent: "",
      },
      rules: {
        warningCode: [
          {required: true, message: this.$t("placeholderInput"), trigger: "blur"},
        ],
        warningDescription: [
          {required: true, message: this.$t("placeholderInput"), trigger: "blur"},
        ],
        sendWay: [
          {required: true, message: this.$t("placeholderInput"), trigger: "blur"},
        ],
        receiver: [
          {required: true, message: this.$t("placeholderInput"), trigger: "blur"}
        ],
        staffCode: [
          {required: true, message: this.$t("placeholderInput"), trigger: "blur"}
        ],
        phone: [
          {required: true, message: this.$t("placeholderInput"), trigger: "blur"}
        ]
      },
      treeData: [],
      stockPointList: [],
      operationCodeList: [],
      ProductionLeadTimeEnum: [],
      mainLineGroupOption: [],
      specialClassOption: [],
      userEmailOptions: [],
      scenarioOptions: []
    };
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        // getUserEmail().then(res => {
        //   const data = res.data
        //   if (data.success) {
        //     this.userEmailOptions = data.data
        //   } else {
        //     this.$message.error(data.msg || "获取用户失败")
        //   }
        // })
        warningSqlScenario().then((res) => {
          if (res.success) {
            this.scenarioOptions = res.data;
          } else {
            this.scenarioOptions = [];
          }
        });
      }
    },
  },
  mounted() {
  },
  computed: {
    //计算必选逻辑
    dynamicRules() {
      const rules = {
        receiver: [],
        staffCode: [],
        phone: []
      };
      // 根据radioType动态分配必填规则
      switch (this.ruleForm.sendWay) {
        case '1':
          rules.receiver = this.rules.receiver;
          break;
        case '2':
          rules.staffCode = this.rules.staffCode;
          break;
        case '3':
          rules.phone = this.rules.phone;
          break;
      }
      return rules;
    }
  },
  methods: {
    //清除必选逻辑
    changeSendWay(val) {
      this.ruleForm.sendWay = val;
      this.$nextTick(() => {
        ['receiver', 'staffCode', 'phone'].forEach(field => {
          this.$refs.ruleForm?.clearValidate(field);
        });
      });

    },
    changeCron(val) {
      this.ruleForm.pollingTime = val;
    },
    // 新增界面
    addForm() {
      this.dialogVisible = true;
      this.title = this.$t("addText");
    },
    // 编辑界面
    editForm() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      this.dialogVisible = true;
      this.title = this.$t("editText");
      const info = this.rowInfo;
      if (info.id) {
        this.ruleForm = {...info};
      }
    },
    deleteBatch() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning("请选择数据");
        return;
      }
      this.deleteLoading = true;
      warningSqlDelete(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.msg || "删除成功");
            this.$emit("submitAdd");
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          this.$message.error("删除失败");
        });
      this.deleteLoading = false;
    },
    //测试sql
    testSend() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      const info = this.rowInfo;
      this.sendLoading = true;
      warningSqlSend(info)
        .then((res) => {
          const data = res.data;
          if (data.success) {
            this.$message.success(data.msg || "发送成功");
          } else {
            this.$message.error(data.msg);
          }
        })
        .catch((err) => {
          this.$message.error("发送失败");
        });
      this.sendLoading = false;
    },
    handleClose() {
      this.$refs["ruleForm"].resetFields();
      this.dialogVisible = false;
      this.ruleForm = {
        warningCode: "",
        warningDescription: "",
        receiver: "",
        carbonCopy: "",
        blindCarbonCopy: "",
        enabled: "YES",
        activationDate: "",
        invalidDate: "",
        pollingTime: "",
        warnType: "1",
        sqlContext: "",
        textContent: "",
      };
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm));
          if (this.title === this.$t("addText")) {
            warningSqlCreate(form)
              .then((res) => {
                const data = res.data;
                if (data.success) {
                  this.$message.success(this.$t("addSucceeded"));
                  this.handleClose();
                  this.$emit("submitAdd");
                } else {
                  this.$message.error(data.msg || this.$t("addFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("addFailed"));
              });
            return;
          }
          if (this.title === this.$t("editText")) {
            form.id = this.rowInfo.id;
            warningSqlUpdate(form)
              .then((res) => {
                const data = res.data;
                if (data.success) {
                  this.$message.success(this.$t("editSucceeded"));
                  this.$parent.SelectionChange([]);
                  this.handleClose();
                  this.$emit("submitAdd");
                } else {
                  this.$message.error(data.msg || this.$t("editFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("editFailed"));
              });
            return;
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style scoped>
.el-row {
  border-bottom-width: 0 !important;
}

.input[aria-hidden=true] {
  display: none !important;
}
.custom-dialog {
  /* 设置对话框的最大高度 */
  max-height: 80vh;
  /* 确保对话框内容不会超出视口 */
  overflow: hidden;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

.form-container {
  /* 设置对话框的最大高度 */
  max-height: calc(80vh - 140px);
  /* 确保对话框内容不会超出视口 */
  overflow-y: auto;
}

.form-container {
  font-size: 12px;
}

.cancelButton {
  width: 50px;
  height: 35px;
  padding: 0;
}

.okButton {
  color: #FFF;
  background-color: #409EFF;
  border-color: #409EFF;
  width: 50px;
  height: 35px;
  padding: 0;
}
</style>
