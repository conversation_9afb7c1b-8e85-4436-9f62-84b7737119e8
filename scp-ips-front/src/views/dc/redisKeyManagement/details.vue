<template>
  <div style="display:inline-block">
    <el-link
      size="mini"
      type="primary"
      @click="show"
    >
      详情
    </el-link>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :close-on-click-modal="false"
    >
      <div class="container" v-loading="loading">
        <div class="main">
          <vxe-table
            ref="vxeTable"
            border
            show-overflow
            show-header-overflow
            show-footer-overflow
            auto-resize
            height="350"
            size="mini"
            :row-config="{ isTree: true, isCurrent: true, isHover: true }"
            :tree-config="{ childrenField: 'children' }"
            :column-config="{ resizable: true, isHover: true }"
            :virtual-y-config="{enabled: true, gt: 0}"
            :data="tableData"
          >
            <vxe-column type="seq" width="40" fixed="left"></vxe-column>
            <vxe-column
              v-for="(item, index) in tableColumns"
              :key="item.prop + index"
              :field="item.prop"
              :title="item.label"
              :width="item.width"
              :fixed="item.fixed ? 'left' : ''"
            >
              <template #default="scope">
                <template v-if="item.prop === 'existKey'">
                  {{ scope.row.existKey ?  scope.row.existKey === 'YES' ? '是' : '否' : scope.row.existKey }}
                </template>
                <template v-else-if="item.dataType === 'DATE'">
                  {{ getDateStr(scope.row[item.prop]) }}
                </template>
                <template v-else-if="item.prop === 'configCode'">
                  <el-popover
                    placement="bottom"
                    trigger="click"
                    v-if="scope.row.keyResult"
                  >
                    <div style="mix-width: 500px;max-height: 50vh;overflow: auto">
                      <json-viewer
                        :value="scope.row.keyResult"
                        copyable
                        :expand-depth=1
                      ></json-viewer>
                    </div>
                    <el-link
                      slot="reference"
                      type="primary"
                      style="margin-left: 16px"
                    >
                      {{ scope.row[item.prop] }}
                    </el-link>
                  </el-popover>
                  <template v-else>
                    {{ scope.row[item.prop] }}
                  </template>
                </template>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
            </vxe-column>
            <vxe-column title="操作" width="100">
              <template #default="scope">
                <el-link
                  size="mini"
                  type="primary"
                  @click="clear(scope.row)"
                >
                  清除
                </el-link>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {clearApi, getDetailsApi} from "@/api/dc/redisKeyManagement";
import moment from "moment/moment";
export default {
  name: "details",
  props: {
    rowInfo: {type: Object, default: () => ({})},
  },
  data() {
    return {
      dialogVisible: false,
      title: "详情",
      loading: false,
      tableData: [],
      tableColumns: [
        {
          label: '编码',
          prop: 'configCode',
          dataType: 'CHARACTER',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '过期时间',
          prop: 'expireTime',
          dataType: 'DATE',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: 'key是否存在',
          prop: 'existKey',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
      ]
    }
  },
  methods: {

    show() {
      this.dialogVisible = true;
      this.getData();
    },

    async getData() {
      let configCode = this.rowInfo?.configCode || '';

      if(!configCode) {
        this.tableData = [];
        console.log(configCode);
        return;
      }

      this.loading = true;
      try {
        let res = await getDetailsApi({configCode});
        if(res.success) this.tableData = res?.data ?? [];
      }catch (e) {

      }
      this.loading = false;
    },

    async clear(row) {
      let {configCode} = row;
      try {
        this.loading = true;
        let res = await clearApi({configCode});
        if(res.success) {
          this.$message.success('清除成功');
          const index = this.tableData.findIndex(item => item.configCode === row.configCode);
          if (index !== -1) {
            this.tableData.splice(index, 1);
          }
        }
      }catch (e) {

      }
      this.loading = false;
    },

    getDateStr(time) {
      return time ? moment(time).format('YYYY-MM-DD hh:mm:ss') : ''
    },
  }
}
</script>

<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }

  .show-top-right {
    position: absolute;
    top: 20px;
    right: $gap;

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color: #005ead;
  cursor: pointer;

  p {
    margin: 0;
  }
}
</style>
