<template>
  <div class="container" v-loading="loading">
    <div style="display: flex; justify-content: flex-end; gap: 3px;">
      <Auth url="/mrp/originalFilmDynamic/versionRecord">
        <div slot="toolBar">
          <el-button size="small" @click="versionVisible = true">版本记录</el-button>
        </div>
      </Auth>
      <Auth url="/mrp/originalFilmDynamic/generate">
        <div slot="toolBar">
          <el-button size="small" :loading="generateLoading" @click="generateData">计算</el-button>
        </div>
      </Auth>
      <el-button size="small" @click="exportData">导出</el-button>
    </div>
    <div class="main">
      <vxe-table
        ref="vxeTable"
        border
        show-overflow
        show-header-overflow
        show-footer-overflow
        auto-resize
        height="auto"
        size="mini"
        :data="tableData"
        :cellStyle="cellStyle"
        :row-config="{ isCurrent: true, isHover: true }"
        :column-config="{resizable: true}"
        :cell-class-name="cellClassName"
        :scroll-x="{enabled: true}"
        :scroll-y="{enabled: true}"
        >
        <vxe-column field="productColor" title="颜色" width="135" fixed="left" rowspan="2"></vxe-column>
        <vxe-column field="productThickness" title="厚度" width="60" fixed="left" rowspan="2"></vxe-column>

        <!-- 本地库存分组 -->
        <vxe-colgroup title="本地库存" align="center">
          <vxe-column field="localInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="localInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 在途库存分组 -->
        <vxe-colgroup title="在途库存" align="center">
          <vxe-column field="transitInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="transitInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 供应商/内部浮法分组 -->
        <vxe-colgroup title="供应商/内部浮法" align="center">
          <vxe-column field="internalFloatGlassInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="internalFloatGlassInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 供应商/外部浮法分组 -->
        <vxe-colgroup title="供应商/外部浮法" align="center">
          <vxe-column field="externalFloatGlassInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="externalFloatGlassInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 在供应商合计分组 -->
        <vxe-colgroup title="在供应商合计" align="center">
          <vxe-column field="floatGlassInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="floatGlassInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 积压库存分组 -->
        <vxe-colgroup title="积压库存" align="center">
          <vxe-column field="backlogInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="backlogInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 总库存分组 -->
        <vxe-colgroup title="总库存" align="center">
          <vxe-column field="totalInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="totalInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 有效总库存量分组 -->
        <vxe-colgroup title="有效总库存量" align="center">
          <vxe-column field="effectiveTotalInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="effectiveTotalInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 当月消耗分组 -->
        <vxe-colgroup title="当月消耗" align="center">
          <vxe-column field="currentMonthConsumeInventoryArea" title="面积" width="80"></vxe-column>
          <vxe-column field="currentMonthConsumeInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 后三个月滚动预测分组 -->
        <vxe-colgroup title="后三个月滚动预测" align="center">
          <vxe-column field="nextOneMonthConsumeInventoryArea" title="后一个月" width="80"></vxe-column>
          <vxe-column field="nextOneMonthConsumeInventoryWeight" title="吨位" width="80"></vxe-column>
          <vxe-column field="nextTwoMonthConsumeInventoryArea" title="后两个月" width="80"></vxe-column>
          <vxe-column field="nextTwoMonthConsumeInventoryWeight" title="吨位" width="80"></vxe-column>
          <vxe-column field="nextThreeMonthConsumeInventoryArea" title="后三个月" width="80"></vxe-column>
          <vxe-column field="nextThreeMonthConsumeInventoryWeight" title="吨位" width="80"></vxe-column>
        </vxe-colgroup>

        <vxe-column field="inventoryDays" title="库存天数" width="80" rowspan="2"></vxe-column>
      </vxe-table>
      <vxe-table
        :data="otherData"
        style="margin-top:20px"
        border
        highlight-hover-row
        show-header-overflow
        show-overflow
        >
        <vxe-column field="indicator" title="指标" width="120" fixed="left"></vxe-column>
        <vxe-column field="nextOneMonth" title="后一个月" width="100"></vxe-column>
        <vxe-column field="nextTwoMonth" title="后两个月" width="100"></vxe-column>
        <vxe-column field="nextThreeMonth" title="后三个月" width="100"></vxe-column>
      </vxe-table>
    </div>
    <el-dialog
      title="版本记录"
      :visible.sync="versionVisible"
      append-to-body="true"
      v-dialogDrag="true"
      :close-on-click-modal="false"
      width="800px"
    >
      <div style="width: 100%; height: 400px;">
        <Table
          ref="C001"
          componentKey="versionRecord"
          @showVersion="showVersion"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import baseUrl from '@/utils/baseUrl';
import { getListApi,generateData } from "@/api/mrpApi/originalFilmDynamic";
import { myExportDataSimple } from "@/api/mrpApi/componentCommon";
import Table from "./versionRecord/table.vue";

export default {
  name: "BInventoryAlert",
  data(){
    return {
      loading: false,
      tableData: [],
      total: 0,
      otherData: [],
      generateLoading: false,
      versionId: '',
      versionVisible: false,
    }
  },
  components: {
    Table,
  },
  methods: {
    async queryComplate(){
      this.loading = true;
      this.tableData = []
      try {
        const params = {};
        if (this.versionId) params.versionId = this.versionId;
        let {success, data, msg} = await getListApi(params);
        if (!success) {
          this.$message.error(msg || '获取数据失败');
        }
        const { list = [], total = 0 } = data;
        this.tableData = list;
        this.total = total;

        const { 
          glassNextOneMonthGrowthRate,
          glassNextTwoMonthGrowthRate,
          glassNextThreeMonthGrowthRate,
          planNextOneMonthGrowthRate,
          planNextTwoMonthGrowthRate,
          planNextThreeMonthGrowthRate
        } = data
        this.otherData = [
          {
            indicator: '原片增幅(%)',
            currentMonth: '-',
            nextOneMonth: glassNextOneMonthGrowthRate || '-',
            nextTwoMonth: glassNextTwoMonthGrowthRate || '-',
            nextThreeMonth: glassNextThreeMonthGrowthRate || '-'
          },
          {
            indicator: '计划增幅(%)',
            currentMonth: '-',
            nextOneMonth: planNextOneMonthGrowthRate || '-',
            nextTwoMonth: planNextTwoMonthGrowthRate || '-',
            nextThreeMonth: planNextThreeMonthGrowthRate || '-'
          }
        ]
      } catch(e){
        this.tableData = [];
        this.otherData = [];
        this.total = 0;
        console.error(e);
      } finally {
        this.loading = false
      }
    },
    cellStyle({row, column}){
      if (row.productColor === '小计' || row.productColor === '合计') {
      return {
          backgroundColor: '#FFF200',
          fontWeight: 'bold'
        }
      }
      return {}
    },
    generateData(){
      this.generateLoading = true
      generateData().then(res => {
        this.generateLoading = false
        if(res.success){
          this.$message.success(res.msg || '生成成功')
        } else {
          this.$message({showClose: true, message: res.msg || '生成失败', type: 'error', duration: 0});
        }
      }).finally(() => {
        this.generateLoading = false
      })
    },
    exportData(){
      myExportDataSimple(`${baseUrl.mrp}/glassInventoryDynamicDataReport/exportData`)
    },
    showVersion(row) {
      this.versionVisible = false;
      this.versionId = row.id;
      this.queryComplate();
    },
  }
}
</script>
<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .main {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }
}
</style>