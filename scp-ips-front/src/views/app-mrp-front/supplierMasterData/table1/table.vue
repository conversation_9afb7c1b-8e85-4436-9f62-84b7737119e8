<template>
  <div style="height: 100%" class="materialsSafety" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMds"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ExportVisible="false"
      :ImportVisible="true"
      :requestHeaders="requestHeaders"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :ExportTemplate="ExportTemplate"
      :RowClick="rowClick"
    >
      <template slot="header">
        <!-- <el-button
          size="medium"
          icon="el-icon-circle-plus-outline"
          v-debounce="[addForm]"
        >
          {{ $t('addText') }}
        </el-button> -->
      <span
        v-if="warningStatus.text"
        :style="{ color: warningStatus.color, fontSize: '12px' }"
      >
        {{ warningStatus.text }}
      </span>
        <Auth url="/mrp/supplierMasterData/manualSync">
          <div slot="toolBar">
            <el-button size="medium" icon="el-icon-refresh" :loading="syncLoading" v-debounce="[manualSync]">手动同步</el-button>
          </div>
        </Auth>
        <Auth url="/mrp/supplierMasterData/edit">
          <div slot="toolBar">
            <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">
              {{ $t('editText') }}
            </el-button>
          </div>
        </Auth>
      </template>
    </yhl-table>
    <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      :currencyUnitOptions="currencyUnitOptions"
      :countUnitOptions="countUnitOptions"
      @submitAdd="QueryComplate()"
    />
    <!-- <template slot="header">
          <FormDialog
        ref="formDialogRef"
        :rowInfo="selectedRows[0]"
        :enums="enums"
        :selectedRowKeys="selectedRowKeys"
        @submitAdd="QueryComplate()"
      />
      </template>
  </yhl-table> -->
  </div>
</template>
<script>
import FormDialog from './formDialog.vue';
import {dropdownEnum} from "@/api/mdsApi/itemManagement/index";
import {
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
  myExportTemplate,
  myExportDataSimple
} from '@/api/mrpApi/componentCommon';
import {
  fetchList,
} from '@/api/mdsApi/componentCommon';
import baseUrl from '@/utils/baseUrl';
import {deleteApi,syncSupplier} from '@/api/mrpApi/supplierMasterData';
import Auth from "@/components/Auth/index.vue";
import { handleInterfaceWarning } from '@/api/componentCommon'
export default {
  name: 'materialsSafety',
  components: {
    Auth,
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'newSupplier',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'newSupplier',
      },
      tableColumns: [
        {
          label: '供应商代码',
          prop: 'supplierCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '供应商名称',
          prop: 'supplierName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '组织',
          prop: 'organizationCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '组织名称',
          prop: 'organizationName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '最后更新时间',
          prop: 'modifyTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '供应商类型',
          prop: 'supplierAbbreviation',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },

        // {
        //   label: '供应商可配送天数',
        //   prop: 'supplierDeliveryDate',
        //   dataType: 'CHARACTER',
        //   width: '150',
        //   align: 'center',
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: 'TEXT',
        //   fshow: 1,
        // },
        {
          label: '供应商要货计划展示周期（天）',
          prop: 'planDisplayCycle',
          dataType: 'CHARACTER',
          width: '150',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '供应商滚动预测展示周期（月）',
          prop: 'planDisplayCycleMonth',
          dataType: 'CHARACTER',
          width: '150',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: "星期几收货",
          prop: "receivingWeekDay",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "每月几号收货",
          prop: "receivingMonthDay",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "失效时间",
          prop: "headExpiryTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_pro_product_stock_point',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      syncLoading: false,
      warningStatus: { text: '', color: '' },
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: 'MDS',
      // Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    };
  },
  activated() {
    document.body.classList.add('supplierMasterData')
  },
  mounted() {
    document.body.classList.add('supplierMasterData')
  },
  deactivated() {
    document.body.classList.remove('supplierMasterData')
  },
  methods: {
   checkInterfaceStatus(id) {
      handleInterfaceWarning(id).then(result => {
         this.warningStatus = result;
      });
    },
    manualSync() {
      this.syncLoading = true
      syncSupplier().then(res => {
        this.syncLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(()=>{
        this.syncLoading = false
      })
    },
    // 导出模版
    ExportTemplate() {
      let url = `/api/mds/excelCommon/exportTemplate?objectType=newSupplier`;
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.click();
    },
    // 导出数据
    ExportData() {
      let params = {
        queryCriteriaParam: '',
        sortParam: ''
      };
      let url = `/api/mds/supplierData/export`;
      myExportDataSimple(url, params).then(response => {});
    },
    //导入
    ImportChange(data, v) {
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          if(data.response?.data === null){
            this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
          } else {
            this.handleText(data.response.data)
          }
        }
      }
    },
    // 导入 多条数据提示信息
    handleText(res){
      let result = []
      let gathered = res.filter(item => item.infoType == 'GATHER')
      let others = res.filter(item => item.infoType !== 'GATHER')
      if (gathered.length > 0) {
        let textToRemove = "详情请展开";
        result.push(gathered[0].errorDetail.replace(textToRemove, "").trim());
      }
      let otherErrors = others.map(item =>
        `第${item.displayIndex}行：` + item.errorDetail
      );
      result.push(...otherErrors)
      this.$message({showClose: true, message: result.join('<br>') || this.$t("importFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
    },
    // 点击行
    rowClick(e) {
      let row = e ? e : null;
      this.$emit("chooseId", row);
    },
    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row]);
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm();
      });
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '');
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',');
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      };
      const url = `supplierData/page`;
      const method = 'get';
      this.loading = true;
      const currentTime = new Date();
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
            this.checkInterfaceStatus(43);
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log('分页查询异常', error);
        });
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : '';
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t('viewSaveFailed'));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || '';
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append('objectType', this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            });
          }
        },
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      // console.log(v, '勾选的数据11');
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      deleteApi(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t('deleteFailed'));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          );
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
  },
};
</script>

<style>
/* .supplierMasterData #import > :first-child{
  display: none;
}
.supplierMasterData #import > .yhl-table-exportTemplate{
  display: block;
} */
</style>
