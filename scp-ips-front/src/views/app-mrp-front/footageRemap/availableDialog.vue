<template>
  <div>
    <el-dialog
      :title="title"
      width="1062px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dps-dialog"
      :close-on-click-modal="false"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form :inline="true" :model="searchForm" class="search-bar">
        <el-row>
          <el-form-item label="切裁率" prop="productCode">
            <el-input v-model="searchForm.cuttingRatePercentage" size="mini" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              v-debounce="[availableSearch]"
              size="mini"
              icon="el-icon-search"
            >
              查询
            </el-button>
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              size="mini"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-debounce="[oneClick]"
              size="mini"
            >
              一键替代
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button
              v-debounce="[batchAlternative]"
              size="mini"
            >
              批量替代
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        border
        size="mini"
        height="300"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%">
        <el-table-column
          type="selection"
          fixed
          width="40">
        </el-table-column>
        <el-table-column
          prop="replacedProductCode"
          label="原片"
          width="150">
        </el-table-column>
        <el-table-column
          prop="demandProductCode"
          label="被替代原片"
          width="150">
        </el-table-column>
        <el-table-column
          prop="existingInventory"
          label="现有库存"
          width="150">
        </el-table-column>
        <!-- <el-table-column
          prop="cuttingRate"
          label="切裁率"
          width="100">
        </el-table-column> -->
        <el-table-column
          prop="currentMonthTotalDemand"
          label="当月需求量"
          width="120">
        </el-table-column>
        <el-table-column
          prop="nextMonthTotalDemand"
          label="次月需求量"
          width="120">
        </el-table-column>
        <el-table-column
          prop="alternativeQuantity"
          label="替代数量"
          width="120">
          <template slot-scope="scope">
            <el-input v-model="scope.row.alternativeQuantity" size="mini" :placeholder="$t('placeholderInput')"></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="cuttingRate"
          label="切裁率"
          width="120">
        </el-table-column>
        <el-table-column
          prop="priority"
          label="优先级"
          width="120">
        </el-table-column>
        <el-table-column
          prop="startTime"
          label="开始时间"
          width="150">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.startTime"
              type="date"
              style="width: 100%"
              size="mini"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('placeholderSelect')">
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column
          prop="endTime"
          label="结束时间"
          width="150">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.endTime"
              type="date"
              style="width: 100%"
              size="mini"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('placeholderSelect')">
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          width="120">
          <template slot-scope="scope">
            <el-input v-model="scope.row.remark" size="mini" :placeholder="$t('placeholderInput')"></el-input>
          </template>
        </el-table-column>
        <!-- fixed="right" -->
        <el-table-column
          label="操作"
          width="100">
          <template slot-scope="scope">
            <el-button @click="handelClick(scope.row)" type="text" size="small">替代</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">
          {{ $t('cancelText') }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
  <script>
import { availableSearch, batchAlternative } from '@/api/mrpApi/originalInventoryBatchDetails/inventoryOurFactoryDetail'
export default {
  name: 'calendar',
  data() {
    return {
      searchForm: {
        cuttingRatePercentage: 0.8,
      },
      dialogVisible: false,
      title: '',
      typeMap: {
        BACKLOG: '积压替代',
        MATERIAL_SHORTAGE: '缺料替代查找'
      },
      tableData: [],
      options: [],
      loading: false,
      row: {},
      type: '',
      selectedRows: [],
      dateRange: []
    };
  },
  mounted() {
    // this.dropdownEnum('com.yhl.scp.mds.basic.calendar.enums.ShiftTypeEnum')
  },
  methods: {
    availableSearch() {
      this.selectedRows = [];
      this.loading = true
      availableSearch({productCode: this.row.rawProductCode, alternativeType: this.type, cuttingRatePercentage: this.searchForm.cuttingRatePercentage})
        .then(res => {
          this.selectedRows = [];
          this.loading = false
          if (res.success) {
            this.tableData = res.data
          } else {
            this.$message.error(res.msg || this.$t('queryFailed'))
          }
        })
        .catch(err => {
          this.loading = false
        })
    },
    addForm(res, type) {
      this.row = res;
      this.type = type;
      this.dialogVisible = true;
      this.title = this.typeMap[type]
      this.availableSearch()
    },
    handleClose() {
      this.dialogVisible = false;
      this.tableData = []
    },
    handelClick(row) {
      if(!this.check(row)) return;

      let _row = {
        "alternativeQuantity": row.alternativeQuantity,
        "createTime": row.createTime,
        "creator": row.creator,
        "cuttingRate": row.cuttingRate,
        "demandProductCode": row.demandProductCode,
        "enabled": row.enabled,
        "endTime": row.endTime,
        "id": row.id,
        "modifier": row.modifier,
        "modifyTime": row.modifyTime,
        "priority": row.priority,
        "remainingQuantity": row.remainingQuantity,
        "remark": row.remark,
        "replacedProductCode": row.replacedProductCode,
        "replacedQuantity": row.replacedQuantity,
        "rowIndex": row.rowIndex,
        "startTime": row.startTime,
        "versionValue": row.versionValue
      };

      this.loading = true
      batchAlternative([_row])
        .then((res) => {
          this.loading = false
          if (res.success) {
            this.$message.success(this.$t('operationSucceeded'));
            this.handleClose();
            this.$emit('submitAdd');
          } else {
            this.$message.error(res.msg || this.$t('operationFailed'));
          }
        })
        .catch((err) => {
          this.loading = false
          this.$message.error(this.$t('operationFailed'));
        });
    },
    submitForm() {
      console.log(this.tableData)
    },
    handleSelectionChange(val) {
      this.selectedRows = val;
    },
    check(row) {
      let result = true;
      if (!row.alternativeQuantity) {
        this.$message.error('请输入代替数量！')
        return false;
      }
      if (!row.startTime) {
        this.$message.error('请选择开始时间！')
        return false;
      }
      if (!row.endTime) {
        this.$message.error('请选择结束时间！')
        return false;
      }
      // 开始时间和结束时间在当前时间到7天后的范围内
      const now = moment().startOf('day');
      const sevenDaysLater = moment().add(7, 'days').endOf('day');
      const start = moment(row.startTime);
      const end = moment(row.endTime);
      if (
          (start.isSameOrAfter(now) && start.isSameOrBefore(sevenDaysLater)) ||
          (end.isSameOrAfter(now) && end.isSameOrBefore(sevenDaysLater)) ||
          (start.isBefore(now) && end.isAfter(sevenDaysLater))
      ) {
        if (!row.remark?.trim()) {
          this.$message.error('请输入备注！');
          return false;
        }
      }
      return result;
    },
    oneClick() {
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择一条数据！');
        return;
      }
      this.selectedRows.forEach(item => {
        item.alternativeQuantity = item.existingInventory;
        if(this.dateRange && this.dateRange.length === 2) {
          item.startTime = this.dateRange[0];
          item.endTime = this.dateRange[1];
        }
      });
      this.batchAlternative();
    },
    batchAlternative() {
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择一条数据！');
        return;
      }

      for (let i = 0; i < this.selectedRows.length; i++) {
        let item = this.selectedRows[i];
        if(!this.check(item)) return;
      }

      let rows = this.selectedRows.map(row => {
        return {
          "alternativeQuantity": row.alternativeQuantity,
          "createTime": row.createTime,
          "creator": row.creator,
          "cuttingRate": row.cuttingRate,
          "demandProductCode": row.demandProductCode,
          "enabled": row.enabled,
          "endTime": row.endTime,
          "id": row.id,
          "modifier": row.modifier,
          "modifyTime": row.modifyTime,
          "priority": row.priority,
          "remainingQuantity": row.remainingQuantity,
          "remark": row.remark,
          "replacedProductCode": row.replacedProductCode,
          "replacedQuantity": row.replacedQuantity,
          "rowIndex": row.rowIndex,
          "startTime": row.startTime,
          "versionValue": row.versionValue
        };
      });

      this.loading = true
      batchAlternative(rows)
        .then((res) => {
          this.loading = false
          if (res.success) {
            this.$message.success(this.$t('operationSucceeded'));
            this.handleClose();
            this.$emit('submitAdd');
          } else {
            this.$message.error(res.msg || this.$t('operationFailed'));
          }
        })
        .catch((err) => {
          this.loading = false
          this.$message.error(this.$t('operationFailed'));
        });

    }
  },
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 0;
}
</style>
