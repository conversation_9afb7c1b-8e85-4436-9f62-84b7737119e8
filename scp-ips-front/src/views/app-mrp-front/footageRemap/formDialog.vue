<template>
  <div style="display:inline-block">
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dps-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="160px"
        size="mini"
      >
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="原片使用于本厂编码" prop="productCode">
              <span v-if="isBatch">{{ ruleForm.productCode }}</span>
              <el-input v-else v-model="ruleForm.productCode" size="small" :disabled="isEdit" />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="ERP-BOM" prop="rawProductCode">
              <span v-if="isBatch">{{ ruleForm.rawProductCode }}</span>
              <el-input v-else v-model="ruleForm.rawProductCode" size="small" :disabled="isEdit" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="替代BOM" prop="substituteProductCode">
              <span v-if="isBatch">{{ ruleForm.substituteProductCode }}</span>
              <el-input v-else v-model="ruleForm.substituteProductCode" size="small"/>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="生产BOM" prop="productionSubstituteProductCode">
              <el-select
              :placeholder="$t('selectHolder')"
              v-model="ruleForm.productionSubstituteProductCode"
              clearable
              filterable
              v-if="isEdit || isBatch"
              >
              <el-option
              v-for="item in bomOptions"
              :value="item.value"
              :key="item.value"
              :label="item.label"
              ></el-option>
            </el-select>
            <el-input v-else v-model="ruleForm.productionSubstituteProductCode" size="small" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="生产单耗" prop="productionInputFactor">
              <el-input v-model="ruleForm.productionInputFactor" size="small" />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="替代单耗" prop="substituteInputFactor">
              <el-input v-model="ruleForm.substituteInputFactor" size="small" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <!-- <el-col :span="11">
            <el-form-item label="需求计算规则" prop="rule">
              <el-select style="width: 100%" v-model="ruleForm.rule" clearable size="small" filterable :placeholder="$t('placeholderSelect')">
                <el-option v-for="item in ruleOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="11">
            <el-form-item label="ERP-BOM库存是否禁用" prop="rawProductInventoryDisabled">
              <el-switch
                v-model="ruleForm.rawProductInventoryDisabled"
                size="small"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="是否启用" prop="enabled">
              <el-switch
                v-model="ruleForm.enabled"
                size="small"
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">{{ $t('cancelText') }}</el-button>
        <el-button size="small" type="primary" :loading="loading" @click="submitForm">{{ $t('okText') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Auth from '@/components/Auth'
import { updateApi, addApi, batchUpdateApi, dropDownProductBom } from '@/api/mrpApi/footageRemap/index';
export default {
  name: 'footageRemapFormDialog',
  components: {Auth},
  props: {
    rowInfo: {type: Array, default: () => []},
    enums: {type: Array, default: () => []},
    selectedRowKeys: {type: Array, default: () => []},
    mode: {type: String, default: 'edit'} // 'add' | 'edit' | 'batch'
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      ruleForm: {},
      rules: {},
      ruleOptions: [],
      yesNoOptions: [],
      bomOptions: [],
      loading: false,
      isEdit: false,
      isBatch: false
    }
  },
  // watch: {
  //   dialogVisible(nv) {
  //     if (nv) {
  //       this.initForm();
  //     }
  //   },
  // },
  methods: {
    addForm(defaultRow) {
      this.mode = 'add';
      this.isEdit = false;
      this.isBatch = false;
      this.title = '原片替代映射-新增';
      this.dialogVisible = true;
      this.ruleForm = defaultRow ? {...defaultRow} : {
        enabled: false,
        rawProductInventoryDisabled: false
      };
      this.initEnums();
    },
    editForm() {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning('请选择一条数据');
        return;
      }
      this.mode = 'edit';
      this.isEdit = true;
      this.isBatch = false;
      this.title = '原片替代映射-修改';
      this.dialogVisible = true;
      const info = this.rowInfo[0];
      this.ruleForm = {...info};
      this.ruleForm.enabled = this.ruleForm.enabled === "YES";
      this.ruleForm.rawProductInventoryDisabled = this.ruleForm.rawProductInventoryDisabled === "YES";
      this.initEnums();
    },
    batchEditForm(rows) {
      if (!rows || rows.length < 2) {
        this.$message.warning('请至少选择两条数据');
        return;
      }
      this.mode = 'batch';
      this.isEdit = false;
      this.isBatch = true;
      this.title = '原片替代映射-批量修改';
      this.dialogVisible = true;
      this.ruleForm = this.getBatchDefault(rows);
      this.ruleForm.enabled = this.ruleForm.enabled === "YES";
      this.ruleForm.rawProductInventoryDisabled = this.ruleForm.rawProductInventoryDisabled === "YES";
      this.initEnums();
    },
    getBatchDefault(rows) {
      const fields = [
        'rawProductCode',
        'productCode',
        'substituteProductCode',
        'productionSubstituteProductCode',
        'productionInputFactor',
        'substituteInputFactor',
        'rule',
        'enabled',
        'rawProductInventoryDisabled'
      ];

      const result = { ...rows[0] };

      fields.forEach(f => {
        if (rows.every(row => row[f] === rows[0][f])) {
          result[f] = rows[0][f];
        } else {
          result[f] = '';
        }
      });

      if (!result.rawProductInventoryDisabled) {
        result.rawProductInventoryDisabled = false;
      }
      return result;
    },
    initEnums() {
      this.ruleOptions = this.enums.find(item => item.key === 'com.yhl.scp.mrp.enums.GlassSubstitutionRelationshipRuleEnum')?.values || [];
      this.yesNoOptions = this.enums.find(item => item.key === 'com.yhl.platform.common.enums.YesOrNoEnum')?.values || [];
      this.getBomOptions()
    },
    async getBomOptions(){
      try{
        const res = await dropDownProductBom([this.ruleForm.rawProductCode])
        this.bomOptions = res.data
      } catch {
        console.log('获取bom生产下拉数据出错')
      }
    },
    handleClose() {
      this.dialogVisible = false;
      this.ruleForm = {};
      this.$refs['ruleForm'] && this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.loading = true;
      let form = JSON.parse(JSON.stringify(this.ruleForm));
      if(this.isBatch){
        form.enabled = form.enabled ? "YES" : "NO";
        form.rawProductInventoryDisabled = form.rawProductInventoryDisabled ? "YES" : "NO";
        delete form.id;
        const updateRows = this.rowInfo.map(item => ({
          id: item.id,
          ...form,
          substituteProductCode: item.substituteProductCode,
          productCode: item.productCode,
          rawProductCode: item.rawProductCode,
        }));
        batchUpdateApi(updateRows)
          .then(res => {
            this.loading = false;
            if (res.success) {
              this.$message.success('批量修改成功');
              this.handleClose();
              this.$emit('submitAdd');
            } else {
              this.$message.error(res.msg || '批量修改失败');
            }
          })
          .catch(() => {
            this.loading = false;
            this.$message.error('批量修改失败');
          });
      }else if(this.isEdit){
        form.enabled = form.enabled ? "YES" : "NO";
        form.rawProductInventoryDisabled = form.rawProductInventoryDisabled ? "YES" : "NO";
        updateApi([form])
          .then(res => {
            this.loading = false;
            if (res.success) {
              this.$message.success('修改成功');
              this.handleClose();
              this.$emit('submitAdd');
            } else {
              this.$message.error(res.msg || '修改失败');
            }
          })
          .catch(() => {
            this.loading = false;
            this.$message.error('修改失败');
          });
      }else{
        form.enabled = form.enabled ? "YES" : "NO";
        form.rawProductInventoryDisabled = form.rawProductInventoryDisabled ? "YES" : "NO";
        delete form.id;
        addApi(form)
          .then(res => {
            this.loading = false;
            if (res.success) {
              this.$message.success('新增成功');
              this.handleClose();
              this.$emit('submitAdd');
            } else {
              this.$message.error(res.msg || '新增失败');
            }
          })
          .catch(() => {
            this.loading = false;
            this.$message.error('新增失败');
          });
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  border: none;

  .el-form-item {
    width: 100%;
  }
}
</style>
