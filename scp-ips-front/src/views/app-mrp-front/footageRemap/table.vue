<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :ExportVisible="false"
      :ExportTemplate="ExportTemplate"
      :requestHeaders="requestHeaders"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
      <template slot="header">
        <Auth url="/mrp/footageRemap/add">
          <div slot="toolBar">
            <el-button size="medium" @click="onAddClick">新增</el-button>
          </div>
        </Auth>
        <Auth url="/mrp/footageRemap/batchEdit">
          <div slot="toolBar">
            <el-button size="medium" :disabled="selectedRowKeys.length < 2" @click="onBatchEditClick">批量修改</el-button>
          </div>
        </Auth>
        <Auth url="/mrp/footageRemap/edit">
          <div slot="toolBar">
            <el-button size="medium" icon="el-icon-edit-outline" @click="editForm">
              {{ $t("editText") }}
            </el-button>
          </div>
        </Auth>
        <Auth url="/mrp/footageRemap/import">
          <div slot="toolBar">
            <el-button
              size="medium"
              @click="importData"
              :loading="importLoading"
            >
              导入
            </el-button>
          </div>
        </Auth>
        <Auth url="/mrp/footageRemap/generate">
          <div slot="toolBar">
            <el-button
              size="medium"
              @click="generateData"
              :loading="generateLoading"
            >
              替代数据生成
            </el-button>
          </div>
        </Auth>
      </template>

      <template slot="header">
        <FormDialog
          ref="formDialogRef"
          :rowInfo="selectedRows"
          :enums="enums"
          :selectedRowKeys="selectedRowKeys"
          @submitAdd="QueryComplate()"
        />
      </template>

      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'stockPointCode'" :class="tableRowClassName(scope.row)">
          {{ scope.row[scope.column.prop] }}
        </div>
        <div style="display: flex;justify-content: space-around;" v-if="scope.column.prop == 'operation'">
          <el-link type="primary" @click="handleSend(scope.row, '1')">积压替代</el-link>
          <el-link type="primary" @click="handleSend(scope.row, '2')">缺料替代查找</el-link>
        </div>
      </template>
    </yhl-table>
    <AvailableDialog
      ref="availableDialog"
      @submitAdd="QueryComplate()"
    />
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import { deleteApi, importData, generateData, ExportTemplate } from '@/api/mrpApi/footageRemap/index';
import AvailableDialog from './availableDialog.vue'
import { dropdownEnum, dropdownEnumCollection } from "@/api/mrpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/mrpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import Auth from "@/components/Auth/index.vue";
export default {
  name: "footageRemap",
  components: {
    Auth,
    FormDialog,
    AvailableDialog
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "glassSubstitutionRelationship",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "glassSubstitutionRelationship",
      },
      tableColumns: [
        {
          label: "工厂",
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope:true
        },
        {
          label: "颜色厚度",
          prop: "colorThickness",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "ERP-BOM",
          prop: "rawProductCode",
          dataType: "CHARACTER",
          width: "150",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "替代BOM",
          prop: "substituteProductCode",
          dataType: "CHARACTER",
          width: "150",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "原片使用于本厂编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "毛坯规格",
          prop: "blankSpec",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求计算规则",
          prop: "rule",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mrp.enums.GlassSubstitutionRelationshipRuleEnum"
        },
        {
          label: "产品尺寸",
          prop: "productSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "淋子方向",
          prop: "linziDirection",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "毛坯单耗",
          prop: "blankInputFactor",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "原片对毛坯单耗",
          prop: "glassInputFactor",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "切裁率",
          prop: "cuttingRate",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "生产单耗",
          prop: "productionInputFactor",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "替代单耗",
          prop: "substituteInputFactor",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "生产BOM",
          prop: "productionSubstituteProductCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "是否启用",
          prop: "enabled",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum"
        },
        {
          label: "生产切裁率",
          prop: "productionCuttingRate",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "ERP-BOM库存是否禁用",
          prop: "rawProductInventoryDisabled",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum"
        },
        // {
        //   label: "操作",
        //   prop: "operation",
        //   dataType: "CHARACTER",
        //   width: "180",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   fscope: true,
        // },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_pro_product_stock_point",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      importLoading: false,
      generateLoading: false
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {},
  methods: {
    ExportTemplate(){
      ExportTemplate()
    },
    importData() {
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.style.display = 'none';

      fileInput.addEventListener('change', async (e) => {
        try {
          this.importLoading = true;
          const file = e.target.files[0];
          if (!file) return;

          const formData = new FormData();
          formData.append('file', file);
          const response = await importData(formData);

          if (response.success) {
            this.$message.success('导入成功')
            this.QueryComplate();
            if(response.data) {
              this.$nextTick(() => {
                this.$message({
                  showClose: true,
                  message: response.data,
                  type: 'warning',
                  duration: 0
                });
              })
            }
          }else {
            this.$message.error(response.msg || '导入失败')
          }
        } catch (error) {
          console.error(error);
          this.$message.error(this.$t('uploadFailed'))
        } finally {
          document.body.removeChild(fileInput); // 清理 DOM
          this.importLoading = false;
        }
      });
      document.body.appendChild(fileInput);
      fileInput.click();
    },
    async handleSend(row, t) {
      if (t === '1') {
        this.$refs.availableDialog.addForm(row, 'BACKLOG')
        return
      }
      if (t === '2') {
        this.$refs.availableDialog.addForm(row, 'MATERIAL_SHORTAGE')
        return
      }
    },
    generateData(){
      this.generateLoading = true
      generateData().then(res => {
        this.generateLoading = false
        if(res.success){
          this.$message.success(res.msg || '生成成功')
        } else {
          this.$message({showClose: true, message: res.msg || '生成失败', type: 'error', duration: 0});
        }
      }).finally(() => {
        this.generateLoading = false
      })
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row]);
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm();
      });
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      if (_sorts) {
        _sorts.push({"property":"enabled","label":"是否启用","seqNum":1,"sortOrder":"desc","fieldType":"CHARACTER"})
      } else {
        _sorts = [{"property":"enabled","label":"是否启用","seqNum":1,"sortOrder":"desc","fieldType":"CHARACTER"}]
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `glassSubstitutionRelationship/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    setMerge(a, b, c) {
      return a ? b + "(" + c + ")" : "";
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      deleteApi(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();

      // 使用 Promise.all 并行执行两个请求
      Promise.all([
        dropdownEnumCollection(enumsKeys),
        dropdownEnum({ enumKeys: enumsKeys.join(",") })
      ]).then(([res1, res2]) => {
        let data = [];
        if (res1.success) {
          data = res1.data || [];
        }
        if (res2.success) {
          for (let key in res2.data) {
            data.push({
              key: key,
              values: res2.data[key],
            });
          }
        }
        data.push(JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}"));
        this.enums = data;
      }).catch(error => {
        console.error("获取枚举数据失败:", error);
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    tableRowClassName(row) {
      const currentRawProductCode = row.rawProductCode;
      const allCodes = this.tableData.map(item => item.rawProductCode);
      const currentIndex = this.tableData.findIndex(item => item.id === row.id);

      let startIndex = currentIndex;
      while (startIndex > 0 && allCodes[startIndex - 1] === currentRawProductCode) {
        startIndex--;
      }

      let groupIndex = 0;
      let lastCode = null;
      for (let i = 0; i < startIndex; i++) {
        if (allCodes[i] !== lastCode) {
          groupIndex++;
          lastCode = allCodes[i];
        }
      }

      const colorClass = groupIndex % 2 === 0 ? 'even-group' : 'odd-group';

      if (row.recommendSubstitute === 'YES') {
        if(row.enabled === 'YES'){
          return `${colorClass}-normal`;
        }
        return `${colorClass}-recommend`;
      } else {
        return `${colorClass}-normal`;
      }
    },
    onAddClick() {
      // 选中一条数据时带默认值新增，否则空
      if (this.selectedRows.length === 1) {
        this.$refs.formDialogRef.addForm(this.selectedRows[0]);
      } else {
        this.$refs.formDialogRef.addForm();
      }
    },
    onBatchEditClick() {
      if (this.selectedRows.length < 2) {
        this.$message.warning('请至少选择两条数据');
        return;
      }
      this.$refs.formDialogRef.batchEditForm(this.selectedRows);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .table_content_row:has(.even-group-recommend) {
  background-color: #e0e0e0 !important;
}
::v-deep .table_content_row:has(.even-group-normal) {
  background-color: #e6f3ff !important;
}
::v-deep .table_content_row:has(.odd-group-recommend) {
  background-color: #f5f5f5 !important;
}
::v-deep .table_content_row:has(.odd-group-normal) {
  background-color: #ffffff !important;
}
</style>
