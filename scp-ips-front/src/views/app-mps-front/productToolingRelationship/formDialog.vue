
<template>
  <div style="display:inline-block">
    <el-button
      size="medium"
      icon="el-icon-circle-plus-outline"
      v-debounce="[addForm]"
    >{{$t('addText')}}</el-button>
    <el-button
      size="medium"
      icon="el-icon-edit-outline"
      v-debounce="[editForm]"
    >{{$t('editText')}}</el-button>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="120px"
        size="mini"
      >
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
              label="组织"
              prop="stockPointCode"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.stockPointCode"
                size="small"
                clearable
                filterable
                @change="selStockPointCode"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in stockPointList"
                  :key="item.value"
                  :label="item.label+'('+item.value+')'"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="产品编码"
              prop="productCode"
            >
              <SelectVirtual
                style="width: 100%"
                :selectConfig="selectConfig"
                v-model="ruleForm.productCode"
                size="small"
                placeholder="请输入产品编码(支持模糊查询)"
                remote
                :remote-method="debouncedRemoteMethod"
                :loading="loading"
              ></SelectVirtual>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
              label="工序"
              prop="standardStepCode"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.standardStepCode"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
                @change="getStandarResource"
              >
                <el-option
                  v-for="item in standardStepList"
                  :key="item.value"
                  :label="item.label+'('+item.value+')'"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
              label="工具标准资源"
              prop="standardResourceId"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.standardResourceId"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
                @change="selStandardResource"
              >
                <el-option
                  v-for="item in standardResourceList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="工具物理资源"
              prop="physicalResourceId"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.physicalResourceId"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in physicalResourceList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item label="替代工器具组号">
              <el-input size="small" v-model="ruleForm.altToolCode" :placeholder="$t('placeholderInput')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="是否有效" prop="enabled">
              <el-switch size="small" v-model="ruleForm.enabled"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span
        slot="footer"
        class="dialog-footer"
      >
          <el-button
            size="small"
            v-debounce="[handleClose]"
          >{{$t('cancelText')}}</el-button>
          <el-button
            size="small"
            type="primary"
            v-debounce="[submitForm]"
          >{{$t('okText')}}</el-button>
        </span>
    </el-dialog>
  </div>
</template>
<script>
import { newStockPointCreate, newStockPointUpdate, getStandarResource, getPhysicalResourceById  } from "@/api/mpsApi/productToolingRelationship";
import { selectProductionOrganizeList } from "@/api/mpsApi/foundation/coatingMaintenanceAmount";
import { getStandarStepCode } from "@/api/mpsApi/foundation/productionCapacity";
import { materialProductCodeDropDown } from "@/api/dfpApi/basicData/relationshipChange";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import {debounce} from "lodash";
export default {
  name: 'newStockPoint',
  components: {
    SelectVirtual
  },
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
    enums: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      ruleForm: {},
      rules: {
        stockPointCode: [{required: true, message: this.$t('placeholderInput'), trigger: 'blur'}],
        productCode: [{required: true, message: this.$t('placeholderInput'), trigger: 'blur'}],
        standardStepCode: [{required: true, message: this.$t('placeholderInput'), trigger: 'blur'}],
        standardResourceId: [{required: true, message: this.$t('placeholderInput'), trigger: 'blur'}],
        physicalResourceId: [{required: true, message: this.$t('placeholderInput'), trigger: 'blur'}],
      },
      stockPointCode: '',
      organizationId: '',
      standardResourceId: '',
      organizeTypeOption: [],
      StockPointTypeEnum: [],
      ediModeEnum: [],
      stockPointList: [],
      productCodeList: [],
      standardStepList: [],
      standardResourceList: [],
      physicalResourceList: [],
      selectConfig: {
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      loading: false
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.getStockPoint()
      }
    }
  },
  mounted() {
  },
  methods: {
    getStockPoint() {
      selectProductionOrganizeList().then(res => {
        if (res.success) {
          this.stockPointList = res.data.map(m => {
            return {
              value: m.organizationCode,
              label: m.organizationName,
              id: m.id
            }
          })
        } else {
          this.stockPointList = []
        }
      })
    },
    getStandarStepCodeList() {
      getStandarStepCode({stockPointCode: this.stockPointCode}).then(res => {
        if (res.success) {
          this.standardStepList = res.data;
        } else {
          this.standardStepList = []
        }
      })
    },
    getStandarResource(code) {
      getStandarResource({organizationId: this.organizationId, standardResourceCode: code}).then(res => {
        if (res.success) {
          this.standardResourceList = res.data
        } else {
          this.standardResourceList = []
        }
      })
    },
    getPhysicalResourceById() {
      getPhysicalResourceById({standardResourceId: this.standardResourceId}).then(res => {
        if (res.success) {
          this.physicalResourceList = [{ label: '*', value: '*' }, ...res.data];
        } else {
          this.physicalResourceList = []
        }
      })
    },
    selStockPointCode(e) {
      this.stockPointCode = e
      this.ruleForm.stockPointCode = e
      this.getStandarStepCodeList()
      const selectedItem = this.stockPointList.find(item => item.value === e);
      this.organizationId = selectedItem.id
    },
    selStandardResource(e) {
      this.standardResourceId = e
      this.ruleForm.standardResourceId = e
      this.ruleForm.altToolCode = this.ruleForm.productCode + '+' + e || ''
      this.getPhysicalResourceById()
    },
    addForm() {
      this.dialogVisible = true
      this.title = this.$t('addText')
      this.ruleForm.enabled = true
    },
    editForm() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      this.dialogVisible = true
      this.title = this.$t('editText')
      const info = this.rowInfo
      if (info.id) {
        this.ruleForm = {
          //stockPointCode productCode standardStepCode standardResourceId physicalResourceId altToolCode enabled
          stockPointCode: info.stockPointCode,
          productCode: info.productCode,
          standardStepCode: info.standardStepCode,
          standardResourceId: info.standardResourceId,
          physicalResourceId: info.physicalResourceId,
          enabled: info.enabled == 'YES',
          altToolCode: info.altToolCode
        }
        this.selStandardResource(info.standardResourceId)
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.ruleForm = {}
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          form.enabled = form.enabled ? 'YES' : "NO"
          if (this.title == this.$t('addText')) {
            newStockPointCreate(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('addSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('addFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('addFailed'))
              })
            return
          }
          if (this.title == this.$t('editText')) {
            form.id = this.rowInfo.id
            form.organizeId = this.rowInfo.organizeId
            newStockPointUpdate(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('editSucceeded'))
                  this.$parent.SelectionChange([])
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('editFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('editFailed'))
              })
            return
          }
        } else {
          return false;
        }
      });
    },
    // 模糊查找
    async remoteMethod(query) {
      if (query === '') {
        this.selectConfig.data = [];
        return;
      }
      this.loading = true;
      let data = [];
      try {
        let res = await materialProductCodeDropDown({productCode: query, stockPointCode:this.stockPointCode, productTypes:"P" ,logicContains:"NO"});
        if (res.success) {
          data = res?.data || [];
        }
      } catch (e) {
        console.error(e);
      }
      this.selectConfig.data = data;
      this.loading = false;

    },
    // 防抖后的 remoteMethod 方法
    debouncedRemoteMethod: debounce(function (query) {
      this.remoteMethod(query);
    }, 500),
  }
}
</script>
