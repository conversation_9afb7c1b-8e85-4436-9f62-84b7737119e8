<template>
  <div style="height: 100%">
    <div class="toolBar">
      <div class="toolBarButton">
        <el-button v-debounce="[tableExplan]">{{ $t("oneKeyExpansion") }}</el-button>
        <el-button v-debounce="[tableUnExplan]">{{
          $t("putAwayWithOneClick")
        }}</el-button>
      </div>
    </div>
    <div class="table-gantt">
      <div class="tableBox" ref="tableBox">
        <el-table
          :data="tableList"
          class="dom-table"
          ref="tableRef"
          border
          default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          style="width: 100%"
          height="100%"
          row-key="id"
          @expand-change="handledetail"
          show-overflow-tooltip
          :header-cell-style="{
            background: '#eef1f6',
            color: '#606266',
            height: '60px',
          }"
        >
          <el-table-column
            prop="supplyTypeStr"
            :label="$t('ganttChart_supplyTypeStr')"
            width="300"
          >
          </el-table-column>
          <el-table-column
            prop="supplyCode"
            :label="$t('ganttChart_supplyCode')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="orderNo"
            :label="$t('ganttChart_orderNo')"
            width="350"
          >
          </el-table-column>
          <el-table-column
            prop="planUnitCode"
            :label="$t('ganttChart_planUnitCode')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="planStatusStr"
            :label="$t('ganttChart_planStatusStr')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="allsetStatusStr"
            :label="$t('ganttChart_allsetStatusStr')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="productCode"
            :label="$t('ganttChart_productCode')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="productName"
            :label="$t('ganttChart_productName')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="quantity"
            :label="$t('ganttChart_quantity')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="fulfillQuantity"
            :label="$t('ganttChart_fulfillQuantity')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="availableTime"
            :label="$t('ganttChart_availableTime')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="planStartTime"
            :label="$t('ganttChart_planStartTime')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="planEndTime"
            :label="$t('ganttChart_planEndTime')"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="modifyTime"
            :label="$t('ganttChart_modifyTime')"
            width="250"
          >
          </el-table-column>
        </el-table>
      </div>
      <TreeGantt
        :dates="dates"
        :ganttInfo="ganttInfo"
        @scrollTop="setTableScrollTop"
        :expandIds="expandIds"
        class="dom-gantt"
        ref="treeGanttRef"
      />
    </div>
  </div>
</template>
<script>
import {
  orderSupplyChainData,
  upplyOrderInfoOfWorkOrderRnfold,
  getAllUnfoldGantt,
} from "@/api/mpsApi/routingTabs";
import TreeGantt from "./TreeGantt.vue";

export default {
  name: "orderSupplyChain",
  routingId: { type: String, default: "" },
  components: {
    TreeGantt,
  },
  data() {
    return {
      isExpansion: true,
      tableList: [],
      params: {},
      dates: [],
      ganttInfo: {},
      ganttList: [],
      expandIds: "",
      dataId: "",
      routingDataId: "",
      selectedRowKeys: [],
      dom: {
        scrollTop: "",
        clientHeight: "",
        scrollHeight: "",
      },
      // 判断滚动锁
      isScrollTop: false,
      listData: [],
      treeData: [], // 树结构扁平化
      upIds: [], // 全部的id
    };
  },
  watch: {
    // routingId(){
    //     this.getList();
    // },
  },
  mounted() {
    // 获取需要绑定的table
    this.dom = this.$refs.tableRef.bodyWrapper;
    this.dom.addEventListener("scroll", () => {
      let num = this.$refs.tableRef.bodyWrapper.scrollTop;
      if (this.isScrollTop) {
        this.$refs.treeGanttRef.changeMainMove(num, this.isScrollTop);
      }

      // if (scrollTop + windowHeight === scrollHeight) {
      // // 获取到的不是全部数据 当滚动到底部 继续获取新的数据
      // console.log('scrollTop', scrollTop + 'windowHeight', windowHeight + 'scrollHeight', scrollHeight)
      // }
    });
    let tableBox = this.$refs.tableBox;
    // 给表格绑定鼠标移入事件
    tableBox.addEventListener("mouseout", (e) => {
      //   console.log("鼠标进入表格行的回调函数", e);
      this.isScrollTop = true;
    });
    // tableBox.addEventListener("mousemove", (e) => {
    //   console.log("鼠标离开表格行的回调函数,",e);
    // });
  },
  methods: {
    // 点击左边表的展开
    handledetail(row, expandedRows) {
      let ids = [];
      function setGantt(arr) {
        arr.forEach((item) => {
          ids.push(item["fulfillmentId"]);
          if (item.children && item.children.length > 0) {
            // console.log(item.children,'数据**************')
            setGantt(item.children);
          }
        });
      }
      let updataId = [];

      this.upIds.forEach((item) => {
        ids.forEach((idItem) => {
          if (item != idItem) {
            updataId.push(item);
          }
        });
      });
      console.log("updataId", updataId);
      console.log("ids", ids);
      console.log("ids", ids);

      setGantt(row.children);
      if (!expandedRows) {
        setTimeout(() => {
          this.expandIds = updataId.join(",");
          console.log("this.expandIds", this.expandIds);
        }, 50);
      }

      console.log(row, "点击打开某条的data数据（props）");
      console.log(expandedRows, "expandedRows点击打开某条的data数据（props）");
    },
    setTableScrollTop(e, t) {
      //   console.log("接收到的数据", t);
      this.isScrollTop = t;
      if (!this.isScrollTop) {
        let body = document
          .querySelector(".dom-table")
          .querySelector(".el-table__body-wrapper");
        body.scrollTop = e._num;
      }
    },
    // 切换数据表格树形展开
    toggleRowExpansion() {
      this.isExpansion = !this.isExpansion;
      this.toggleRowExpansionAll(this.tableList, this.isExpansion);
    },
    // 一键展开和关闭
    toggleRowExpansionAll(data, isExpansion) {
      data.forEach((item) => {
        this.$refs.tableRef.toggleRowExpansion(item, isExpansion);
        if (item.children !== undefined && item.children !== null) {
          this.toggleRowExpansionAll(item.children, isExpansion);
        }
      });
    },

    // 给表格绑定鼠标移入事件
    // enterSelectionRows() {
    //   console.log("鼠标进入表格行的回调函数");
    // },
    // // 鼠标移出
    // leaveSelectionRows() {
    //   console.log("鼠标离开表格行的回调函数");
    // },
    treeGanttRefClick() {
      this.$refs.treeGanttRef.changeMainMove(
        this.$refs.tableRef.bodyWrapper.scrollTop
      );
    },
    onSelect(record) {
      console.log(record);
      this.selectedRow = record;
    },
    onSelectChange(selectedRowKeys) {
      console.log(selectedRowKeys);
      this.selectedRowKeys = selectedRowKeys;
    },

    tableUnExplan() {
      //   this.defaultExpandAllRows = false;
      //   this.expandedRowKeys = [];
      //   this.expandIds = ''
      //   if (this.explanTxt == '一键收起') {
      //     this.defaultExpandAllRows = false;
      //     this.expandedRowKeys = [];
      //     this.explanTxt = '一键展开';
      //   } else {
      //     this.explanTxt = '一键收起';
      //     this.defaultExpandAllRows = true;
      //     const id = this.importId;
      //     if (id) {
      //       this.explanAllTree(id);
      //     }
      //   }
      this.getList();
    },
    tableExplan() {
      // this.defaultExpandAllRows = true;
      //   const id = '10';
      if (this.routingDataId) {
        // this.explanAllTree();
        this.unfoldFn();
      }
    },
    // 展开的甘特图方法
    explanGanttAllTree() {
      let list = JSON.parse(JSON.stringify(this.tableList));

      let that = this;
      let ids = [];
      function setGantt(arr) {
        arr.forEach((item) => {
          for (const key in item) {
            if (key !== "fulfillmentId" && key !== "children") {
              delete item[key];
            }
          }

          let _arr = that.ganttList.filter((n) => {
            return n.processId == item["fulfillmentId"];
          });
          ids.push(item["fulfillmentId"]);

          item.gantt = _arr;
          if (item.children && item.children.length > 0) {
            // console.log(item.children,'数据**************')
            setGantt(item.children);
          }
        });
      }
      console.log("this.defaultExpandAllRows", this.defaultExpandAllRows);
      if (this.defaultExpandAllRows) {
        setTimeout(() => {
          this.expandIds = ids.join(",");
          console.log("this.expandIds", this.expandIds);
        }, 50);
      }
      setGantt(list);

      this.ganttInfo = { children: list };
    },
    dataSourceTree(dataSource, newDataSource) {
      //  console.log('datasource-----------',dataSource,this.state.treeNodeId,'newDAta--------------',newDataSource);
      if (dataSource && dataSource.length > 0) {
        return dataSource.map((item) => {
          if (item.id == this.treeNodeId) {
            item.children =
              newDataSource && newDataSource.length > 0 ? newDataSource : [];
          } else if (item.supplyTypeStr == "制造订单供应" && !item.children) {
            item.children = [];
          }
          return item.children && item.children.length > 0
            ? this.dataSourceTree(item.children, newDataSource)
            : "";
        });
      }
    },
    getList(dataId) {
      this.routingDataId = dataId ? dataId : this.routingDataId;
      this.loading = true;
      let params = {
        customerOrderId: this.routingDataId,
      };
      orderSupplyChainData(params)
        .then((res) => {
          this.tableList = [];
          this.loading = false;
          const { success } = res || {};
          const { list, gantt, dates } = res.data || {};
          if (success) {
            if (list && list.length > 0) {
              list.forEach((element) => {
                const { children } = element || {};
                if (!children) {
                  element.children = [];
                }
              });
              this.tableList = list;
              this.selectItem = list[0];
              if (gantt) {
                this.ganttList = gantt.processes;
              }
              if (dates && dates.length > 0) {
                this.dates = dates;
              }
              console.log(this.tableList, "this.tableList");
              console.log(this.ganttList, "this.ganttList");
              console.log(this.dates, "this.dates");
              this.dataGanttTree();
            }
          } else {
            this.$message.error("获取信息失败");
          }
        })
        .catch(() => {
          this.$message.error("获取信息失败");
          this.loading = false;
        });
    },
    // 树结构扁平化

    // 一键展开
    unfoldFn(dataId) {
      this.routingDataId = dataId ? dataId : this.routingDataId;
      this.loading = true;
      let params = {
        customerOrderId: this.routingDataId,
      };
      getAllUnfoldGantt(params)
        .then((res) => {
          this.loading = false;
          const { success } = res || {};
          const { list, gantt, dates } = res.data || {};
          if (success) {
            if (list && list.length > 0) {
              list.forEach((element) => {
                const { children } = element || {};
                if (!children) {
                  element.children = [];
                }
              });

              function treeDatafn(source) {
                let res = [];
                source.forEach((el) => {
                  res.push(el);
                  el.children && res.push(...treeDatafn(el.children));
                });
                return res;
              }
              this.treeData = [];
              this.treeData = treeDatafn(list);
              console.log("数据扁平化", this.treeData);
              this.tableList = list;

              this.selectItem = list[0];
              if (gantt) {
                this.ganttList = gantt.processes;
              }
              if (dates && dates.length > 0) {
                this.dates = dates;
              }
              console.log(this.tableList, "this.tableList");
              console.log(this.ganttList, "this.ganttList");
              console.log(this.dates, "this.dates");
              this.upDataGanttTree();
            }
          } else {
            this.$message.error("获取信息失败");
          }
        })
        .catch(() => {
          //   this.$message.error("获取信息失败");
          this.loading = false;
        });
    },
    // 展开
    upDataGanttTree() {
      let list = JSON.parse(JSON.stringify(this.treeData));
      this.listData = [];
      let that = this;
      // 全部展开的数据
      this.upIds = [];
      let ids = [];
      function setGantt(arr) {
        arr.forEach((item) => {
          for (const key in item) {
            if (key !== "fulfillmentId" && key !== "children") {
              delete item[key];
            }
          }
          let _arr = that.ganttList.filter((n) => {
            return n.processId == item["fulfillmentId"];
          });
          ids.push(item["fulfillmentId"]);
          item.gantt = _arr;
          if (item.children && item.children.length > 0) {
            // console.log(item.children,'数据**************')
            setGantt(item.children);
          }
        });
      }
      if (this.defaultExpandAllRows) {
        setTimeout(() => {
          this.upIds = ids;
          this.expandIds = ids.join(",");
          console.log("this.expandIds", this.expandIds);
        }, 50);
      }
      setGantt(list);

      this.ganttInfo = { children: list };
    },
    dataGanttTree() {
      let list = JSON.parse(JSON.stringify(this.tableList));
      let that = this;
      let ids = [];
      function setGantt(arr) {
        arr.forEach((item) => {
          for (const key in item) {
            if (key !== "fulfillmentId" && key !== "children") {
              delete item[key];
            }
          }
          let _arr = that.ganttList.filter((n) => {
            return n.processId == item["fulfillmentId"];
          });
          ids.push(item["fulfillmentId"]);
          item.gantt = _arr;
          if (item.children && item.children.length > 0) {
            // console.log(item.children,'数据**************')
            setGantt(item.children);
          }
        });
      }
      console.log("this.defaultExpandAllRows", this.defaultExpandAllRows);
      if (this.defaultExpandAllRows) {
        setTimeout(() => {
            this.upIds = ids;
          this.expandIds = ids.join(",");
          console.log("this.expandIds", this.expandIds);
        }, 50);
      }
      setGantt(list);
      console.log(list, "数据**************list");

      this.ganttInfo = { children: list };
    },
  },
};
</script>

<style lang="scss" scoped>
.toolBar {
  position: relative;
  height: 30px;
  .toolBarButton {
    position: absolute;
    right: 10px;
  }
}
.table-gantt {
  display: flex;
  border: 1px solid #ddd;
  height: calc(100% - 30px);
  .tableBox {
    width: 55%;
    height: 100%;
  }
  .dom-table {
    width: 100%;
    height: 100%;
  }
  .dom-gantt {
    width: 45%;
    height: 100%;
  }
}
</style>
