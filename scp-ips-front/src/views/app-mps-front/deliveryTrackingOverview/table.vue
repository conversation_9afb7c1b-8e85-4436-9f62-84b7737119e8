<template>
  <div class="deliveryTrackingOverview" style="height: 100%" v-loading="loading">
    <el-dialog
      title="交付详情"
      width="758px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
    >
      <el-table :data="dialogData" border size="mini" height="300px">
        <el-table-column prop="productCode" label="本厂编码" width="150"></el-table-column>
        <el-table-column prop="combineTime" label="要求开始~结束时间" width="127"></el-table-column>
        <el-table-column prop="totalDelivery" label="总交付数量" width="100"></el-table-column>
        <el-table-column prop="actualOutput" label="实际产量" width="80"></el-table-column>
        <el-table-column prop="reportingQuantity" label="报工数量" width="80"></el-table-column>
        <el-table-column prop="reportingTime" label="最后一次报工时间" width="160"></el-table-column>
      </el-table>
    </el-dialog>
    <div class="deliveryTrackingOverview-title">交 付 跟 踪 总 览</div>
    <el-form :inline="true" :model="formInline" class="deliveryTrackingOverview-inline">
      <el-form-item label="发货日期:">
        <el-date-picker
          v-model="formInline.deliveryTime"
          type="date"
          size="mini"
          clearable
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :placeholder="$t('placeholderSelect')">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="产品编码:">
        <el-autocomplete
          size="mini"
          style="width: 100%"
          clearable
          v-model="formInline.productCode"
          :fetch-suggestions="querySearch"
          :placeholder="$t('placeholderSelect')"
          :trigger-on-focus="false"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="生产组织:">
        <el-select size="mini" clearable v-model="formInline.stockPointCode" :placeholder="$t('placeholderSelect')">
          <el-option
            v-for="(item, index) in stockPointCodeList"
            :key="index"
            :label="item.label"
            :value="item.label"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工序:">
        <el-select size="mini" clearable v-model="formInline.standardOperation" :placeholder="$t('placeholderSelect')">
          <el-option
            v-for="(item, index) in standardOperationList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select size="mini" multiple collapse-tags clearable v-model="formInline.status" :placeholder="$t('placeholderSelect')">
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="getTableData">查询</el-button>
      </el-form-item>
      <el-form-item>
        <Auth url="/mps/deliveryDynamicTracking/unPublish">
          <div style="display: inline-block" slot="toolBar">
            <el-button size="mini" @click="getSelectedItems" :loading="cancelLoading">取消紧急交付</el-button>
          </div>
        </Auth>
      </el-form-item>
    </el-form>
    <div class="node-text">节点明细：</div>
    <div style="height: calc(100% - 150px);overflow: auto;">
      <el-collapse v-model="activeNames">
        <el-collapse-item v-for="(item, index) in tableData" :title="item.node" :key="index" :name="index">
          <el-table
            ref="table"
            :data="item.deliveryTrackingResultVOS"
            border
            :span-method="objectSpanMethod"
            :cell-style="changeCellStyle"
            size="mini"
            :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
            @cell-dblclick="handleCellDblClick"
            style="width: 100%">
            <!-- <el-table-column
              label="序号"
              type="index"
              width="50">
            </el-table-column> -->
            <el-table-column
              type="selection"
              width="55"
            ></el-table-column>
            <el-table-column
              label="序号"
              width="50">
              <template slot-scope="scope">
                <span>{{ (scope.$index + 2) / 2 }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-for="(itemA, indexA) in columnList"
              :key="indexA"
              :prop="itemA.prop"
              :label="itemA.label"
              :width="itemA.width"
              align="center">
            </el-table-column>
            <el-table-column
              v-for="(itemB, indexB) in item.dynamicHeader"
              :key="indexB"
              :prop="itemB"
              :label="itemB"
              width="127"
              align="center">
              <template slot-scope="scope">
                <div>{{ getTableValue(scope.row['dynamicDeliveryTrackingBody'], itemB, scope.$index, scope.row) }}</div>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>
<script>
import baseUrl from "@/utils/baseUrl";
import { dropdownEnum } from "@/api/mpsApi/dropdown";
import { orgOption,operationStep,masterPlanProductDropDown,deliveryTrackingView,doUnPublish,getDetailApi } from "@/api/mpsApi/deliveryTrackingOverview/index";
export default {
  name: "deliveryTrackingOverview",
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      dialogVisible: false,
      dialogData: [],
      activeNames: [0],
      columnList: [
        {label:"车型",prop:"vehicleModelCode",width:"120",},
        {label:"本厂编码",prop:"productCode",width:"140",},
        {label:"待进仓量",prop:"warehouseNumber",width:"120",},
        {label:"状态",prop:"trackingStatus",width:"120",},
      ],
      formInline: {
        deliveryTime: undefined,
        productCode: '',
        stockPointCode: '',
        standardOperation: '',
        status: ['PUBLISHED'],
      },
      productCodeList: [],
      stockPointCodeList: [],
      standardOperationList: [],
      statusList: [],
      tableData: [],
      total: 0,
      loading: false,
      cancelLoading: false,
    };
  },
  created() {
    this.orgOption();
    this.operationStep();
    this.masterPlanProductDropDown();
    this.dropdownEnum();
    this.getTableData();
  },
  methods: {
    async handleCellDblClick(row, column, cell, event) {
      if(!column?.label?.includes('时间')) return;

      let taskId = row[column.property]?.taskId;

      if(!taskId || taskId == -1) {
        this.$message.warning('无详情');
        return;
      }
      let params = {
        taskId: taskId,
        limitFlag: false
      };
      let res = await getDetailApi(params);

      if(res.success) {

        let data = res.data?.map(data => {
          let startTime = '--', endTime = '--';
          try {
            startTime = data.startTime ? moment(data.startTime).format('YYYY-MM-DD HH:mm') : '--';
          }catch (e) {

          }
          try {
            endTime = data.endTime ? moment(data.endTime).format('YYYY-MM-DD HH:mm') : '--';
          }catch (e) {

          }
          // try {
          //   lastReportTime = row.trackingStatus === '执行中' ||  row.trackingStatus === '已完成' ?
          //     data.modifyTime ? moment(data.modifyTime).format('YYYY-MM-DD HH:mm') : '--'
          //     : '--';
          // }catch (e) {

          // }
          let combineTime = `${startTime}~${endTime}`;

          return {
            productCode: data.productCode,
            combineTime,
            totalDelivery: data.plannedQuantity || 0,
            actualOutput: data.finishedQuantity || 0,
            reportingQuantity: data.reportingQuantity,
            reportingTime: data.reportingTime ? moment(data.reportingTime).format('YYYY-MM-DD HH:mm') : ''
          }
        }) || [];
        this.dialogData = data;
        this.dialogVisible = true;
      }

    },
    async getSelectedItems() {
      this.cancelLoading = true;
      const allSelected = [];
      if (this.$refs.table) {
        this.$refs.table.forEach(table => {
          if (table.selection) {
            allSelected.push(...table.selection);
          }
        });
      }
      if(allSelected.length === 0) {
        this.$message.warning('请选择数据');
        this.cancelLoading = false;
        return;
      }
      let ids = allSelected.map(item => item.trackingId);
      let res = await doUnPublish(ids);
      if(res.success) {
        this.$message.success('取消成功');
        this.getTableData();
      }
      this.cancelLoading = false;
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 6) {
        if (rowIndex % 2 === 0) {
          return {
            rowspan: 2,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    changeCellStyle({row, column, rowIndex, columnIndex}) {
      if (column?.label?.includes('时间')) {
        if (rowIndex % 2 == 1) {
          let obj = row.dynamicDeliveryTrackingBody.find(n => n.operationName == column.label)
          if (obj) {
            if (obj.color == 'red' || obj.color == 'green') {
              return 'background-color:' + obj.color + ";font-size: 13px;font-weight: bold;color:#fff";
            }
            return 'background-color:' + obj.color + ";font-size: 13px;font-weight: bold;"
          } else {
            return "font-size: 13px;font-weight: bold;";
          }
        }
      } else {
        return '';
      }
    },
    getCellBg(arr , t) {
      let obj = arr.find(item => item.operationName === t)
      if (obj) {
        return obj.color
      }
      return obj
    },
    getTableValue(arr , t, index, row) {
      let obj = arr.find(item => item.operationName === t)
      if (obj) {
        row[t] = obj;
        if (index % 2) {
          return obj.count
        } else {
          return obj.productionTime
        }
      }
      return obj
    },
    orgOption() {
      orgOption()
      .then((res) => {
        if (res.success) {
          this.stockPointCodeList = res.data
        }
      })
      .catch((error) => {
        console.log(error);
      });
    },
    operationStep() {
      operationStep()
      .then((res) => {
        if (res.success) {
          this.standardOperationList = res.data
        }
      })
      .catch((error) => {
        console.log(error);
      });
    },
    masterPlanProductDropDown() {
      masterPlanProductDropDown()
      .then((res) => {
        if (res.success) {
          this.productCodeList = res.data
        }
      })
      .catch((error) => {
        console.log(error);
      });
    },
    getTableData() {
      this.loading = true;
      deliveryTrackingView(this.formInline)
        .then((res) => {
          this.loading = false;
          if (res.success) {
            res.data.forEach(m => {
              let arr = [];
              m.deliveryTrackingResultVOS.forEach(item => {
                arr.push(item, item)
              })
              m.deliveryTrackingResultVOS = arr;
            })
            this.tableData = res.data;
            if (res.data.length === 0) {
              this.$message.warning("暂无数据！");
            }
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    //获取枚举值
    dropdownEnum() {
      dropdownEnum({ enumKeys: 'com.yhl.scp.mps.enums.TrackingStatusEnum' }).then((response) => {
        if (response.success) {
          this.statusList = response.data['com.yhl.scp.mps.enums.TrackingStatusEnum'];
        }
      });
    },
    querySearch(queryString, cb) {
      let result = queryString ? this.productCodeList.filter(this.createFilter(queryString)) : this.productCodeList;
      cb(result);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1);
      };
    },
  },
};
</script>
<style lang="scss"  scoped>
.deliveryTrackingOverview-title {
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
}
.deliveryTrackingOverview-inline {
  .el-form-item {
    margin-bottom: 2px;
  }
}
.border_bottom {
  position: relative;
}
.border_bottom::before {
  position: absolute;
  content: '';
  bottom: 0;
  left: -10px;
  width: calc(100% + 20px);
  height: 0;
  border-bottom: 1px solid #EBEEF5;
}
.node-text {
  margin: 5px 0 10px 0;
  font-size: 13px;
  font-weight: bold;
}
</style>
<style>
.deliveryTrackingOverview .el-table .el-table__body-wrapper .el-table__cell {
  padding: 3px 0;
}
</style>
