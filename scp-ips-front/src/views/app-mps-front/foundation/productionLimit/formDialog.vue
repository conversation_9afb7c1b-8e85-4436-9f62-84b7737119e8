<template>
  <div style="display:inline-block">
    <Auth url="/mps/foundation/productionLimit/btn1">
      <div style="display: inline-block" slot="toolBar">
        <el-button size="medium" icon="el-icon-circle-plus-outline" v-debounce="[addForm]">{{$t('addText')}}</el-button>
      </div>
    </Auth>
    <Auth url="/mps/foundation/productionLimit/btn2">
      <div style="display: inline-block" slot="toolBar">
        <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">{{$t('editText')}}</el-button>
      </div>
    </Auth>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row>
          <!-- <el-col :span="11" >
            <el-form-item :label="$t('productionLimit_companyCode')" prop="companyCode">
              <el-input size="small" v-model="ruleForm.companyCode" :placeholder="$t('placeholderInput')" disabled></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="11" >
            <el-form-item :label="$t('productionLimit_productType')" prop="productType">
              <el-select
                style="width: 100%"
                v-model="ruleForm.productType"
                size="small"
                clearable
                filterable
                @change="getProductType"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in ProductTypeEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="$t('productionLimit_factorValue')" prop="factorValue">
              <el-select
                style="width: 100%"
                v-model="ruleForm.factorValue"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in FactorValueEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11" >
            <el-form-item :label="$t('productionLimit_days')" prop="days">
              <el-input-number size="small" :min="0" v-model="ruleForm.days" :placeholder="$t('placeholderInput')"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="$t('productionLimit_mainOperationProductionCapacity')" prop="mainOperationProductionCapacity">
              <el-input-number size="small" :min="0" v-model="ruleForm.mainOperationProductionCapacity" :placeholder="$t('placeholderInput')"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="11" >
            <el-form-item :label="$t('productionLimit_startTime')" prop="startTime">
              <el-date-picker
                v-model="ruleForm.startTime"
                type="datetime"
                :placeholder="$t('placeholderInput')">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="$t('productionLimit_endTime')" prop="endTime">
              <el-date-picker
                v-model="ruleForm.endTime"
                type="datetime"
                :placeholder="$t('placeholderInput')">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {productionLimitCreate, productionLimitUpdate, getCompanyCode} from "@/api/mpsApi/foundation/productionLimit";
import { dropdownByCollectionCode, dropdownXByCollectionCode } from "@/api/mpsApi/dropdown";
import moment from 'moment';
export default {
  name: 'productionLimit',
  components: {
  },
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
    enums: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      ruleForm: {
        productType: '',
        factorValue: '',
      },
      rules: {
        companyCode: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        productType: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        factorValue: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        days: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        mainOperationProductionCapacity: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        startTime: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }]
      },
      ProductTypeEnum:[],
      FactorValueEnum:[],
      companyCode:''
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        // this.enums.forEach((item) => {
        //   if (item.key == "PRODUCT_TYPE") {
        //     this.ProductTypeEnum = item.values;
        //   }
        //   if (item.key == "ElEMENT_VALUE") {
        //     this.FactorValueEnum = item.values;
        //   }
        // });
      }
    }
  },
  mounted() {
    this.dropdownXByCollectionCode();
  },
  created() {
    this.companyCode=localStorage.getItem("companyCode")
  },

  methods: {
    dropdownXByCollectionCode() {
      dropdownXByCollectionCode({
        collectionCode: 'PRODUCT_TYPE',
        recursiveTimes: 1,
        includePrevious: false,
      }).then((response) => {
        if (response.success) {
          this.ProductTypeEnum = response.data;
        }
      });
    },
    getProductType(e, t) {
      if (!t) {
        this.ruleForm.factorValue = ''
      }
      if (!e) {
        this.FactorValueEnum = []
        return
      }
      dropdownXByCollectionCode({
        collectionCode: e,
        recursiveTimes: 2,
        includePrevious: false,
      }).then((response) => {
        if (response.success) {
          this.FactorValueEnum = response.data;
        }
      });
    },
    addForm() {
      this.ruleForm.companyCode=this.companyCode
      this.dialogVisible = true
      this.title = this.$t('addText')
    },
    editForm() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      this.dialogVisible = true
      this.title = this.$t('editText')
      const info = this.rowInfo

      if (info.id) {
        this.ruleForm = {
          ...info
        }
        if (info.productType) {
          this.getProductType(info.productType, true)
        }
        this.ruleForm.enabled = 'YES'
        this.ruleForm.startTime = moment(this.ruleForm.startTime).valueOf()
        this.ruleForm.endTime = moment(this.ruleForm.endTime).valueOf()

      }
    },
    handleClose () {
      this.dialogVisible = false
      this.ruleForm = {
        productType: '',
        factorValue: '',
      }
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if(this.ruleForm.endTime!==null&this.ruleForm.endTime!==undefined){
            if(this.ruleForm.startTime>this.ruleForm.endTime){
            this.$message.error(this.$t('productionLimitDateError'))
            return
            }
          }

          let form = JSON.parse(JSON.stringify(this.ruleForm))
          form.enabled = 'YES'
          if (this.title == this.$t('addText')) {
            productionLimitCreate(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('addSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('addFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('addFailed'))
              })
            return
          }
          if (this.title == this.$t('editText')) {
            form.id = this.rowInfo.id
            productionLimitUpdate(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('editSucceeded'))
                  this.$parent.SelectionChange([])
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('editFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('editFailed'))
              })
            return
          }
        } else {
          return false;
        }
      });
    },
  }
}
</script>
