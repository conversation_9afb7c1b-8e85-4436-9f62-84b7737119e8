<template>
  <div style="display:inline-block">
    <!-- <el-button size="medium" icon="el-icon-circle-plus-outline" @click="addForm">{{$t('addText')}}</el-button> -->
    <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">{{$t('editText')}}</el-button>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row>
          <!-- <el-col :span="11">
            <el-form-item label="组织" prop="stockPointCode">
              <el-select
                style="width: 100%"
                v-model="ruleForm.stockPointCode"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in stockPointList"
                  :key="item.stockPointCode"
                  :label="item.stockPointName+'('+item.stockPointCode+')'"
                  :value="item.stockPointCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="11">
            <el-form-item label="组织">
              <div class="w100">{{ ruleForm.stockPointCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="组织名称">
              <div class="w100">{{ ruleForm.stockPointName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="产品编码">
              <div class="w100">{{ ruleForm.productCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="客户零件号">
              <div class="w100">{{ ruleForm.partName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="工序">
              <div class="w100">{{ ruleForm.operationCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="工序名称">
              <div class="w100">{{ ruleForm.operationName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="产线">
              <div class="w100">{{ ruleForm.resourceCode }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="产线描述">
              <div class="w100">{{ ruleForm.resourceName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="产线类型">
              <div class="w100">{{ ruleForm.resourceType }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="资源优先级" prop="priority">
              <el-input-number size="small" v-model="ruleForm.priority" :placeholder="$t('placeholderInput')" :min="0" :max="10000" :step="1" :precision="0"></el-input-number>
            </el-form-item>
          </el-col>          
          <el-col :span="11">
            <el-form-item label="生产线组">
              <div class="w100">{{ ruleForm.lineGroup }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="生效时间" prop="effectiveTime">
              <el-date-picker
                v-model="ruleForm.effectiveTime"
                type="datetime"
                :placeholder="$t('placeholderInput')">
              </el-date-picker>
            </el-form-item>
          </el-col>    
          <el-col :span="11">
            <el-form-item label="是否有效" prop="enabled">
              <el-switch size="small" v-model="ruleForm.enabled" disabled></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {createApi, updateApi} from "@/api/mpsApi/foundation/resourceRelations";
import {fetchListMds} from "@/api/mpsApi/componentCommon";
// import {dropdownEnum} from "@/api/mpsApi/demandPriority";
import moment from 'moment';
export default {
  // name: 'productionLeadTime',
  name: '',
  components: {
  },
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      ruleForm: {
      },
      rules: {
        stockPointCode: [{ required: false, message: this.$t('placeholderInput'), trigger: 'change' }],
      },
      stockPointList: [],
      // ProductionLeadTimeEnum: [],
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.getStockPoint();
        // dropdownEnum({enumKeys: 'com.yhl.scp.mps.productionLeadTime.enums.ProductionLeadTimeEnum'}).then(res => {
        //   this.ProductionLeadTimeEnum = res.data['com.yhl.scp.mps.productionLeadTime.enums.ProductionLeadTimeEnum']
        // })
      }
    }
  },
  mounted() {
  },
  methods: {
    getStockPoint() {
      const params = {
        pageNum: 1,
        pageSize: 10000,
        queryCriteriaParam: "",
        sortParam: "",
      };
      const url = `newStockPoint/page`;
      const method = "get";
      this.loading = true;
      fetchListMds(params, url, method, this.componentKey)
        .then((response) => {
          if (response.success) {
            this.stockPointList = response.data.list;
          } else {
            this.stockPointList = [];
          }
        })
        .catch((error) => {
          console.log("分页查询异常", error);
        });
    },

    addForm() {
      this.dialogVisible = true
      this.title = this.$t('addText')
      this.ruleForm.enabled = true
    },
    editForm() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      this.dialogVisible = true
      this.title = this.$t('editText')
      const info = this.rowInfo
      if (info.id) {
        this.ruleForm = {...info}
        this.ruleForm.enabled = this.ruleForm.enabled=='YES'
        this.ruleForm.effectiveTime = moment(this.ruleForm.effectiveTime)
      }
    },
    handleClose () {
      this.dialogVisible = false
      this.ruleForm = {
      }
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          this.removeNoKey(form);
          form.enabled = form.enabled ? 'YES' : "NO"
          // form.effectiveTime = moment(form.effectiveTime).format('YYYY-MM-DD HH:mm:ss')
          form.effectiveTime = moment(form.effectiveTime).valueOf()
          if (this.title == this.$t('addText')) {
            createApi(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('addSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('addFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('addFailed'))
              })
            return
          }
          if (this.title == this.$t('editText')) {
            form.id = this.rowInfo.id
            updateApi(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('editSucceeded'))
                  this.$parent.SelectionChange([])
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('editFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('editFailed'))
              })
            return
          }
        } else {
          return false;
        }
      });
    },
    removeNoKey(obj) {
      for (let key in obj) {
        if (!key) {
          delete obj[key];
        }
      }
    }
  }
}
</script>
