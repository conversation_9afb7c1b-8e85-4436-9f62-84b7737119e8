<template>
  <div id="lowCode">
    <yhl-lcdp
      ref="lcdp"
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectMps"
      :sysElements="this.getSysElements"
      @customPageResize="customPageResize"
      @loaderComponent="loaderComponent"
    >
    </yhl-lcdp>
  </div>
</template>
<script>
export default {
  name: "lowCode",
  data() {
    return {
      componentKey: "",
      customContainers: [],
    };
  },
  created() {
    this.initParams();
  },
  methods: {
    initParams() {
      let key = this.$route.path.toUpperCase().replace(/\//g, "_");
      this.componentKey = key;
    },
    // 自定义页面自动查询方法
    customPageQuery(item, layoutSetConfig) {},
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig(item) {},
    customPageResize(item) {
      // this.$refs[item.id].handleResize()
    },
    loaderComponent(router, id) {
      Promise.resolve(require('@/' + router).default)
        .then(data => {
          this.$refs.lcdp.setSysObjComponent(data, id)
        })
    },
  },
};
</script>
<style scoped>
#lowCode {
  width: 100%;
  height: calc(100vh - 80px);
}
</style>
