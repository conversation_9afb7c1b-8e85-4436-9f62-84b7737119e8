<template>
  <div style="height: 400px" class="deliveryDackingDetail" v-loading="loading">
    <div class="top-title">
      详情信息
      <el-button
        style="float: right; margin-top: -3px !important"
        size="medium"
        @click="getJudgeByOrderStatus(1)"
        :loading="loading0"
      >
        保 存
      </el-button>
      <el-button
        style="float: right; margin-top: -3px !important"
        size="medium"
        :loading="loading1"
        @click="getJudgeByOrderStatus(2)"
      >
        删 除
      </el-button>
      <el-button
        style="float: right; margin-top: -3px !important"
        size="medium"
        @click="getJudgeByOrderStatus(3)"
        >{{ $t("addText") }}
      </el-button>
    </div>
    <el-table
      :data="tableData"
      row-key="id"
      @selection-change="handleSelectionChange"
      size="mini"
      border
      ref="multipleTable"
      height="calc(100% - 40px)"
      :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
      style="width: 100%; margin-top: 5px"
      :key="tableKey"
    >
      <el-table-column type="index" width="50" fixed="left"> </el-table-column>
      <el-table-column type="selection" width="50" fixed="left">
      </el-table-column>
      <el-table-column
        v-for="(item, index) in columnList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
      >
        <template #header>
          <span>
            <span v-if="item.required" style="color: red">*</span>
            {{ item.label }}
          </span>
        </template>
        <template slot-scope="scope">
          <el-input
            :class="item.required ? 'isrequired' : ''"
            size="mini"
            clearable
            v-model="scope.row[item.prop]"
            v-if="item.edit && item.editType === 'input'"
            @change="getBoxQuantity(scope.row, item.prop, scope.$index)"
            @focus="handlefocus"
            @input="handleInput(scope.row, item.prop)"
            :placeholder="$t('placeholderInput')"
          ></el-input>

          <!-- 动态获取 物料器具 -->
          <el-select
            v-else-if="
              item.edit &&
              item.editType === 'select' &&
              item.prop === 'materialEquipment'
            "
            :class="item.required ? 'isrequired' : ''"
            size="mini"
            filterable
            clearable
            v-model="scope.row.productBoxRelationId"
            @change="onMaterialEquipmentChange(scope.row, item.prop, scope.$index)"
          >
            <el-option
              v-for="item in materialEquipmentOption[scope.row.productCode]"
              :key="item.value3"
              :label="item.value1"
              :value="item.value3"
            >
            </el-option>
          </el-select>

          <el-select
            :class="item.required ? 'isrequired' : ''"
            size="mini"
            filterable
            clearable
            v-model="scope.row[item.prop]"
            v-else-if="item.edit && item.editType === 'select'"
          >
            <el-option
              v-for="item in optionObj[item.prop + 'Option']"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <SelectVirtual
            v-model="scope.row[item.prop]"
            v-else-if="item.edit && item.editType === 'selectVirtual'"
            style="width: 100%"
            :selectConfig="selectConfig"
            size="mini"
            placeholder="请选择"
            clearable
            @change="getBoxQuantity(scope.row, 'productCode', scope.$index)"
          ></SelectVirtual>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import {
  compare,
  deleteDetailApi,
  getBoxQuantity,
  getBoxType,
  getBoxTypeDropDown,
  getProductCodeByOemCodeToVehicleCode,
  saveForm,
  selectDockingOrderDetail,
  getStatusByDeliveryDockingNumber,
  checkTallyOrderMode
} from "@/api/dfpApi/deliveryPlan/deliveryPlan";
import { getByCollectionCode } from "@/api/dfpApi/dropdown";
import SelectVirtual from "@/components/selectVirtual/index";
import moment from "moment";
export default {
  name: "promotionCalendarDetail",
  components: {
    SelectVirtual,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    deliveryDockingNumber: { type: Object, default: "" },
    oemCode: { type: Array, default: () => [] },
    deliveryTime: { type: Object, default: "" },
    isAdd: { type: Boolean, default: false },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      columnList: [
        {
          prop: "productCode",
          label: "产品编码",
          width: "200px",
          edit: true,
          required: true,
          editType: "selectVirtual", // select input date
        },
        {
          prop: "deliveryQuantity",
          label: "发货数量",
          width: "120px",
          edit: true,
          required: true,
          editType: "input", // select input date
          isInteger: true, // 添加整数标识
        },
        {
          prop: "mustQuantity",
          label: "必发数量",
          width: "120px",
          required: true,
          edit: true,
          editType: "input", // select input date
          isInteger: true,
        },
        {
          prop: "materialEquipment",
          label: "物料器具",
          required: true,
          width: "120px",
          edit: true,
          editType: "select", // select input date
        },
        {
          prop: "boxNumber",
          label: "箱数",
          width: "120px",
          edit: true,
          required: true,
          editType: "input",
          isInteger: true,
        },
        {
          prop: "deliveryTime",
          label: "发货时间",
          width: "174px",
          required: true,
          // edit: true,
          // editType: 'date'  // select input date
        },
        {
          prop: "grossWeight",
          label: "毛重",
          width: "120px",
          edit: true,
          editType: "input", // select input date
        },
        {
          prop: "volume",
          label: "体积",
          width: "120px",
          edit: true,
          editType: "input", // select input date
        },
        {
          prop: "cabinetType",
          label: "柜型",
          width: "120px",
          edit: true,
          editType: "input", // select input date
        },
        {
          prop: "billLadingNumber",
          label: "提单号",
          width: "120px",
          edit: true,
          editType: "input", // select input date
        },
        {
          prop: "invoiceNumber",
          label: "发票号",
          width: "120px",
          edit: true,
          editType: "input", // select input date
        },
        {
          prop: "remark",
          label: "备注",
          width: "120px",
          edit: true,
          editType: "input", // select input date
        },
        {
          prop: "actualDeliveryQuantity",
          label: "实际发货数量",
          width: "120px",
        },
        {
          prop: "actualDeliveryBoxQuantity",
          label: "实际发货箱数",
          width: "120px",
        },
      ],
      loading: false,
      tableData: [],
      optionObj: {
        productCodeOption: [],
      },
      materialEquipmentOption: {},
      selectedRows: [],
      selectedRowKeys: [],
      selectConfig: {
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      detailId: "",
      tableKey: 0,
      loading0: false,
      loading1: false,
      saveIds:[]
    };
  },
  watch: {
    oemCode() {
      getProductCodeByOemCodeToVehicleCode({
        oemCode: this.oemCode?.join(","),
      }).then((res) => {
        if (res.success && res.data.length) {
          this.selectConfig.data = res.data.map((item) => {
            return {
              label: item.value + "(" + item.label + ")",
              value: item.value,
            };
          });
        }
      });
    },
    deliveryTime(newVal) {
      this.tableData = this.tableData.map((n) => {
        n.deliveryTime = newVal
          ? moment(newVal).format("YYYY-MM-DD HH:mm")
          : "";
        return n;
      });
    },
    deliveryDockingNumber() {
      this.selectDockingOrderDetail();
    },
    tableData: {
      handler(newVal) {
        const productData = newVal.map(item => ({
          deliveryDockingLineNumber: item.deliveryDockingLineNumber,
          productCode: item.productCode,
          deliveryQuantity: item.deliveryQuantity
        })).filter(item => item.productCode && item.deliveryQuantity);
        this.$emit('updateProductData', productData);
      },
      deep: true
    },
  },
  activated() {
    if (this.$route.query && this.$route.query.id) {
      this.detailId = this.$route.query.id;
      // this.selectDockingOrderDetail()
    }
  },
  created() {
    this.initData();
    if (this.$route.query && this.$route.query.id) {
      this.detailId = this.$route.query.id;
      // this.selectDockingOrderDetail()
    }
  },
  methods: {
    initData() {
      // getProductList
      // getByCollectionCode({ collection: 'BOX_TYPE' }).then((res) => {
      //   if (res.success) {
      //     this.optionObj.materialEquipmentOption = res.data
      //   }
      // })
      // getByCollectionCode({ collection: 'CABINET_TYPE' }).then((res) => {
      //   if (res.success) {
      //     this.optionObj.cabinetTypeOption = res.data
      //   }
      // })
    },
    handlefocus() {
      this.selectedRows = [];
      this.$refs.multipleTable.clearSelection();
    },

    getBoxTypeDropDown() {
      let arr = [];
      this.tableData.map((m) => {
        if (!arr.includes(m.productCode)) {
          arr.push(m.productCode);
        }
      });
      if (arr.length) {
        getBoxTypeDropDown({ productCode: arr.join(",") }).then((data) => {
          if (data.success && data.data) {
            // let index = this.tableData.findIndex(n => n.id === res.id)
            // this.tableData[index].materialEquipment = data.data
            // this.optionObj.materialEquipmentOption = data.data
            for (const key in data.data) {
              this.$set(this.materialEquipmentOption, key, data.data[key]);
            }
            console.log("++++++++", this.materialEquipmentOption);
            this.tableKey += 1;
          }
        });
      }
    },
    getBoxQuantity(res, t, index) {
      if (!res.productCode) {
        return;
      }
      // let productCode = this.selectConfig.data.find(n => n.value === res.productCode).label
      if (t === "productCode") {
        this.checkTallyOrderMode();
        getBoxTypeDropDown({ productCode: res.productCode }).then((data) => {
          if (data.success && data.data) {
            this.tableData[index].materialEquipment =
              data.data[res.productCode][0].value;
            // this.optionObj.materialEquipmentOption = data.data
            this.$set(
              this.materialEquipmentOption,
              res.productCode,
              data.data[res.productCode]
            );
            this.tableKey += 1;
          } else {
            this.tableData[index].materialEquipment = "";
            this.$message({
              showClose: true,
              message: data.msg || "此产品编码无对应的物料器具",
              type: "error",
              duration: 0,
            });
          }
        });
      }

      if (t === "materialEquipment" || t === "deliveryQuantity") {
        // console.log("materialEquipment-------", res, t, index);
        let code = this.tableData[index].productCode
        let deliveryQuantity = this.tableData[index].deliveryQuantity
        let value = this.tableData[index].materialEquipment
        if (!code) {
          // this.$message.warning('请先选择产品编码！')
          return
        }
        if (!deliveryQuantity) {
          // this.$message.warning('请填写发货数量！')
          return
        }
        if (!value) {
          // this.$message.warning('请先选择产品编码！')
          return
        }
        if (this.materialEquipmentOption[code]) {
          let label = this.materialEquipmentOption[code].find((n) => n.value === value).label;
          let n = label.split("&")[1].split(")")[0]
          if (!n) {
            n = 1
          }
          this.tableData[index].boxNumber = Math.ceil(Number(deliveryQuantity) / Number(n))
        }
      }

        // if (!this.oemCode.length) {
        //   this.$message.warning('请先选择主机厂！')
        //   return
        // }
        // if (!this.deliveryTime) {
        //   this.$message.warning('请先选择发货时间！')
        //   return
        // }
        // if (!res.deliveryQuantity) {
        //   let index = this.tableData.findIndex(n => n.id === res.id)
        //   this.tableData[index].boxNumber = ''
        //   return
        // }
        // let index = this.tableData.findIndex(n => n.id === res.id)
        // 校验输入的发货数量
        // let info = {
        //   deliveryQuantity: res.deliveryQuantity,
        //   productCode: res.productCode,
        //   deliveryTime: this.deliveryTime,
        //   oemCode: this.oemCode.join(',')
        // }
        // compare(info).then((res) => {
        //   if (res.success && res.data) {
        //     // this.tableData[index].deliveryQuantity = ''
        //   } else {
        //     this.tableData[index].deliveryQuantity = ''
        //     this.$message({showClose: true, message: res.msg || '发货数量输入范围不正确！', type: 'error', duration: 0});
        //   }
        //   this.tableKey += 1
        // })
        // if (!res.deliveryQuantity) {
        //   this.tableData[index].boxNumber = ''
        //   return
        // }
        // let _info = {
        //   deliveryQuantity: res.deliveryQuantity,
        //   productCode: res.productCode
        // }
        // getBoxQuantity(_info).then((data) => {
        //   let num = ''
        //   if (!data.msg && data.length < 10) {
        //     num = data
        //   } else {
        //     this.$message({showClose: true, message: data.msg || '获取箱数失败！', type: 'error', duration: 0});
        //   }
        //   this.tableData[index].boxNumber = num
        //   this.tableKey += 1
        // })
    },
    checkTallyOrderMode() {
      if (this.tableData.length > 1) {
        let code = this.tableData.map(n => {
          return n.productCode
        })
        checkTallyOrderMode({ productCode: code.join(',')}).then((data) => {
          if (!data.success) {
            this.tableData[this.tableData.length - 1].productCode = "";
            this.$message({
              showClose: true,
              message: data.msg || "选择的产品编码不匹配！",
              type: "error",
              duration: 0,
            });
          }
        });
      }
    },
    // 表格查询数据
    pageDetailApi() {
      // this.loading = true
      // pageDetailApi().then(res => {
      //   this.loading = false
      //   if (res.success && res.data && res.data.list) {
      //     this.tableData = res.data.list.map(n => {
      //       n.deliveryTime = n.deliveryTime ? moment(n.deliveryTime).format("YYYY-MM-DD") : '';
      //       return n
      //     })
      //   } else {
      //     this.tableData = []
      //   }
      // }).catch(() => {
      //   this.loading = false
      // })
    },
    selectDockingOrderDetail() {
      if (!this.deliveryDockingNumber) {
        return;
      }
      this.loading = true;
      selectDockingOrderDetail({
        deliveryDockingNumber: this.deliveryDockingNumber,
      })
        .then((res) => {
          this.loading = false;
          if (res.success && res.data) {
            this.tableData = res.data.map((n) => {
              n.deliveryTime = n.deliveryTime
                ? moment(n.deliveryTime).format("YYYY-MM-DD HH:mm")
                : "";
              return n;
            });
            this.saveIds = res.data.map(item => item.id)
            // 只有快速创建时执行，导入和保存时都不执行
            if (this.isAdd) {
              res.data.forEach((item) => {
                if (
                  item.deliveryQuantity !== undefined &&
                  item.productCode !== undefined
                ) {
                  this.getBoxQuantity(item, "deliveryQuantity");
                }
              });
              this.isAdd = false;
            }
            this.getBoxTypeDropDown();
          } else {
            this.tableData = [];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    addTable() {
      this.tableData.push({
        id: "ID" + new Date().getTime(),
        deliveryTime: this.deliveryTime || "",
        productCode: "",
        deliveryQuantity: "",
        mustQuantity: "",
        materialEquipment: "",
        boxNumber: "",
        grossWeight: "",
        volume: "",
        cabinetType: "",
        invoiceNumber: "",
        remark: "",
      });
    },
    delTable() {
      // 保存时存的id调用接口删除 否则 页面删除
      let deleteIndices = []; // 页面删除的索引
      let deleteIds = []; // 需要调用接口删除的id

      this.selectedRows.forEach((n) => {
        const index = this.tableData.findIndex(m => m.id === n.id);
        if (index !== -1) {
          if (this.saveIds.includes(n.id)) {
            deleteIds.push(n.id);
          } else {
            deleteIndices.push(index);
          }
        }
      });

      if (deleteIds.length > 0) {
        this.loading1 = true;
        deleteDetailApi(deleteIds)
          .then((res) => {
            if (res.success) {
              this.$refs.multipleTable.clearSelection();
              this.$message.success(res.msg || this.$t("deleteSucceeded"));
              // 删除tableData
              deleteIndices.sort((a, b) => b - a);
              deleteIndices.forEach((index) => {
                this.tableData.splice(index, 1);
              });
              this.selectDockingOrderDetail();
            } else {
              this.$message({
                showClose: true,
                message: res.msg || this.$t("deleteFailed"),
                type: "error",
                duration: 0,
                dangerouslyUseHTMLString: true
              });
            }
          })
          .finally(() => {
            this.loading1 = false;
          });
      } else {
        if (deleteIndices.length > 0) {
          deleteIndices.sort((a, b) => b - a);
          deleteIndices.forEach((index) => {
            this.tableData.splice(index, 1);
          });
          this.$message.success(this.$t("deleteSucceeded"));
        }
      }
    },
    saveTable() {
      const rows = this.$refs.multipleTable.tableData;
      rows.forEach((row) => {
        this.$refs.multipleTable.toggleRowSelection(row, true);
      });
      // 下表无数据时
      if (this.selectedRows.length == 0) {
        this.$message.success("保存成功");
        return;
      }
      // let info = this.selectedRows[0]
      if (!this.oemCode.length) {
        this.$message.warning("请先选择主机厂！");
        return;
      }
      if (!this.deliveryTime) {
        this.$message.warning("请先选择发货时间！");
        return;
      }
      let query = {
        oemCode: this.oemCode.join(","),
        deliveryTime: this.deliveryTime,
      };
      let infos = this.selectedRows;
      for (let i = 0; i < infos.length; i++) {
        var currentInfo = infos[i];
        if (currentInfo.deliveryQuantity != '0' && !currentInfo.deliveryQuantity) {
          this.$message.warning("发货数量还未维护！");
          return;
        }
        if (currentInfo.mustQuantity != '0' && !currentInfo.mustQuantity) {
          this.$message.warning("必发数量还未维护！");
          return;
        }
        if (!currentInfo.materialEquipment) {
          this.$message.warning("物料器具还未维护！");
          return;
        }
        if (currentInfo.boxNumber != '0' && !currentInfo.boxNumber) {
          this.$message.warning("箱数还未维护！");
          return;
        }
        currentInfo.deliveryDockingLineNumber = this.tableData.findIndex(
          (n) => n.id === currentInfo.id
        );
        currentInfo.deliveryDockingNumber = this.deliveryDockingNumber;
        infos[i] = currentInfo;
      }
      this.loading0 = true;
      saveForm(query, infos)
        .then((res) => {
          if (res.success) {
            this.saveIds = Array.from(new Set([...this.saveIds, ...infos.map(item => item.id)]));
            this.selectDockingOrderDetail();
            this.$refs.multipleTable.clearSelection();
            this.$message.success(res.msg || this.$t("editSucceeded"));
            this.$emit("refresh");
          } else {
            this.saveIds = []
            this.$message({
              showClose: true,
              message: res.msg || this.$t("editFailed"),
              type: "error",
              duration: 0,
              dangerouslyUseHTMLString: true
            });
          }
        })
        .finally(() => {
          this.loading0 = false;
        });
      // }
    },
    getJudgeByOrderStatus(code) {
      //增加判断取消或关闭时不可进行任何操作
      getStatusByDeliveryDockingNumber({
        deliveryDockingNumber: this.deliveryDockingNumber,
      }).then((res) => {
        if (res.success) {
          if (res.data[0] === "CLOSED" || res.data[0] === "CANCELLED") {
            this.$message.warning(
              "本单据已经取消或关闭，不可进行新增，删除和保存的操作。"
            );
          } else {
            if (code === 1) {
              this.saveTable();
            } else if (code === 2) {
              this.delTable();
            } else if (code === 3) {
              this.addTable();
            }
          }
        }
      });
    },
    handleSelectionChange(res) {
      this.selectedRows = res;
      this.selectedRowKeys = res.map((e) => e.id);
    },
    handleInput(row, prop) {
      const column = this.columnList.find(col => col.prop === prop);
      if (column && column.isInteger) {
        let value = row[prop].replace(/[^\d]/g, '');
        if (!value) {
          row[prop] = '';
          return;
        }
        value = parseInt(value);
        row[prop] = value.toString();
        if (!Number.isInteger(Number(value))) {
          this.$message.warning(`${column.label}必须为整数！`);
          row[prop] = '';
        }
      }
    },
    onMaterialEquipmentChange(row, prop, index) {
      const productCode = row.productCode;
      const selectedId = row.productBoxRelationId;
      const options = this.materialEquipmentOption[productCode] || [];
      const selectedOption = options.find(opt => opt.value3 === selectedId);
      if (selectedOption) {
        row.materialEquipment = selectedOption.value2;
      } else {
        row.materialEquipment = '';
      }
      this.getBoxQuantity(row, prop, index);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .isrequired .el-input__inner,
.el-select__input {
  background-color: #ffeea8 !important;
}
::v-deep .el-select .el-input__inner,
.el-select__input {
  background-color: #ffeea8 !important;
}
</style>
