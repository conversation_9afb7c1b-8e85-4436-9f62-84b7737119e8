<template>
  <el-dialog
    :title="checkMsg"
    :visible.sync="dialogVisible"
    width="1000px"
    v-if="dialogVisible"
    append-to-body
    id="dfp-dialog"
    :before-close="handleClose"
  >
      <el-table
        :data="forecastCheckList"
        border
        style="width: 100%"
      >
        <el-table-column
          prop="oemCode"
          label="主机厂编码"
          align="center"
        />
        <el-table-column
          prop="productCode"
          label="产品编码"
          align="center"
        />
        <el-table-column
          prop="forecastQuantity"
          label="最新版当月预测"
          align="center"
        />
        <el-table-column
          prop="deliveryQty"
          label="当月已发货"
          align="center"
        />
        <el-table-column
          prop="waitDeliveryQty"
          label="当月待发货"
          align="center"
        />
        <el-table-column
          prop="deviationRate"
          label="偏差率"
          align="center"
        />
      </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">发 布</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { publish,batchUapdatForecast } from "@/api/dfpApi/deliveryPlan/deliveryPlanFormat";

export default {
  name: 'ForecastCheck',
  props: {
    checkMsg: {
      type: String,
      default: ''
    },
    forecastCheckList: {
      type: Array,
      default: () => []
    },
    selectedRowKeys: {
      type: Array,
      default: () => []
    },
    deliveryPlanList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    handleCancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    async handleConfirm() {
      this.loading = true;
      try {
        let info = this.forecastCheckList.map(item => ({
          id: item.id,
          oemCode: item.oemCode,
          productCode: item.productCode,
          forecastQuantity: item.forecastQuantity,
          deliveryQty: item.deliveryQty,
          waitDeliveryQty: item.waitDeliveryQty
        }))
        const res = await batchUapdatForecast(info)
        if(res.success){
          if(this.deliveryPlanList?.length > 0) {
            this.$confirm('存在同客户的产品编码，请确认是否合并下发?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async() => {
              let isLatest = await this.$parent.latestVersion();
              if(!isLatest) {
                this.$message.warning('版本已不是最新版本，请刷新后重试')
                this.$parent.QueryComplate();
                this.loading = false;
                return;
              }
              this.$parent.publishInfo = this.deliveryPlanList;
              this.$parent.$refs.publishDom.dialogVisible = true;
              this.dialogVisible = false;
              this.loading = false;
            }).catch(() => {
              this.loading = false;
            })
          } else {
            publish(this.selectedRowKeys).then((res) => {
              if (res.success) {
                this.$parent.detailData = [];
                this.$parent.SelectionChange('reset')
                setTimeout(() => {
                  this.$parent.QueryComplate();
                }, 500)
                this.dialogVisible = false;
                this.loading = false;
                this.$message.success(res.msg || this.$t("operationSucceeded"));
              } else {
                this.loading = false;
                this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
              }
            }).catch(error => {
              console.log(error,'发布出错')
              this.loading = false;
            })
          }
        } else {
          this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
        }
      }
      catch(error){
        console.log(error, '出错了')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__header {
    padding: 20px 45px 10px;
}
</style>
