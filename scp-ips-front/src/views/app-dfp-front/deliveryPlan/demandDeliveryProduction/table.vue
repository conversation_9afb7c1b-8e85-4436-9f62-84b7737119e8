<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMps"
      :selection-change="SelectionChange"
      :add-visible="false"
      :edit-visible="false"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :mergeColumms="[{prop:'vehicleModelCode'},  {prop:'productCode'}]"
      :ImportVisible="false"
      :export-visible="false"
      :HaveDynamicColumn="true"
      :requestHeaders="requestHeaders"
      :CustomSetVisible="false"
      :ScreenVisible="false"
      :fpagination="false"
    >
      <template slot="header">
        <el-input style="margin-right:10px;width: 140px" v-model="productCode" :placeholder="'请输入产品编码'" size='mini' />
        <el-input style="margin-right:10px;width: 140px" v-model="vehicleModelCode" :placeholder="'请输入内部车型'" size='mini' />
        <el-button size="mini" type="primary" v-debounce="[queryFn]">查 询</el-button>

        <el-dropdown @command="ExportTemplate">
          <el-button size="medium">
            导入模版<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="dynamicDeliveryTrackingJC">下载导入模板-夹层</el-dropdown-item>
            <el-dropdown-item command="dynamicDeliveryTrackingGH">下载导入模板-钢化</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <el-dropdown @command="importTemplate">
          <el-button size="medium">
            导入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="dynamicDeliveryTrackingJC">导入-夹层</el-dropdown-item>
            <el-dropdown-item command="dynamicDeliveryTrackingGH">导入-钢化</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <input
          id="demandDeliveryProduction-upload"
          type="file"
          accept="application/-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          @change="fileUp"
        />
        <el-button size="medium" @click="eventFn('1')">设置紧急交付</el-button>
        <!-- <el-button size="medium" @click="eventFn('2')">取消紧急交付</el-button> -->
      </template>
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop.indexOf('-') > -1">
        <template v-if="scope.row.category === '装车计划'">
          <div
            :style="{
              backgroundColor: scope.row[scope.column.prop + '_loadingColor'] == '1' ? '#00b050' :
              scope.row[scope.column.prop + '_loadingColor'] == '2' ? '#92d050' :
              scope.row[scope.column.prop + '_loadingColor'] == '3'?'#00b0f0':
              scope.row[scope.column.prop + '_loadingColor'] == '4'?'#f00':'',
              height: '100%'
            }"
          >
            {{ scope.row[scope.column.prop] }}

          </div>
        </template>
          <template v-if="scope.row.category === '发货计划'">
            <div
              :style="{
              backgroundColor: scope.row[scope.column.prop + '_demandColor'] == '1' ? '#00b050' :
              scope.row[scope.column.prop + '_demandColor'] == '2' ? '#92d050' :
              scope.row[scope.column.prop + '_demandColor'] == '3'?'#00b0f0':
              scope.row[scope.column.prop + '_demandColor'] == '4'?'#f00':'',
              height: '100%'
            }"
            >
              {{ scope.row[scope.column.prop] }}

            </div>
          </template>
        <span v-else>
          {{ scope.row[scope.column.prop] }}
        </span>
        </div>
      </template>

    </yhl-table>
    <FormDialog
      ref="formDialogRef"
      :selectedRowKeys="selectedRowKeys"
      @submitAdd="QueryComplate"
    />
  </div>
</template>
<script>
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAllWithResWithService
} from "@/api/dfpApi/componentCommon";
// import {dropdownByCollectionCode} from "@/api/mdsApi/dropdown";
import { doUnPublish, uploadFile ,queryDemandDeliveryProductionReport} from '@/api/dfpApi/deliveryPlan/demandDeliveryProduction'
import { dropdownEnum } from "@/api/mdsApi/itemManagement/index";
import FormDialog from "./formDialog.vue";
import baseUrl from '@/utils/baseUrl';
export default {
  name: "demandDeliveryProduction",
  components: {
    FormDialog
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      tableColumns: [
        {
          label: "内部车型",
          prop: "vehicleModelCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "中转库存",
          prop: "bohStock",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "在途",
          prop: "transportingQty",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "仓库成品",
          prop: "fgStock",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "包装后",
          prop: "afterPacking",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "合片后",
          prop: "afterLamination",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "成形后",
          prop: "afterShape",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "已排产量",
          prop: "scheduleQty",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {label:'类别',prop:'category',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
      ],
      tableRow:[
        {
          label: '装车计划',
          prop: 'loadingQty',
          _color:'loadingColor'
        },
        {
          label: '发货计划',
          prop: 'demandQty',
          _color:'demandColor'
        }],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_sds_ord_work_order_supplementary_publish_log",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      nowTime: false,
      fileObjectType: '',
      productCode: '',
      vehicleModelCode: ''
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  methods: {
    // 导出模版
    ExportTemplate(t) {
      ExportTemplateAllWithResWithService(t, '/api/mps');
    },
    fileUp(event) {
      const files = event.target.files;
      if (!files || !files[0]) {
        return
      }
      let formData = new FormData();
      formData.append("importType", 'INCREMENTAL_IMPORT');
      formData.append("objectType", this.fileObjectType);
      formData.append("file", files[0]);

      const fileInput = document.getElementById('demandDeliveryProduction-upload');
      uploadFile(formData).then((res) => {
        // 重置 input 值
        if(fileInput) fileInput.value = '';
        this.fileObjectType =  '';
        if (res.success) {
          this.$message.success(res.msg);
          this.QueryComplate();
        } else {
          let str = res.data.map(m => {
            return m.errorDetail + '<br/>'
          })
          let msg = res.msg + '<br/>' + str
          this.$message({showClose: true, message: msg || this.$t("importFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
        }
      }).catch((res) => {
        if(fileInput) fileInput.value = '';
        this.fileObjectType =  '';
      })
    },
    importTemplate(t) {
      this.fileObjectType = t
      document.getElementById('demandDeliveryProduction-upload').click();
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_screens, _sorts) {
      if (this.nowTime) {
        this.nowTime = false;
        return;
      }
      this.loading = true;
      queryDemandDeliveryProductionReport({
        productCode: this.productCode,
        vehicleModelCode: this.vehicleModelCode
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          let data = res.data;
          let list = [];
          console.log('后端返回的数据:', data);

          if (data && data.length > 0) {
            // 处理动态列头
            data[0].header.forEach(row => {
              // 保持原有逻辑
              let index = this.tableColumns.findIndex(x => x.prop == row)
              if (index < 0) {
                this.tableColumns.push({
                  label: row,
                  prop: row + '',
                  dataType: 'CHARACTER',
                  width: '120',
                  align: 'center',
                  fixed: 0,
                  sortBy: 1,
                  showType: 'TEXT',
                  fshow: 1,
                  fscope: true,
                })
              }
            })

            // 处理数据行
            data.forEach(row => {
              this.tableRow.forEach((item) => {
                // 保持原有逻辑
                let dataRow = {
                  category: item.label,
                  vehicleModelCode: row.vehicleModelCode,
                  bohStock: row.bohStock,
                  productCode: row.productCode,
                  transportingQty: row.transportingQty,
                  fgStock: row.fgStock,
                  afterPacking: row.afterPacking,
                  afterLamination: row.afterLamination,
                  afterShape: row.afterShape,
                  scheduleQty: row.scheduleQty,
                };
                data[0].header.forEach((m, index) => {
                  dataRow[m] = row.details[index][item.prop] || 0
                  dataRow[m+ "_demandColor"] = row.details[index].demandColor
                  dataRow[m+ "_loadingColor"] = row.details[index].loadingColor
                })
                list.push(dataRow)
              })
            })
          }

          console.log('处理后的表格数据:', list);
          this.$nextTick(() => {
            this.tableData = list;
            this.total = data.length || 0;
          })
        }
        this.nowTime = true;
        setTimeout(() => {
          this.nowTime = false;
        }, 500);
        this.handleResize()
      })
    },
    eventFn(e) {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要操作的行！')
        return
      }
      console.log('eventFn', this.selectedRowKeys)
      if (e === '1') {
        this.$refs.formDialogRef.buildTrackingByProductCodeList(this.selectedRowKeys)
        return
      }
      if (e === '2') {
        doUnPublish(this.selectedRowKeys).then(res => {
          if (res.success) {
            this.$message.success('取消发布成功！');
            this.SelectionChange([]);
            this.QueryComplate();
          }
        }).catch(error => {
          console.log("取消发布异常", error);
        });
        return
      }
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate( _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.productCode);
    },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      // let enumsKeys = this.initEnums();
      // console.log("enumsKeys:"+enumsKeys)
      // dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
      //   if (response.success) {
      //     let data = [];
      //     for (let key in response.data) {
      //       let item = response.data[key];
      //       data.push({
      //         key: key,
      //         values: item,
      //       });
      //     }
      //     data.push(
      //       JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      //     );
      //     this.enums = data;
      //     // this.dropdownByCollectionCode('PLAN_ORDER_STATUS', 'PLAN_ORDER_STATUS');
      //   }
      // });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    // 值集
    dropdownByCollectionCode(n, m) {
      let params = {
        collection: n,
      }
      dropdownByCollectionCode(params)
        .then((res) => {
          if (res.success) {
            let obj = {
              key: m,
              values: res.data,
            }
            this.enums.push(obj);
          }
        })
        .catch((err) => {})
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
    queryFn() {
      this.QueryComplate()
    },
  },
};
</script>
<style scoped>
#demandDeliveryProduction-upload {
  height: 0;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style>
