<template>
  <div style="display:inline-block">
    <el-dialog
      :title="title"
      width="1200px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dfp-dialog"
      class="demandDeliveryProduction-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <el-form style="max-height: 300px;overflow: auto;" :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="100px" size="mini">
        <el-checkbox-group v-model="checkList">
          <div v-for="(domain, index) in ruleForm.list" :key="index + 'a'">
            <el-row
              style="margin-top: 5px"
              type="flex"
              justify="space-between"
              >
              <el-col :span="1">
                <el-checkbox style="margin-top: 6px" v-if="domain.id" :label="domain.id">{{''}}</el-checkbox>
              </el-col>
              <el-col :span="5">
                <el-form-item label="发货时间" label-width="80px"
                  :prop="'list.' + index + '.deliveryTime'"
                  :rules="{
                    required: true,
                    message: $t('emptyValidate'),
                    trigger: 'blur',
                  }"
                >
                  <el-date-picker
                    size="mini"
                    style="width: 146px"
                    v-model="domain.deliveryTime"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="待进仓数量" label-width="100px"
                  :prop="'list.' + index + '.inWarehousedQuantity'"
                  :rules="{
                    required: true,
                    message: $t('emptyValidate'),
                    trigger: 'blur',
                  }"
                >
                  <el-input v-model="domain.inWarehousedQuantity" clearable :placeholder="$t('placeholderInput')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="要求进仓时间" label-width="110px"
                  :prop="'list.' + index + '.inWarehousedTime'"
                  :rules="{
                    required: true,
                    message: $t('emptyValidate'),
                    trigger: 'blur',
                  }">
                  <el-date-picker
                    size="mini"
                    style="width: 250px"
                    v-model="domain.inWarehousedTime"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm"
                    type="datetime"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="间隔时间" label-width="80px">
                  <!-- <el-time-picker
                    v-model="intervalTime['t'+index]"
                    @change="setTime($event, index)"
                    style="width: 86px"
                    value-format="HH:mm"
                    format="HH:mm"
                    placeholder="间隔时间">
                  </el-time-picker> -->
                  <el-input-number
                    style="width: 86px"
                    controls-position="right"
                    size="mini"
                    :min="0"
                    @change="setTime($event, index)"
                    v-model="intervalTime['t'+index]"
                    clearable>
                  </el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="1">
                <el-link style="margin-top: 6px" v-if="index > 0" type="primary" @click="delList(index)">删除</el-link>
              </el-col>
            </el-row>
            <template v-if="domain.taskVOS">
              <el-row style="margin-top: 5px" v-for="(_domain, _index) in domain.taskVOS" :key="_index + 'b'">
                <el-col :span="1">
                  <el-form-item :label="_domain.standardStepCode + '-' + _domain.standardStepName" label-width="100px">
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="数量" label-width="90px">
                    <el-input v-model="_domain.quantity" clearable :placeholder="$t('placeholderInput')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="产线" label-width="60px">
                    <el-select
                      style="width: 100%"
                      v-model="_domain.physicalResourceId"
                      clearable
                      filterable
                      :placeholder="$t('placeholderSelect')"
                    >
                      <el-option
                        v-for="item in _domain.routingStepResourceDOList"
                        :key="item.physicalResourceId"
                        :label="item.physicalResourceDOS[0].physicalResourceName + '-' +item.physicalResourceDOS[0].physicalResourceCode"
                        :value="item.physicalResourceId"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="生产时间" label-width="80px">
                    <el-date-picker
                      v-if="_domain.standardStepName !== '高压'"
                      size="mini"
                      style="width: 280px"
                      v-model="_domain.startTime"
                      format="yyyy-MM-dd HH:mm"
                      value-format="yyyy-MM-dd HH:mm"
                      type="datetime"
                      placeholder="选择日期时间">
                    </el-date-picker>

                    <el-date-picker
                      v-else
                      v-model="_domain.timerange"
                      style="width: 280px"
                      type="datetimerange"
                      format="yyyy-MM-dd HH:mm"
                      value-format="yyyy-MM-dd HH:mm"
                      range-separator="~"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item v-if="_index === domain.taskVOS.length - 1" label="" label-width="90px">
                    <div style="width: 86px"></div>
                  </el-form-item> 
                  <el-form-item v-else label="间隔时间" label-width="90px">
                    <!-- <el-time-picker
                      v-model="intervalTime['t'+index+'i'+_index]"
                      @change="setTime($event, index, _index)"
                      style="width: 86px"
                      value-format="HH:mm"
                      format="HH:mm"
                      placeholder="间隔时间">
                    </el-time-picker> -->
                    <el-input-number
                      style="width: 86px"
                      controls-position="right"
                      size="mini"
                      :min="0"
                      @change="setTime($event, index, _index)"
                      v-model="intervalTime['t'+index+'i'+_index]"
                      clearable>
                    </el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="流水线时间" label-width="80px">
                    <el-input-number style="width: 86px" controls-position="right" size="mini" :min="0" v-model="_domain.pipelineTime" clearable></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-link v-if="_domain.standardStepName !== '高压'" style="margin-top: 6px" type="primary" @click="openInfo(_domain, index, _index)">展开</el-link>
                </el-col>
              </el-row>
            </template>
          </div>
        </el-checkbox-group>
      </el-form>
      <el-link style="margin-top: 10px" type="primary" @click="addList">分批发货</el-link>
      <el-link v-if="ruleForm.list[0].id" style="margin: 10px 0 0 20px" type="primary" @click="doUnPublish">取消紧急交付</el-link>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini"  v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
        <el-button size="mini" type="primary" :loading="loading" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>

      <el-dialog
        width="800px"
        title=""
        :visible.sync="innerVisible"
        append-to-body
        id="dfp-dialog"
        v-dialogDrag="true"
      >
        <el-form style="max-height: 300px;overflow: auto;" ref="innerForm" :model="innerForm" label-width="110px">
          <el-row
            type="flex"
            justify="space-between"
            v-for="(item, index) in innerForm"
            :key="index"
            >
            <el-col :span="8">
              <el-form-item style="margin-bottom: 5px;" label="要求开始时间" label-width="110px">
                <el-date-picker
                  size="mini"
                  style="width: 146px"
                  v-model="item.startTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择日期时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item style="margin-bottom: 5px;" label="结束时间" label-width="110px">
                <el-date-picker
                  size="mini"
                  style="width: 146px"
                  v-model="item.endTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择日期时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item style="margin-bottom: 5px;" label="交付数量" label-width="100px">
                <el-input size="mini" v-model="item.plannedQuantity" clearable :placeholder="$t('placeholderInput')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <span
                v-if="index == 0"
                @click="addDomain"
                class="el-icon-circle-plus-outline"
                style="font-size: 20px;margin: 10px 0 0 10px;"
              ></span>
              <span
                v-else
                @click="removeDomain(index)"
                class="el-icon-remove-outline"
                style="font-size: 20px;margin: 10px 0 0 10px;"
              ></span>
            </el-col>
          </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button size="mini"  v-debounce="[innerClose]">{{$t('cancelText')}}</el-button>
          <el-button size="mini" type="primary" :loading="loading" v-debounce="[innerSubmit]">{{$t('okText')}}</el-button>
        </span>
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
import { buildTrackingByProductCodeList, buildSubTaskByTask, doPublish, doUnPublish } from '@/api/dfpApi/deliveryPlan/demandDeliveryProduction'
import moment from 'moment'
export default {
  name: 'deliveryPlanVersionForm',
  components: {},
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '紧急交付跟踪',
      loading: false,
      checkList: [],
      ruleForm: {
        list: [{
          deliveryTime: null,
          inWarehousedQuantity: null,
          inWarehousedTime: null,
        }],
      },
      intervalTime: {},
      rules: {
        // deliveryTime: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
      },
      innerVisible: false,
      value1: [],
      innerForm: [{}],
      listIndex: null,
      list_Index: null,
      standardStepCode: '',
      innerFormCopy: {},
      quantity: null,
    }
  },
  mounted() {
  },
  methods: {
    addList() {
      let obj =  this.ruleForm.list[this.ruleForm.list.length - 1]
      obj = JSON.parse(JSON.stringify(obj))
      obj.id = null;
      obj.deliveryTime = null;
      obj.inWarehousedQuantity = null;
      obj.inWarehousedTime = null;
      console.log(obj)
      obj.taskVOS.forEach(n => {
        n.id = undefined;
        n.dynamicDeliveryTrackingId = undefined;
        n.quantity = undefined;
        n.physicalResourceId = undefined;
        n.pipelineTime = undefined;
        n.startTime = undefined;
        n.timerange = [];
        n.subTaskVOS && n.subTaskVOS.forEach(n => {
          n.taskId = ''
        })
      })
      this.ruleForm.list.push(obj);
    },
    setTime(value, index, _index) {
      // console.log(value, index, _index);
      // console.log(this.intervalTime);
      // console.log(this.ruleForm.list[index].inWarehousedTime);
      if (!value) {
        if (_index === undefined) {
          this.ruleForm.list[index].taskVOS.map((m, mindex)=> {
            if (m.hasOwnProperty('startTime')) {
              m.startTime = undefined;
            }
            if (m.hasOwnProperty('timerange')) {
              m.timerange = [];
            }
            this.$set(this.intervalTime, 't'+index+'i'+mindex, null)
          })
        } else {
          this.ruleForm.list[index].taskVOS.map((m, mindex) => {
            if (mindex > _index) {
              if (m.hasOwnProperty('startTime')) {
                m.startTime = undefined;
              }
              if (m.hasOwnProperty('timerange')) {
                m.timerange = [];
              }
              this.$set(this.intervalTime, 't'+index+'i'+mindex, null)
            }
          })
        }
        return;
      }
      let minutes = value * 60;
      if (index === 0 && _index === undefined) {
        let time = moment(this.ruleForm.list[index].inWarehousedTime, 'YYYY-MM-DD HH:mm');
        // const [hours, minutes] = value.split(':').map(Number);
        // time.add(hours, 'hours').add(minutes, 'minutes');
        time.subtract(minutes, 'minutes');
        this.ruleForm.list[index].taskVOS[0].startTime = time.format('YYYY-MM-DD HH:mm');
        return
      }
      if (_index !== undefined && _index + 1 < this.ruleForm.list[index].taskVOS.length) {
        let n = this.ruleForm.list[index].taskVOS[_index]
        let _n = this.ruleForm.list[index].taskVOS[_index + 1]
        if (_n.standardStepName === '高压') {
          if (!n.startTime) {
            return
          }
          let time = moment(n.startTime, 'YYYY-MM-DD HH:mm');
          time.subtract(minutes, 'minutes');
          this.$set(_n, 'timerange', [time.format('YYYY-MM-DD HH:mm'), time.format('YYYY-MM-DD HH:mm')])
          return;
        }

        if (n.standardStepName === '高压') {
          if (!n.timerange[0]) {
            return
          }
          let time = moment(n.timerange[0], 'YYYY-MM-DD HH:mm');
          time.subtract(minutes, 'minutes');
          _n.startTime = time.format('YYYY-MM-DD HH:mm')
          return;
        }
        if (!n.startTime) {
          return
        }
        let _time = moment(n.startTime, 'YYYY-MM-DD HH:mm');
        _time.subtract(minutes, 'minutes');
        _n.startTime = _time.format('YYYY-MM-DD HH:mm')
      }
    },
    delList(index) {
      this.ruleForm.list.splice(index, 1);
    },
    handleClose() {
      this.dialogVisible = false
      this.checkList = [];
      this.innerFormCopy = {}
      this.standardStepCode = ""
      this.ruleForm = {
        list: [{
          deliveryTime: null,
          inWarehousedQuantity: null,
          inWarehousedTime: null,
        }],
      }
      this.intervalTime = {}
      this.$refs['ruleForm'].resetFields();
    },
    buildTrackingByProductCodeList(arr) {
      // 详情
      buildTrackingByProductCodeList(arr)
        .then(res => {
          if (res.success) {
            this.dialogVisible = true
            this.ruleForm.list = res.data.map(m => {
              m.deliveryTime = m.deliveryTime ? moment(m.deliveryTime).format("YYYY-MM-DD HH:mm") : undefined;
              m.inWarehousedTime = m.inWarehousedTime ? moment(m.inWarehousedTime).format("YYYY-MM-DD HH:mm") : undefined;
              m.taskVOS.forEach(n => {
                if (n.standardStepName === '高压') {
                  let s = n.startTime ? moment(n.startTime).format("YYYY-MM-DD HH:mm") : undefined;
                  let e = n.endTime ? moment(n.endTime).format("YYYY-MM-DD HH:mm") : undefined;
                  n.timerange = s && e ? [s, e] : [];
                } else {
                  n.startTime = n.startTime ? moment(n.startTime).format("YYYY-MM-DD HH:mm") : undefined;
                }
              })
              return m
            })
            console.log('ruleForm------', this.ruleForm);
          } else {
            this.$message.error(res.msg || '未查询出数据！')
          }
        })
        .catch(err => {
        })
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm.list))
          form = form.map(m => {
            m.deliveryTime = m.deliveryTime ? moment(m.deliveryTime).valueOf() : undefined;
            m.inWarehousedTime =  m.inWarehousedTime ? moment(m.inWarehousedTime).valueOf() : undefined;
            let arr = m.taskVOS.filter(n => {
              if (n.standardStepName === '高压') {
                n.startTime = n.timerange[0] ? moment(n.timerange[0]).valueOf() : undefined;
                n.endTime = n.timerange[1] ? moment(n.timerange[1]).valueOf() : undefined;
                n.subTaskVOS = [{
                  taskId: n.id,
                  startTime: n.startTime,
                  endTime: n.endTime,
                  plannedQuantity: n.quantity,
                  finishedQuantity: 0
                }]
              } else {
                n.startTime = n.startTime ? moment(n.startTime).valueOf() : undefined;
              }
              return n.quantity && n.startTime && n.physicalResourceId
            })
            m.taskVOS = arr
            return m
          })
          // console.log('form------', form);
          // 确认下发
          this.loading = true; 
          doPublish(form)
          .then(res => {
            this.loading = false; 
            if (res.success) {
              this.innerFormCopy = {}
              this.standardStepCode = ""
              this.$message.success(this.$t('operationSucceeded'))
              this.handleClose()
              this.$emit('submitAdd')
            } else {
              this.$message({showClose: true, message: res.msg || this.$t('operationFailed'), type: 'error', duration: 0});
            }
          })
          .catch(err => {
            this.loading = false; 
            this.$message({showClose: true, message: err || this.$t('operationFailed'), type: 'error', duration: 0});
          })
        } else {
          return false;
        }
      });
    },
    openInfo(e, index, _index) {
      // console.log(e, index, _index);
      if (!e.quantity || !e.physicalResourceId || !e.startTime) {
        this.$message.warning('数量，产线，生产时间不能为空！')
        return
      }
      this.quantity = e.quantity;
      this.standardStepCode = index + '_'  + _index + '_' + e.standardStepCode;
      if (this.innerFormCopy && this.innerFormCopy[this.standardStepCode]) {
        this.innerForm = this.innerFormCopy[this.standardStepCode]
        this.innerVisible = true
        this.listIndex = index
        this.list_Index = _index
        return
      }
      
      let u = e.routingStepResourceDOList.find(m => m.physicalResourceId === e.physicalResourceId).unitProductionTime
      if (u === null ) {
        this.innerVisible = true
        this.listIndex = index
        this.list_Index = _index
        return;
      }
      buildSubTaskByTask([e])
        .then(res => {
          this.loading = false; 
          if (res.success) {
            this.innerForm = res.data.map(m => {
              m.startTime = moment(m.startTime).format("YYYY-MM-DD HH:mm")
              m.endTime = moment(m.endTime).format("YYYY-MM-DD HH:mm")
              return m
            })
            this.innerVisible = true
            this.listIndex = index
            this.list_Index = _index
          }
        })
        .catch(err => {
        })
    },
    addDomain() {
      this.innerForm.push({});
    },
    removeDomain(index) {
      this.innerForm.splice(index, 1);
    },
    innerClose() {
      this.innerVisible = false
      this.innerFormCopy[this.standardStepCode] = this.innerForm;
      setTimeout(() => {
        this.innerForm = [{}]
      }, 100)
      this.$refs['innerForm'].resetFields();
      this.listIndex = null
      this.list_Index = null
    },
    innerSubmit() {
      this.$refs['innerForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.innerForm))
          let num = 0
          form = form.map(m => {
            m.startTime = moment(m.startTime).valueOf();
            m.endTime = moment(m.endTime).valueOf();
            m.finishedQuantity = 0;
            num = num + Number(m.plannedQuantity)
            return m
          })
          // console.log(num, this.quantity)
          if (num != this.quantity) {
            this.$message.warning('交付数量总计与汇总数量不一致！')
            return
          }
          this.ruleForm.list[this.listIndex].taskVOS[this.list_Index].subTaskVOS = form
          // console.log('form-------', form)
          this.innerClose();
        } else {
          return false;
        }
      });
    },
    doUnPublish() {
      if (this.checkList.length == 0) {
        this.$message.warning('请勾选需要取消的数据！');
      }
      doUnPublish(this.checkList).then(res => { 
        if (res.success) {
          this.buildTrackingByProductCodeList(this.selectedRowKeys)
          this.checkList = [];
          this.$message.success('取消紧急交付成功!');
        } else {
          this.$message.error(res.msg || '取消紧急交付失败！');
        }
      }).catch(error => { 
        console.log("取消发布异常", error);
      });
    },
  }
}
</script>
<style>
.demandDeliveryProduction-dialog .el-dialog__body{
  padding: 10px 18px;
}
</style>