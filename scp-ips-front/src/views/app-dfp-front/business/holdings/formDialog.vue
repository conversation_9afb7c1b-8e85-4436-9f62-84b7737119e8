<template>
  <div
    id="yhl-dialog-test"
    v-if="dialogVisible"
  >
    <yhl-dialog
      ref="yhlDialog"
      :title="title"
      :dialogVisible="dialogVisible"
      @handleClose="handleClose"
      @handleComplete="handleComplete"
      :optionSet="optionSet"
      :fields="fields"
      :tabs="tabs"
      :config="config"
      :selectData="selectData"
      :urlObject="this.getUrlObjectDfp"
      :objectType="objectType"
      @changeField="changeField"
      :newDefaultDataFun="newDefaultDataFun"
      :itemData="itemData"
      :actionModel="actionModel"
      @setDialogConfig="setConfigSet"
      v-if="dialogVisible"
    >
      <!-- <span slot="footer-after">此处可以自定义内容</span> -->
    </yhl-dialog>
  </div>
</template>
<script>
import {
  createApi,
  updateApi,
  detailApi,
} from '@/api/dfpApi/businessData/holdings'
export default {
  name: 'yhl-dialog-test',
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
    enums: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      title: '车型市场保有量',
      dialogVisible: false,
      optionSet: {}, // 设置属性
      fields: [], // form 表单
      tabs: [], // tabs
      config: {}, // 配置
      urlObject: {}, //
      selectData: [], // 下拉数据集合
      objectType: '',
      actionModel: 'ADD', // 新增
      itemData: {}, // 修改的时候传递的数据
      fieldsList: [],
    }
  },
  created() {
    this.tabs = [
      {
        id: 'basicInformation',
        tabName: this.$t('basicInformation'),
        seqNum: 1,
      },
    ]
    this.fields = [  
      {
        prop: 'vehicleModelName', // 字段属性
        label: '全球销量车型名称', // 名称
        dataType: 'CHARACTER',
        showModel: 'INPUT', // 输入框
        seqNum: 10,
        fshow: 'YES',
        showWidth: 'BASE', // 输入框显示的宽度  BASE：标准宽度 ONE_ROW：一行的宽度
        fnewEdit: 'YES', // 新增是否编辑
        fupdateEdit: 'YES', // 修改是否可以编辑
        fnotNull: 'YES', // 是否必填
        showTabs: 'basicInformation', // 属于哪个tab
      },
      {
        prop: 'inventoryQuantity', // 字段属性
        label: '市场保有量', // 名称
        dataType: 'CHARACTER',
        showModel: 'INPUT', // 输入框
        seqNum: 10,
        fshow: 'YES',
        showWidth: 'BASE', // 输入框显示的宽度  BASE：标准宽度 ONE_ROW：一行的宽度
        fnewEdit: 'YES', // 新增是否编辑
        fupdateEdit: 'YES', // 修改是否可以编辑
        fnotNull: 'YES', // 是否必填
        showTabs: 'basicInformation', // 属于哪个tab
      },
    ]
    this.fieldsList = JSON.parse(JSON.stringify(this.fields))
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.selectData = []
      }
    },
  },
  methods: {
    // 获取配置
    getConfigSet() {
      return JSON.parse(JSON.stringify(this.config))
    },
    // 更新配置
    setConfigSet(config) {
      if (config !== null && config !== undefined && config !== '') {
        this.config = JSON.parse(JSON.stringify(config))
      }
      console.log('config', this.config)
    },
    // 新增
    addForm() {
      this.actionModel = 'ADD'
      this.dialogVisible = true
    },
    // 修改 editForm
    editForm() {
      this.fields = JSON.parse(JSON.stringify(this.fieldsList))
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      let info = this.rowInfo
      if (info.id) {
        this.itemData = info
        this.actionModel = 'EDIT'
        this.dialogVisible = true
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.specList = []
    },
    // 非校验  自己填写
    handleComplete(obj) {
      // TODO 具体的业务处理
      console.log('具体的业务处理', obj)
      let form = JSON.parse(JSON.stringify(obj))
      // versionValue
      form.marketInventory = Number(form.marketInventory)
      //   提交数据
      if (this.actionModel == 'ADD') {
        createApi(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('addSucceeded'))
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('addFailed'))
          }
        })
      }
      if (this.actionModel == 'EDIT') {
        form.id = this.rowInfo.id
        form.versionValue = this.rowInfo.versionValue
        updateApi(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('editSucceeded'))
            this.$parent.SelectionChange([])
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('editFailed'))
          }
        })
      }
    },
    // 动态下拉框
    changeField(field, rowData) {
      console.log('changeField', field, rowData)
      // 控制字段可编辑性
      // this.$refs.yhlDialog.setFieldDisabled("test1", true); //
    },
    // 初始化数据
    newDefaultDataFun(resolve) {
      //   resolve({
      //   })
    },
  },
}
</script>
