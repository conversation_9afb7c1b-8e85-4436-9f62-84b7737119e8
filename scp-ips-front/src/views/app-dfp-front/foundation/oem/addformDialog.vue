<template>
  <div style="display:inline-block">
    <Auth url="/dfp/oem/create">
      <div slot="toolBar" style="display:inline-block">
        <el-button size="medium" icon="el-icon-circle-plus-outline" v-debounce="[addForm]">{{$t('addText')}}</el-button>  
      </div>
    </Auth>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dfp-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="组织">
              <!-- <el-input disabled size="small" v-model="ruleForm.saleOrgCode"></el-input> -->
              <el-select
                style="width: 100%"
                v-model="ruleForm.saleOrgId"
                size="small"
                clearable
                filterable
                disabled
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in orgList"
                  :key="item.id"
                  :label="item.saleOrgCode"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="客户名称" prop="customerCode">
              <el-select
                style="width: 100%"
                v-model="ruleForm.customerCode"
                size="small"
                clearable
                filterable
                @change="customerChange"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in customerCodeList"
                  :key="item.value"
                  :label="item.label + '(' + item.value + ')'"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="主机厂编码">
              <el-input disabled size="small" v-model="ruleForm.oemCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="主机厂名称" prop="oemName">
              <el-input size="small" v-model="ruleForm.oemName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="地点1">
              <el-input disabled size="small" v-model="ruleForm.locationArea1"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="地点2" prop="locationArea2">
              <el-select
                style="width: 100%"
                v-model="ruleForm.locationArea2"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in locationArea2List"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="地点3">
              <el-input disabled size="small" v-model="ruleForm.locationArea3"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="地点4">
              <el-input disabled size="small" v-model="ruleForm.locationArea4"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="运输条款">
              <el-input size="small" v-model="ruleForm.transitClause"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="业务类型" prop="businessType">
              <el-select
                style="width: 100%"
                v-model="ruleForm.businessType"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in OemBusinessTypeEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="市场属性" prop="marketType">
              <el-select
                style="width: 100%"
                v-model="ruleForm.marketType"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in OemTradeTypeEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="目标货位" prop="targetStockLocation">
              <el-tooltip :content="getTargetLocation" popper-class="popoverStyle" placement="top" :disabled="!hasTargetLocation">
                <el-select
                  style="width: 100%"
                  v-model="ruleForm.targetStockLocation"
                  size="small"
                  clearable
                  multiple
                  collapse-tags
                  filterable
                  :placeholder="$t('placeholderSelect')"
                >
                  <el-option
                    v-for="item in targetStockLocationEnum"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="是否EDI直连" prop="ediFlag">
              <el-switch size="small" v-model="ruleForm.ediFlag"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="EDI地址">
              <el-input size="small" v-model="ruleForm.ediLocation"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="公司代码">
              <el-input size="small" v-model="ruleForm.plantCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="理货单模式" prop="tallyOrderMode">
              <el-select
                style="width: 100%"
                v-model="ruleForm.tallyOrderMode"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in tallyOrderModeEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="是否自提" prop="pickUpType">
              <el-select
                style="width: 100%"
                v-model="ruleForm.pickUpType"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in pickUpTypeEnums"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="ERP地点编码" prop="erpCustomerAddressId">
              <el-select
                style="width: 100%"
                v-model="ruleForm.erpCustomerAddressId"
                size="small"
                clearable
                filterable
                @change="selErpSiteCode"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in erpSiteCodeOption"
                  :key="item.erpCustomerAddressId"
                  :label="item.erpSiteAddress+'('+item.erpSiteCode+')'"
                  :value="item.erpCustomerAddressId"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="ERP地址">
              <el-input size="small" disabled v-model="ruleForm.erpSiteAddress"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {oemCreate, oemUpdate, getCustomerByNoEdi,
  getAddressOneThreeFourByCustomer, getAddressTwoLike, getPayment, getOemCodeByCustomerCode, getErpSiteDropdown} from "@/api/dfpApi/foundation/oem";
import {saleOrganizeDropDownEnable} from "@/api/dfpApi/foundation/saleOrganize";
import Auth from '@/components/Auth'
export default {
  name: 'oem',
  components: {
    Auth
  },
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
    enums: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      ruleForm: {
        saleOrgId: '',
        customerCode: '',
        oemCode: '',
        oemName: '',
        locationArea1: '',
        locationArea2: '',
        locationArea3: '',
        locationArea4: '',
        businessType: '',
        marketType: '',
        targetStockLocation: [],
        ediFlag: true,
        ediLocation:'',
        plantCode:'',
        tallyOrderMode: '',
        pickUpType: '',
        erpCustomerAddressId: ''
      },
      rules: {
        saleOrgId: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        customerCode: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        oemName: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        locationArea2: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        marketType: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        businessType: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        targetStockLocation: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        tallyOrderMode: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}]
      },
      treeData: [],
      orgList:[],
      OemTransitClauseEnum:[],
      OemBusinessTypeEnum:[],
      OemTradeTypeEnum:[],
      locationArea2List:[],
      targetStockLocationEnum: [],
      customerCodeList: [],
      tallyOrderModeEnum:[],
      pickUpTypeEnums: [],
      erpSiteCodeOption: []
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.saleOrganizeDropDownEnable();
        this.getCustomerByNoEdi();
        this.enums.forEach(item => {
          if (item.key == 'com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum') {
            this.OemBusinessTypeEnum = item.values
          }
          if (item.key == 'com.yhl.scp.dfp.oem.enums.OemTradeTypeEnum') {
            this.OemTradeTypeEnum = item.values
          }
          if (item.key == 'com.yhl.scp.dfp.oem.enums.OemTallyOrderModeEnum') {
            this.tallyOrderModeEnum = item.values
          }
          if (item.key == 'targetStockLocation') {
            this.targetStockLocationEnum = item.values
          }
          if (item.key == 'com.yhl.scp.dfp.oem.enums.OemPickUpTypeEnum') {
            this.pickUpTypeEnums = item.values;
          }
          // if(item.key == 'erpSiteCode'){
          //   this.erpSiteCodeOption = item.values
          // }
        })
      }
    }
  },
  computed: {
    getTargetLocation(){
      return this.ruleForm.targetStockLocation && this.ruleForm.targetStockLocation.join(',')
    },
    hasTargetLocation(){
      return this.ruleForm.targetStockLocation && this.ruleForm.targetStockLocation.length > 0
    }
  },
  mounted() {
  },
  methods: {
    customerChange(e) {
      this.ruleForm.erpCustomerAddressId = '';
      this.ruleForm.erpSiteAddress = '';
      this.erpSiteCodeOption = [];
      
      this.getAddressOneThreeFourByCustomer(e);
      this.getOemCodeByCustomerCode(e);
      this.getErpOptions(e)
      this.getAddressTwoLike(e)
    },
    selErpSiteCode(e){
      let temp = this.erpSiteCodeOption.find(item => item.erpCustomerAddressId === e)
      this.ruleForm.erpSiteAddress = temp?.erpSiteAddress
    },
    getErpOptions(customerCode){
      getErpSiteDropdown({customerCode}).then(res => {
        if(res.success){
          this.erpSiteCodeOption = res.data
        }
      })
    },
    getOemCodeByCustomerCode(customerCode) {
      this.ruleForm.oemCode = ''
      getOemCodeByCustomerCode({customerCode}).then(res=>{
        if (res.success) {
          this.ruleForm.oemCode = res.data
        }
      })
    },
    getAddressOneThreeFourByCustomer(customerCode) {
      this.ruleForm.locationArea1 = ''
      this.ruleForm.locationArea3 = ''
      this.ruleForm.locationArea4 = ''
      getAddressOneThreeFourByCustomer({customerCode}).then(res=>{
        if (res.success) {
          this.ruleForm.locationArea1 = res.data[0].label
          this.ruleForm.locationArea3 = res.data[0].value
          this.ruleForm.locationArea4 = res.data[0].name
        }
      })
    },
    getPayment(customerCode) {
      this.ruleForm.paymentTerm = ''
      getPayment({customerCode}).then(res=>{
        if (res.success) {
          this.ruleForm.paymentTerm = res.data[0]
        }
      })
    },
    getAddressTwoLike(e) {
      getAddressTwoLike({customerCode:e}).then(res=>{
        this.locationArea2List = res.data
      })
    },
    saleOrganizeDropDownEnable() {
      saleOrganizeDropDownEnable().then(res=>{
        this.orgList = res.data
        this.ruleForm.saleOrgId = '324'
      })
    },
    getCustomerByNoEdi() {
      getCustomerByNoEdi().then(res=>{
        if (res.success) {
          this.customerCodeList = res.data
        }
      })
    },
    addForm() {
      this.dialogVisible = true
      this.title = this.$t('addText')
    },
    handleClose () {
      this.dialogVisible = false
      this.ruleForm = {
        saleOrgId: '',
        customerCode: '',
        oemCode: '',
        oemName: '',
        locationArea1: '',
        locationArea2: '',
        locationArea3: '',
        locationArea4: '',
        businessType: '',
        marketType: '',
        ediLocation:'',
        plantCode:'',
        targetStockLocation: '',
      }
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          if (this.title == this.$t('addText')) {
            let obj = this.customerCodeList.find(n => {
              return n.value === form.customerCode
            })
            form.customerName = obj.label
            form.ediFlag = form.ediFlag ? "YES" : "NO"
            form.targetStockLocation = form.targetStockLocation?.join(',')
            oemCreate(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('addSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('addFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('addFailed'))
              })
            return
          }
        }
      });
    },
  }
}
</script>
<style lang="scss">
.popoverStyle{
  max-width: 500px;
  max-height: 100px;
  overflow: scroll;
}
</style>