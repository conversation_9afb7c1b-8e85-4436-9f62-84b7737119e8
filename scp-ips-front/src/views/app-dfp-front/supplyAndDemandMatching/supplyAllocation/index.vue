<template>
  <div id="lowCode">
    <yhl-lcdp
      ref="lcdp"
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectDfp"
      :sysElements="this.getSysElements"
      @loaderComponent="loaderComponent"
      @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <el-tabs class="calendar-tabs-dom" v-model="activeKey">
          <el-tab-pane :label="'分配规则'" name="1" style="height: 100%;">
            <Tabs1 ref="tabs1" :componentKey="componentKey" />
          </el-tab-pane>
          <el-tab-pane :label="'渠道总供应量'" name="2" style="height: 100%;">
            <Tabs2
              ref="tabs2"
              :componentKey="componentKey"
              @queryTabs="queryTabs"
            />
          </el-tab-pane>
          <el-tab-pane :label="'渠道分配明细'" name="3" style="height: 100%;">
            <Tabs3 ref="tabs3" :componentKey="componentKey" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
import Tabs1 from './tabs1/table.vue'
import Tabs2 from './tabs2/table.vue'
import Tabs3 from './tabs3/table.vue'
export default {
  name: 'supplyAllocation',
  components: {
    Tabs1,
    Tabs2,
    Tabs3,
  },
  data() {
    return {
      componentKey: '',
      customContainers: [],
      activeKey: '1',
    }
  },
  created() {
    this.initParams()
    this.loadCustomContainers()
  },
  methods: {
    queryTabs() {
      this.$refs.tabs3.QueryComplate()
    },
    initParams() {
      let key = this.handleComponentKey(this.$route.path);
      this.componentKey = key
    },
    // 初始化自定义内置容器
    loadCustomContainers() {
      this.customContainers.push({
        id: 'C001',
        position: {
          x: 0,
          y: 0,
          w: 50,
          h: 20,
        },
        name: '',
        bindElement: {
          type: 'SYS_BUILTIN_PAGE',
          model: 'SYS_BUILTIN_PAGE',
          config: undefined,
        },
      })
    },
    // 自定义页面自动查询方法
    customPageQuery(item, layoutSetConfig) {
      // let _item = JSON.parse(JSON.stringify(item))
      // if (item.id === 'C001') {
      //   if (
      //     item.bindElement.hasOwnProperty('config') &&
      //     item.bindElement.config.hasOwnProperty('conf')
      //   ) {
      //     _item.bindElement.config.conf.id = layoutSetConfig.conf.version
      //     _item.bindElement.config.componentId = layoutSetConfig.conf.version
      //   }
      //   const params = {
      //     conf: _item.bindElement.config,
      //     customExpressions: layoutSetConfig.customExpressions,
      //   }
      //   this.$refs[item.id].setParams(params)
      //   this.$refs[item.id].QueryComplate()
      // }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig(item) {
      // if (item.id === 'C001') {
      //   return this.$refs[item.id].getCurrentUserPolicy()
      // }
    },
    customPageResize(item) {
      // this.$refs[item.id].$refs.yhltable.handleResize()
      this.$refs[item.id].handleResize()
    },
    loaderComponent(router, id) {
      // Promise.resolve(require('@/' + router).default).then((data) => {
      //   this.$refs.lcdp.setSysObjComponent(data, id)
      // })
    },
  },
}
</script>
<style lang="scss" scoped>
#lowCode {
  width: 100%;
  height: 100%;
  .calendar-tabs-dom {
    height: 100%;
    padding: 0;
    background-color: #fff;
    border-radius: 8px;
  }
}
</style>
<style>
.calendar-tabs-dom .el-tabs__content {
  height: calc(100% - 53px);
}
</style>
