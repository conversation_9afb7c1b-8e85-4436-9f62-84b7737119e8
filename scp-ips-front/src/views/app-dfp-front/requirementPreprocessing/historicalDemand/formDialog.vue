<template>
  <div id="yhl-dialog-test" v-if="dialogVisible">
    <yhl-dialog
      ref="yhlDialog"
      :title="title"
      :dialogVisible="dialogVisible"
      @handleClose="handleClose"
      @handleComplete="handleComplete"
      :optionSet="optionSet"
      :fields="fields"
      :tabs="tabs"
      :config="config"
      :selectData="selectData"
      :urlObject="this.getUrlObjectDfp"
      :objectType="objectType"
      @changeField="changeField"
      :newDefaultDataFun="newDefaultDataFun"
      :itemData="itemData"
      :actionModel="actionModel"
      @setDialogConfig="setConfigSet"
      v-if="dialogVisible"
    >
      <!-- <span slot="footer-after">此处可以自定义内容</span> -->
    </yhl-dialog>
  </div>
</template>
<script>
import {
  createApi,
  updateApi,
  detailApi,
} from '@/api/dfpApi/requirementPreprocessing/historicalDemand'
import {
  getDropdownData,
  unitList,
  dropdownStockPoint,
  dropdownDemandUnit,
  dropdownSalesSegment,
  dropdownProductSeries,
  dropdownByproductSeries,
  productDropdown,
  dropdownCustomer,
} from '@/api/dfpApi/dropdown'
export default {
  name: 'yhl-dialog-test',
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
    enums: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      title: this.$t('historicalDemand'),
      dialogVisible: false,
      optionSet: {}, // 设置属性
      fields: [], // form 表单
      tabs: [], // tabs
      config: {}, // 配置
      urlObject: {}, //
      selectData: [], // 下拉数据集合
      objectType: '',
      actionModel: 'ADD', // 新增
      itemData: {}, // 修改的时候传递的数据
      currencyUnitOptions: [], // 货币单位
      countUnitOptions: [], // 计数单位
      isto: 0,
      specList: [], // 规格
      fieldsList: [],
      copyInfo: {},
    }
  },
  created() {
    this.tabs = [
      {
        id: 'basicInformation',
        tabName: this.$t('basicInformation'),
        seqNum: 1,
      },
    ]
    this.fields = [
      //     发货单号 :label="$t('historicalDemand_orderNo')"
      {
        prop: 'orderNo', // 字段属性
        label: this.$t('historicalDemand_orderNo'), // 名称
        dataType: 'CHARACTER',
        showModel: 'INPUT', // 输入框
        seqNum: 10,
        fshow: 'YES',
        showWidth: 'BASE', // 输入框显示的宽度  BASE：标准宽度 ONE_ROW：一行的宽度
        fnewEdit: 'YES', // 新增是否编辑
        fupdateEdit: 'YES', // 修改是否可以编辑
        fnotNull: 'YES', // 是否必填
        showTabs: 'basicInformation', // 属于哪个tab
      },
      // 库存点代码 :label="$t('historicalDemand_stockPointCode')"
      {
        prop: 'stockPointId',
        label: this.$t('historicalDemand_stockPointCode'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 1,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'YES', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      // 销售组织 :label="$t('historicalDemand_salesSegmentCode')"
      {
        prop: 'salesSegmentId',
        label: this.$t('historicalDemand_salesSegmentCode'),
        dataType: 'CHARACTER',
        showModel: 'CASCADER',
        seqNum: 1,
        fshow: 'YES',
        fnotNull: 'YES', // 是否必填
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        cascaderSet: {
          expandTrigger: 'hover', // 触发方式 click/hover
          multiple: false, // 是否多选
          showAllLevels: true, // 仅显示最后一级
          checkStrictly: true, // 允许选择任一一级
          collapseTags: false, // 多选 ，折叠显示
        },
      },
      // 物品系列代码 :label="$t('historicalDemand_productSeriesCode')"
      {
        prop: 'productSeriesId',
        label: this.$t('historicalDemand_productSeriesCode'),
        dataType: 'CHARACTER',
        showModel: 'CASCADER',
        seqNum: 1,
        fshow: 'YES',
        fnotNull: 'NO', // 是否必填
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        cascaderSet: {
          expandTrigger: 'hover', // 触发方式 click/hover
          multiple: false, // 是否多选
          showAllLevels: true, // 仅显示最后一级
          checkStrictly: true, // 允许选择任一一级
          collapseTags: false, // 多选 ，折叠显示
        },
      },
      // 物品代码 :label="$t('historicalDemand_productCode')"
      {
        prop: 'productId',
        label: this.$t('historicalDemand_productCode'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 1,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      // 客户 :label="$t('historicalDemand_customerName')"
      {
        prop: 'customerId',
        label: this.$t('historicalDemand_customerName'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 1,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      // 数量 :label="$t('historicalDemand_quantity')"
      {
        prop: 'quantity',
        label: this.$t('historicalDemand_quantity'),
        dataType: 'NUMERICAL',
        showModel: 'INPUT',
        seqNum: 4,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'YES',
      },
      // 金额 :label="$t('historicalDemand_amount')"
      {
        prop: 'amount',
        label: this.$t('historicalDemand_amount'),
        dataType: 'NUMERICAL',
        showModel: 'INPUT',
        seqNum: 4,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO',
      },
      // 期望发货日期 :label="$t('historicalDemand_expectedStartTime')"
      {
        prop: 'expectedStartTime',
        label: this.$t('historicalDemand_expectedStartTime'),
        dataType: 'DATE',
        showModel: 'DATE',
        seqNum: 7,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO',
      },
      // 期望到货日期 :label="$t('historicalDemand_expectedEndTime')"
      {
        prop: 'expectedEndTime',
        label: this.$t('historicalDemand_expectedEndTime'),
        dataType: 'DATE',
        showModel: 'DATE',
        seqNum: 7,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO',
      },
      // 实际开始时间 :label="$t('historicalDemand_startTime')"
      {
        prop: 'startTime',
        label: this.$t('historicalDemand_startTime'),
        dataType: 'DATE',
        showModel: 'DATE',
        seqNum: 7,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO',
      },
      // 实际结束时间 :label="$t('historicalDemand_endTime')"
      {
        prop: 'endTime',
        label: this.$t('historicalDemand_endTime'),
        dataType: 'DATE',
        showModel: 'DATE',
        seqNum: 7,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO',
      },

      //   促销活动标识 :label="$t('historicalDemand_promotionFlag')"
      {
        prop: 'promotionFlag',
        label: this.$t('historicalDemand_promotionFlag'),
        dataType: 'CHARACTER',
        showModel: 'SWITCH',
        seqNum: 9,
        fshow: 'YES',
        fnotNull: 'NO', // 是否必填
        showWidth: 'BASE',
        showTabs: 'basicInformation',
      },
      // 运输方式 :label="$t('historicalDemand_transportType')"
      {
        prop: 'transportType',
        label: this.$t('historicalDemand_transportType'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 1,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      // 状态 :label="$t('historicalDemand_orderStatus')"
      {
        prop: 'orderStatus',
        label: this.$t('historicalDemand_orderStatus'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 1,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      // 备注 :label="$t('historicalDemand_remark')"
      {
        prop: 'remark', // 字段属性
        label: this.$t('historicalDemand_remark'), // 名称
        dataType: 'CHARACTER',
        showModel: 'INPUT', // 输入框
        seqNum: 10,
        fshow: 'YES',
        showWidth: 'BASE', // 输入框显示的宽度  BASE：标准宽度 ONE_ROW：一行的宽度
        fnewEdit: 'YES', // 新增是否编辑
        fupdateEdit: 'YES', // 修改是否可以编辑
        fnotNull: 'NO', // 是否必填
        showTabs: 'basicInformation', // 属于哪个tab
      },
    ]
    this.fieldsList = JSON.parse(JSON.stringify(this.fields))
  },
  watch: {
    dialogVisible(nv) {
      this.selectData = []
      if (nv) {
        this.dropdownStockPoint()
        this.dropdownSalesSegment()
        this.getDropdownData()
        this.dropdownProductSeries()
        this.productDropdown()
        this.dropdownCustomer()
      }
    },
  },
  methods: {
    // 获取配置
    getConfigSet() {
      return JSON.parse(JSON.stringify(this.config))
    },
    // 更新配置
    setConfigSet(config) {
      if (config !== null && config !== undefined && config !== '') {
        this.config = JSON.parse(JSON.stringify(config))
      }
      console.log('config', this.config)
    },
    // 根据enum获取下拉
    getDropdownData() {
      let params = {
        enumKeys:
          'com.yhl.scp.dfp.demand.enums.OrderStatusEnum,com.yhl.scp.dfp.demand.enums.TransportTypeEnum',
      }
      getDropdownData(params).then((res) => {
        let transportTypeObj = {
          id: 'transportType',
          values: res.data['com.yhl.scp.dfp.demand.enums.TransportTypeEnum'],
        }
        let OrderStatusObj = {
          id: 'orderStatus',
          values: res.data['com.yhl.scp.dfp.demand.enums.OrderStatusEnum'],
        }
        this.selectData.push(transportTypeObj, OrderStatusObj)
      })
    },
    // 物品系列下拉（树形）
    dropdownProductSeries() {
      let info = {
        expandDepth: 0,
      }
      dropdownProductSeries(info)
        .then((res) => {
          this.productOptions = res.data
          if (res.data.length) {
            let productSeriesOptionsObj = {
              id: 'productSeriesId',
              values: res.data,
            }

            this.selectData.push(productSeriesOptionsObj)
          }
        })
        .catch((err) => {})
    },
    // 客户下拉
    dropdownCustomer() {
      dropdownCustomer()
        .then((res) => {
          if (res.success) {
            let customerOptionsObj = {
              id: 'customerId',
              values: res.data,
            }
            this.selectData.push(customerOptionsObj)
          }
        })
        .catch((err) => {})
    },

    // 物品下拉
    productDropdown(productSeriesId) {
      let params = {
        productSeriesId,
      }
      productDropdown(params)
        .then((res) => {
          if (res.success) {
            let productOptionsObj = {
              id: 'productId',
              values: res.data,
            }
            this.selectData.push(productOptionsObj)
          }
        })
        .catch((err) => {})
    },
    // 库存点
    dropdownStockPoint() {
      dropdownStockPoint()
        .then((res) => {
          if (res.success) {
            this.stockPointOptions = res.data
            if (res.data.length) {
              let dropdownStockPointObj = {
                id: 'stockPointId',
                values: res.data,
              }
              this.selectData.push(dropdownStockPointObj)
            }
          }
        })
        .catch((err) => {})
    },
    // 需求单元（枚举接口还没有先随便写的需要后补）
    dropdownDemandUnit() {
      let info = {
        expandDepth: 0,
      }
      dropdownDemandUnit(info)
        .then((res) => {
          this.productOptions = res.data
          if (res.data.length) {
            let productOptionsObj = {
              id: 'demandUnit',
              values: res.data,
            }
            this.selectData.push(productOptionsObj)
            console.log(this.selectData, '测试productId')
          }
        })
        .catch((err) => {})
    },
    // 组织下拉
    dropdownSalesSegment() {
      let info = {
        expandDepth: 0,
      }
      dropdownSalesSegment(info)
        .then((res) => {
          this.productOptions = res.data
          if (res.data.length) {
            let organizationOptionsObj = {
              id: 'salesSegmentId',
              values: res.data,
            }
            this.selectData.push(organizationOptionsObj)
          }
        })
        .catch((err) => {})
    },
    // 新增
    addForm() {
      this.actionModel = 'ADD'
      this.dialogVisible = true
    },
    // 修改 editForm
    editForm() {
      this.fields = JSON.parse(JSON.stringify(this.fieldsList))
      console.log(this.selectedRowKeys, '双击修改数据')
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      let info = this.rowInfo
      for (const key in info) {
        info[key] = this.setValueEmpty(info[key])
      }
      if (info.id) {
        detailApi(info.id).then((res) => {
          if (res.success) {
            let resData = res.data
            this.itemData = resData
            this.actionModel = 'EDIT'
            this.dialogVisible = true
            this.$nextTick(() => {
              if (resData.productSeriesId) {
                this.$refs.yhlDialog.setFieldDisabled('productId', true)
              }
              if (resData.productId) {
                this.$refs.yhlDialog.setFieldDisabled('productSeriesId', true)
              }
            })
            // 修改
            console.log('修改数据', this.itemData)
          }
        })
      }
    },
    setValueEmpty(value) {
      return value === '' || value === null ? undefined : value
    },
    handleClose() {
      this.dialogVisible = false
      this.specList = []
    },
    // 非校验  自己填写
    handleComplete(obj) {
      // TODO 具体的业务处理
      console.log('具体的业务处理', obj)
      let form = JSON.parse(JSON.stringify(obj))
      form.productSeriesId =
        form.productSeriesId && typeof form.productSeriesId == 'string'
          ? form.productSeriesId
          : form.productSeriesId && form.productSeriesId.length
          ? form.productSeriesId[form.productSeriesId.length - 1]
          : ''
      form.salesSegmentId =
        form.salesSegmentId && typeof form.salesSegmentId == 'string'
          ? form.salesSegmentId
          : form.salesSegmentId[form.salesSegmentId.length - 1]
      console.log('全部的提交数据', form)
      //   提交数据
      if (this.actionModel == 'ADD') {
        createApi(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('addSucceeded'))
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('addFailed'))
          }
        })
      }
      if (this.actionModel == 'EDIT') {
        form.id = this.rowInfo.id
        updateApi(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('editSucceeded'))
            this.$parent.SelectionChange([])
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('editFailed'))
          }
        })
      }
    },
    // 动态下拉框
    changeField(field, rowData) {
      console.log('changeField', field, rowData)
      // 控制字段可编辑性
      // this.$refs.yhlDialog.setFieldDisabled("test1", true); //
      if (field.prop == 'productSeriesId') {
        if (!rowData.productSeriesId || rowData.productSeriesId.length === 0) {
          this.$refs.yhlDialog.setFieldDisabled('productId', false)
        } else {
          rowData.productId = undefined
          this.$refs.yhlDialog.setFieldDisabled('productId', true)
        }
      }
      if (field.prop == 'productId') {
        if (!rowData.productId) {
          this.$refs.yhlDialog.setFieldDisabled('productSeriesId', false)
        } else {
          rowData.productSeriesId = undefined
          this.$refs.yhlDialog.setFieldDisabled('productSeriesId', true)
        }
      }
    },
    // 初始化数据
    newDefaultDataFun(resolve) {
      if (this.isto + 1000 > new Date().getTime()) {
        return
      }
      this.isto = new Date().getTime()
      resolve({
        orderNo: '', // a： prop 字段； prop的值： 'aaaaa'
        stockPointId: '',
        salesSegmentId: '',
        productSeriesId: '',
        productId: '',
        customerId: '',
        quantity: '',
        amount: '',
        expectedStartTime: '',
        expectedEndTime: '',
        startTime: '',
        endTime: '',
        promotionFlag: 'NO',
        transportType: '',
        orderStatus: '',
        remark: '',
      })
    },
  },
}
</script>
<style>
/* #yhl-dialog-test {
  width: 100%;
  height: 100vh;
} */
</style>
