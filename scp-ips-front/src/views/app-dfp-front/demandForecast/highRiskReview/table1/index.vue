<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :sort-visible="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="false"
      :ImportVisible="false"
      :ExportVisible="false"
      :hintObject="objectTips"
      :requestHeaders="requestHeaders"
      :RowClick="RowClick"
      :DefaultFirstRow="true"
      :CustomSetVisible="false"
      :CellSetVisible="false"
      :fpagination="true"
      paginationLocation="BOTTOM"
    >
      <template slot="header">
        <span style="font-size: 14px; color: #666">组织：</span>
        <el-select
          size="mini"
          v-model="organizationCode"
          filterable
          @change="changeOrganization"
          placeholder="请选择组织"
          style="margin-right: 10px; width: 120px"
        >
          <el-option
            v-for="item in orgList"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          ></el-option>
        </el-select>
        <span style="font-size: 14px; color: #666">版本号：</span>
        <el-select
          size="mini"
          v-model="versionCode"
          filterable
          @change="eventFn('1')"
          @visible-change ="refreshTargetVersion"
          placeholder="请选择版本"
          style="margin-right: 10px; width: 200px"
        >
          <el-option
            v-for="item in versionList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </template>
    </yhl-table>
 </div>
</template>
<script>
import { deleteApi } from "@/api/dfpApi/requirementPreprocessing/promotionCalendar";
import { orgOption } from "@/api/mpsApi/planExecute/mainProductionPlan";
import { originDemandVersionList } from "@/api/dfpApi/versionManage/originDemandVersion";
import { getTableData, getHighRiskOptions, getStandardStepList } from "@/api/dfpApi/highRiskReview/index";
import {
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
  modelExportDataSimple
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";

import { hasPrivilege } from "@/utils/storage";

export default {
  name: "highRiskReview",
  components: {
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: localStorage.getItem("scenario"),
        Tenant: localStorage.getItem("Tenant"),
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "annualDemandTarget",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "annualDemandTarget",
      },
      tableColumns: [],
      tableColumnsCopy: [
        {
          label: "车型",
          prop: "vehicleModelCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'vehicleModelCode'
        },
        {
          label: "本厂编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "100",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'productCode'
        },
        {
          label: "产品名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "100",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'oemCode'
        },
        {
          label: "零件号",
          prop: "partNumber",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品风险等级",
          prop: "riskLevel",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
         {
          label: "客户15天需求量",
          prop: "demand15Days",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
         {
          label: "客户30天需求量",
          prop: "demand30Days",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
         {
          label: "厂外设定标准安全库存天数",
          prop: "outsideStandardSafetyStockDays",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
         {
          label: "厂内设定标准安全库存天数",
          prop: "insideStandardSafetyStockDays",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
         {
          label: "中转库成品库存",
          prop: "transferFgInventory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "中转库半品库存",
          prop: "transferSemiInventory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "发运库库存",
          prop: "shippingInventory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "厂内成品库存量",
          prop: "insideFgInventory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "本票需求缺口",
          prop: "demandGap",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "标准单件包装量",
          prop: "standardPackagingQty",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      versionList: [],
      versionCode: "",
      organizationCode: '',
      nowTime: true,
      detailData: [],
      isToExport: false,
      headerList: []
    }
  },
  watch: {},
  created() {
    this.loadData();
    this.tableColumns = this.tableColumnsCopy;
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  async mounted() {
    try {
      await this.targetVersion();
    }catch (e) {
      console.error(e);
    }
    let versionCode = '';
    try {
      versionCode = this.$route.query.versionCode || this.versionList[0]?.value;
    }catch (e) {
      console.error(e);
    }
    this.versionCode = versionCode;
    this.getHighRiskOptions()
    this.getStandardStepList()
    this.QueryComplate()
  },
  methods: {
    async eventFn(res) {
      if (res === "1") {
        this.nowTime = false;
        this.QueryComplate();
        return;
      }
    },
    // 导出数据
    ExportData() {},
    async targetVersion() {
      try {
        let {success, data = []} =  await originDemandVersionList();
        if (success) {
          this.versionList = data;
        }
      }catch (e) {
        console.error(e);
      }
    },
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data) {
        if (data.success) {
          this.$message.success(data.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
      this.getOrgList()
    },
    RowClick(e) {
      if (e) {
        this.$emit("chooseId", e, this.versionCode);
      } else {
        this.$emit("chooseId", "", "");
      }
    },
    getOrgList() {
      // 组织
      orgOption()
        .then((res) => {
          if (res.success) {
            this.orgList = res.data
            this.organizationCode = res.data[0]?.label
          }
        })
        .catch((err) => {})
    },
    changeOrganization(e){
      this.organizationCode = e
      this.QueryComplate()
      this.getStandardStepList()
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions?.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    getStandardStepList(){
      getStandardStepList({organizationCode:this.organizationCode}).then((res) => {
        if(res.success){
          if(this.organizationCode == 'S1'){
            this.headerList = res.data
          } else if(this.organizationCode == 'S2'){
            this.headerList = res.data.slice(1)
          }
        } else {
          this.headerList = []
          this.$message.error(res.msg)
        }
      });
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (this.nowTime) {
        this.nowTime = false;
        return;
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }

      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      if (this.versionCode) {
        let obj = {
          property: "versionId",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.versionCode,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }

      const params = {
        organizationCode: this.organizationCode,
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `highRiskProductOrder/page`;
      const method = "get";
      this.loading = true;
      getTableData(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let { list, total } = response.data;
            const arr = [];
            if (list.length) {
              if (this.headerList) {
                this.headerList.map((row, index) => {
                  arr.push({
                    label: `在产品库存-${row}`,
                    prop: `inventory${index}`,
                    dataType: "NUMERICAL",
                    width: "130",
                    align: "center",
                    fixed: 0,
                    sortBy: 100 + index,
                    showType: "TEXT",
                    fshow: 1,
                  });
                });
              }
              list.forEach((x) => {
                if (x.stepInventories) {
                  x.stepInventories.forEach((value, index) => {
                    x[arr[index].prop] = value;
                  });
                }
              });
              setTimeout(() => {
                let yhltableTableColumns = this.$refs.yhltable.items;
                let yhltableTableColumnsCopy = JSON.parse(JSON.stringify(yhltableTableColumns)) || [];

                yhltableTableColumnsCopy.forEach((item) => {
                  if (item.prop.indexOf('-') > -1 ) {
                    item.fshow = 1;
                  }
                });
                this.tableColumns.forEach((col) => {
                  let item = yhltableTableColumnsCopy.find((x) => {
                    return x.prop == col.prop
                  })
                  if (item) {
                    item.sortBy = col.sortBy
                  }
                })

                this.$refs.yhltable.items = [...yhltableTableColumnsCopy];

                this.tableData = list;
                this.total = total;
              }, 100);

              if (JSON.stringify(this.tableColumns) !== JSON.stringify(this.tableColumnsCopy.concat(arr))) {
                this.tableColumns = this.tableColumnsCopy.concat(arr);
                this.nowTime = true;
                setTimeout(() => {
                  this.nowTime = false;
                }, 200);
              }
            } else {
              this.tableData = [];
              this.total = 0;
            }
          } else {
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions?.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      let ids = this.selectedRows.map((x) => {
        return { versionValue: x.versionValue, id: x.id };
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {},
    getHighRiskOptions(){
      getHighRiskOptions({versionId:this.versionCode}).then((res) => {
        const { oemDropdown, productDropdown, vehicleDropdown } = res.data;
        
        const dropdownData = [
          {
            key: "oemCode",
            values: oemDropdown.map(item => ({
              label: item.label +'('+item.value+')',
              value: item.value
            }))
          },
          {
            key: "productCode",
            values: productDropdown.map(item => ({
              label: item.label,
              value: item.value
            }))
          },
          {
            key: "vehicleModelCode",
            values: vehicleDropdown.map(item => ({
              label: item.label,
              value: item.value
            }))
          }
        ];

        this.enums = [...dropdownData];
      }).catch(error => {
        console.error('获取枚举数据出错:', error);
        this.$message.error('获取枚举数据失败');
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumnsCopy.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    //判断是否为最新，不是最新的话 按照最新版本刷新
    async latestVersion() {
      try {
        await this.targetVersion();
        if(this.versionList?.length > 0 && this.versionCode === this.versionList[0]?.value) {
          return true;
        }else {
          if(this.versionList?.length > 0) {
            this.versionCode = this.versionList[0]?.value || '';
          }
          return false;
        }
      }catch (e) {
        console.error(e);
        return false;
      }
    },
    // 点击显示的时候刷新下拉
    refreshTargetVersion(visible) {
      if(visible) {
        this.targetVersion();
      }
    }
  },
};
</script>
