<template>
  <div class="demandForecastReview" style="height: 100%;" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :query-complate="QueryComplate"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :import-visible="false"
      :export-visible="false"
      :screen-visible="false"
      :column-set-visible="false"
      :cell-set-visible="false"
      :custom-column-visible="false"
      :sort-visible="false"
      :more-visible="true"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :ExportData="ExportData"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :mergeColumms="[{prop:'oemCode'}, {prop:'oemName'}, {prop:'vehicleModelCode'},
        {prop:'externalVehicleModelCode'}, {prop:'oemRemark'}, {prop:'sopEopTime'}, {prop:'accessPosition'}]"
      :requestHeaders="requestHeaders"
      :HaveDynamicColumn="true"
      :ColumnSetVisible="false"
      :showTitle="true"
      :fpagination="true"
    >
      <!-- :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :ExportTemplate="ExportTemplate"
       -->
      <template slot="header">
        <el-select
          size="mini"
          v-model="oemCode"
          filterable
          clearable
          :placeholder="'请选择主机厂'"
          style="margin-right:10px;width: 160px"
        >
          <el-option
            v-for="item in oemCodeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-input style="margin-right:10px;width: 140px" v-model="vehicleModelCode" :placeholder="'请输入内部车型'" size='mini' />
        <el-input style="margin-right:10px;width: 140px" v-model="externalVehicleModelCode" :placeholder="'请输入外部车型'" size='mini'/>
        <el-button size="mini" type="primary" v-debounce="[queryFn]">查 询</el-button>
      </template>

      <!-- <template slot="column" slot-scope="scope">
        <template v-if="scope.row[scope.column.prop]?.includes(',')">
          <div style="font-size: 10px;line-height: 11px;background: #ddd" v-for="(item, index) in scope.row[scope.column.prop]?.split(',')" :key="index">
            {{ item }}
          </div>
        </template>
      </template> -->
    </yhl-table>
  </div>
</template>
<script>
import { dropdownEnum, queryOemInfo } from '@/api/dfpApi/dropdown'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplate,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import { queryForecastSummaryReport, exportData } from "@/api/dfpApi/forecastingPreparation/forecastSummary";
export default {
  name: 'forecastSummary',
  components: {
  },
  props: {
    componentKey: {type: String, default: ''}, // 组件key
    titleName: {type: String, default: ''},
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.dfp}/demandForecastEstablishment/importExcel`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'promotionCalendarDetail',
      },
      // incrementImportData: {
      //   importType: 'INCREMENTAL_IMPORT',
      //   objectType: 'promotionCalendarDetail',
      // },
      tableColumns: [
        {label:'主机厂编码',prop:'oemCode',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'主机厂名称',prop:'oemName',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'内部车型',prop:'vehicleModelCode',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'外部车型',prop:'externalVehicleModelCode',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'类别',prop:'category',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
      ],
      tableColumnsLast: [
        {label:'预测评审备注',prop:'oemRemark',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1},
        {label:'SOP~EOP时间',prop:'sopEopTime',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1},
        {label:'取数装车位置',prop:'accessPosition',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1},
      ],
      tableRow:[{
        label: '客户销量/客户预测',
        prop: 'customerForecastQty',
      },
      {
        label: '业务预测',
        prop: 'bussinessForecastQty',
      },
      {
        label: '同期销量',
        prop: 'sameTimeQty',
      }],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_pro_product_stock_point',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      oemCode: '',
      oemCodeList: [],
      externalVehicleModelCode: '',
      vehicleModelCode: '',
    }
  },
  created() {
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {
    this.queryOemInfo();
  },
  computed: {
    incrementImportData() {
      return { demandCategory: this.demandType }
    },
  },
  methods: {
    queryFn() {
      this.QueryComplate()
    },
    queryOemInfo() {
      queryOemInfo().then((res) => {
        if (res.success) {
          this.oemCodeList = res.data
        }
      })
    },
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
      ExportTemplate('demandForecastEstablishment/exportTemplate','GET')
    },
    ExportData() {
      let form = {
        oemCode: this.oemCode,
        vehicleModelCode: this.vehicleModelCode,
        externalVehicleModelCode: this.externalVehicleModelCode,
        pageNum: 0,
        pageSize: 0
      }
      exportData(form)
    },
      
        //导入
        ImportChange(data, v) {
          console.log(data, v)
          if (data.response) {
            if (data.response.success) {
              this.$message.success(data.response.msg)
              this.QueryComplate()
            } else {
              this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
            }
          }
        },

        // 双击修改
        RowDblClick(row) {
          // this.SelectionChange([row])
          // this.$nextTick(() => {
          //   this.$refs.formDialogRef.editForm()
          // })
        },
        // 新增
        addForm() {
          this.$refs.formDialogRef.addForm()
        },
        // 修改
        editForm() {
          this.$refs.formDialogRef.editForm()
        },
        // 初始化数据
        loadData() {
          this.initParams()
          this.loadUserPolicy()
        },
        initParams() {
          this.getSelectData()
        },
        // 获取表格版本集合
        loadUserPolicy() {
          const params = {
            componentKey: this.componentKey,
          }
          fetchVersions(params, this.componentKey).then((Response) => {
            if (Response.success) {
              this.userPolicy = Response.data
            }
          })
        },
        // 更新配置参数
        setParams(data) {
          this.setCurrentUserPolicy(data.conf)
          this.setCustomColumns(
            data.customExpressions.filter((r) => r.objectType === this.objectType),
          )
          // 增加写入弹框配置
          //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
        },
        // 写入配置
        setCurrentUserPolicy(_config) {
          if (_config && _config.hasOwnProperty('conf')) {
            this.componentId = _config.componentId
            this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
            const params = {
              id: _config.componentId,
              name: _config.versionName,
              default: _config.defaultComponentForUser || 'NO',
              global: _config.global || 'NO',
            }
            this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
          } else {
            this.currentUserPolicy = {}
          }
        },
        // 获取配置
        getCurrentUserPolicy() {
          return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
          // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
          //   // 增加组装弹框配置
          //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
          //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
          //   console.log('conf', conf)
          //   return conf
        },
        // 写入自定义列集合
        setCustomColumns(_customColumns) {
          this.customColumns = JSON.parse(JSON.stringify(_customColumns))
        },
        // 表格查询数据
        QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
          this.loading = true
          queryForecastSummaryReport({
            externalVehicleModelCode: this.externalVehicleModelCode,
            oemCode: this.oemCode,
            pageNum: _pageNum || 1,
            pageSize: _pageSize || 100,
            vehicleModelCode: this.vehicleModelCode,
          }).then(res => {
            this.loading = false
            if (res.success) {
              let data = res.data;
              let list = [];
              data.list[0].headerList.forEach(row => {
                let index = this.tableColumns.findIndex(x => x.prop == row)
                if (index < 0) {
                  this.tableColumns.push({
                    label: row,
                    prop: row + '',
                    dataType: 'CHARACTER',
                    width: '120',
                    align: 'center',
                    fixed: 0,
                    sortBy: 1,
                    showType: 'TEXT',
                    fshow: 1,
                  })
                }
              })
              data.list.forEach(row => {
                this.tableRow.forEach((item) => {
                  let dataRow = {
                    category: item.label,
                    oemCode: row.oemCode,
                    oemName: row.oemName,
                    vehicleModelCode: row.vehicleModelCode,
                    externalVehicleModelCode: row.externalVehicleModelCode,
                    oemRemark: row.oemRemark,
                    sopEopTime: row.sopEopTime,
                    accessPosition: row.accessPosition,
                  };
                  data.list[0].headerList.forEach((m, index) => {
                    dataRow[m] = row.details[index][item.prop]
                  })
                  list.push(dataRow)
                })
              })
              this.$nextTick(() => {
                this.tableData = list;
                this.total = data.total * 3
                
                let _prop = this.tableColumns.find(n => {
                  return n.prop === this.tableColumnsLast[0].prop
                })
                if (!_prop) {
                  this.tableColumns = this.tableColumns.concat(this.tableColumnsLast)
                }
              })
              this.handleResize()
            }
          })
        },
        // 表格版本保存
        UserPolicyComplate(_userPolicy) {
          createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
            if (Response.success) {
              this.loadUserPolicy()
            } else {
              this.$message.error(this.$t('viewSaveFailed'))
            }
          })
        },
        // 自定义列保存
        CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
          _data.componentKey = this.componentKey
          let formData = new FormData()
          Object.keys(_data).forEach((key) => {
            let val = _data[key] || ''
            if (key == 'expressionIcon' && val) {
              val = JSON.stringify(_data[key])
            }
            formData.append(key, val)
          })
          formData.append('objectType', this.objectType)
          updateExpression(formData, this.componentKey, this.objectType).then(
            (Response) => {
              if (Response.success) {
                this.$message({
                  message: this.$t('operationSucceeded'),
                  type: 'success',
                })
                this.ChangeUserPolicy(this.componentId)
                this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
              } else {
                this.$message({
                  message: Response.msg || this.$t('operationFailed'),
                  type: 'error',
                })
              }
            },
          )
        },
        // 自定义列删除
        CustomColumnDel(id) {
          delExpressions(id).then((Response) => {
            if (Response.success) {
              this.ChangeUserPolicy(this.componentId)
            } else {
              this.$message({
                message: Response.msg || this.$t('operationFailed'),
                type: 'error',
              })
            }
          })
        },
        // 切换表格版本
        ChangeUserPolicy(_version) {
          this.componentId = _version
          fetchComponentinfo(_version, this.componentKey).then((Response) => {
            if (Response.success) {
              this.setCustomColumns(
                Response.data.customExpressions.filter(
                  (r) => r.objectType === this.objectType,
                ),
              )
            }
          })
        },
        AddDataFun() {},
        // 编辑数据方法
        EditDataFun(tableData) {},
        // 勾选数据行触发方法
        SelectionChange(v) {
          console.log(v, '勾选的数据11')
          this.selectedRows = JSON.parse(JSON.stringify(v))
          this.selectedRowKeys = v.map((item) => item.id)
        },
        DelRowFun(row) {
          console.log(row)
        },
        DelRowsFun(rows) {
          console.log(rows)
          let ids = this.selectedRowKeys
          // deleteApi(ids)
          //   .then((res) => {
          //     if (res.success) {
          //       this.$message.success(this.$t('deleteSucceeded'))
          //       this.SelectionChange([])
          //       this.QueryComplate()
          //     } else {
          //       this.$message.error(this.$t('deleteFailed'))
          //     }
          //   })
          //   .catch((error) => {
          //     console.log(error)
          //   })
        },
        DeleteData() {},
        // 模糊搜索查询方法
        ScreenColumnVagueSearch(screen, prop, value, _screen) {},
        //获取枚举值
        getSelectData() {
          let enumsKeys = this.initEnums()
          dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
            if (response.success) {
              let data = []
              for (let key in response.data) {
                let item = response.data[key]
                data.push({
                  key: key,
                  values: item,
                })
              }
              data.push(
                JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
              )
              this.enums = data
            }
          })
        },
        //从tableCoumns获取enumKey
        initEnums() {
          let enums = []
          this.tableColumns.map((item) => {
            if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
              enums.push(item.enumKey)
            }
          })
          return enums
        },
        handleResize() {
          this.$refs.yhltable.handleResize()
        },
      },
    }
</script>
<style lang="scss" scoped>
::v-deep .oemCode-tags .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// .table_content_row:has(.redRow){
//   background: #B87F78;
// }
// .table_content_row:has(.blueRow){
//   background: #878ed6;
// }
// .table_content_row:has(.greenRow){
//   background: #9EBD9E;
// }
// ::v-deep{
//   .table_content .table_content_row .table_content_cell{
//     border-top:1px solid #e7e7e7 !important;
//     border-bottom: none !important;
//   }
//   .table_content .table_content_row:last-child .table_content_cell {
//     border-bottom: 1px solid #e7e7e7 !important;
//   }
// }
</style>
