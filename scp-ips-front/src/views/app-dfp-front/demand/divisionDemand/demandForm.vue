<template>
  <div style="height: 100%;">
    <el-dialog
      title="需求提报"
      width="700px"
      :visible.sync="visible"
      v-if="visible"
      append-to-body
      id="dfp-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <div class="batchDialog">
        <el-form
          :model="dialogForm"
          :rules="rules"
          ref="dialogForm"
          label-position="right"
          label-width="120px"
          size="mini"
        >
          <el-form-item label="提报方式" prop="submissionType">
            <el-radio-group
              v-model="dialogForm.submissionType"
              @change="typeChange"
              size="mini"
            >
              <el-radio-button label="API">接口同步</el-radio-button>
              <el-radio-button label="FILE">模板上传</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="dialogForm.submissionType == 'API'"
            label="同步时间范围"
            prop="range"
          >
            <el-date-picker
              v-model="dialogForm.range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            v-if="dialogForm.submissionType == 'FILE'"
            label="主机厂编码"
            prop="oemCode"
          >
            <el-select
              size="small"
              style="width: 220px"
              filterable
              v-model="dialogForm.oemCode"
              placeholder="选择主机厂后可上传"
            >
              <el-option
                v-for="item in oemCodeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="源文件"
            v-if="dialogForm.submissionType == 'FILE'"
          >
            <div class="upload-img-btn">
              <input
                id="upload"
                type="file"
                accept="application/-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                class="upload-input"
                @change="fileUp"
              />
              <el-button icon="el-icon-upload">点击上传</el-button
              ><span>{{ file.name }}</span>
            </div>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" type="primary" v-debounce="[submitForm]">{{ 
            $t("okText")
          }}</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import baseUrl from "@/utils/baseUrl";
import moment from "moment";
import {
  getOemCodeByUserPermission,
  originDemandVersionList,
} from "@/api/dfpApi/versionManage/originDemandVersion";
import {
  demandSubmission,
  queryOemInfoApi,
} from "@/api/dfpApi/demandManagement/divisionDemand";

export default {
  name: "demandForm",
  components: {},
  props: {
    ifShowImportModal: false,
    originVersionId: "",
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      oemCodeOptions: [],
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/newProductTrialSubmission/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "newTrialProduct",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "newTrialProduct",
      },
      visible: false,
      dialogForm: {
        submissionType: "API",
        oemCode: '',
        range: [],
      },
      rules: {
        submissionType: [
          {
            required: true,
            message: this.$t("placeholderSelect"),
            trigger: "change",
          },
        ],
        // range: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
        // oemCode: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
      },
      file: {},
    };
  },
  created() {
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {
    originDemandVersionList().then((res) => {
      this.versionList = res.data;
    });
  },
  watch: {
    ifShowImportModal(val) {
      this.visible = val;
      if (val) {
        // getOemCodeByUserPermission().then(res => {
        //   this.oemCodeOptions = res.data
        // })
        this.queryOemInfoApiFun();
        this.dialogForm.submissionType = "API";
        this.typeChange(this.dialogForm.submissionType);
      }
    },
  },
  methods: {
    queryOemInfoApiFun() {
      queryOemInfoApi().then((res) => {
        if (res.success) {
          this.oemCodeOptions = res.data || [];
        }
      });
    },
    typeChange(e) {
      if (e == "FILE") {
        this.rules = {
          submissionType: [
            {
              required: true,
              message: this.$t("placeholderSelect"),
              trigger: "change",
            },
          ],
          oemCode: [
            {
              required: true,
              message: this.$t("placeholderSelect"),
              trigger: "change",
            },
          ],
        };
      } else {
        this.rules = {
          submissionType: [
            {
              required: true,
              message: this.$t("placeholderSelect"),
              trigger: "change",
            },
          ],
          range: [
            {
              required: true,
              message: this.$t("placeholderSelect"),
              trigger: "change",
            },
          ],
        };
      }
    },
    fileUp(event) {
      const files = event.target.files;
      this.file = files[0];
    },
    handleSuccess(res) {
      if (res.success) {
        this.$message.success(res.msg);
        this.handleClose();
      } else {
        this.$message.error(res.msg);
      }
    },
    handleError(file) {
      this.$message.error("上传失败");
    },
    submitForm() {
      this.$refs["dialogForm"].validate((valid) => {
        if (valid) {
          console.log(this.dialogForm);
          const formData = new FormData();
          if (this.dialogForm.submissionType == "FILE") {
            if (!this.file.name) {
              return this.$message.error("请上传文件");
            }
            formData.append("file", this.file);
            let oemCode = this.dialogForm.oemCode;
            // .join(",")
            formData.append("oemCode", oemCode);
          } else {
            formData.append(
              "beginTime",
              moment(this.dialogForm.range[0]).format("YYYY-MM-DD")
            );
            formData.append(
              "endTime",
              moment(this.dialogForm.range[1]).format("YYYY-MM-DD")
            );
          }
          formData.append("submissionType", this.dialogForm.submissionType);
          demandSubmission(formData).then((res) => {
            if (res.success) {
              this.$message.success(res.msg || "操作成功");
              this.handleClose();
              this.$emit("submitAdd");
            } else {
              this.$message.error(res.msg || "操作失败");
            }
          });
        }
      });
    },
    handleClose() {
      this.dialogForm = {
        submissionType: "API",
        oemCode: '',
        range: [],
      };
      this.file = {};
      this.$emit("closeForm");
    },
  },
};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.upload-input {
  width: calc(100% - 120px);
  height: 32px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  outline: medium none;
  cursor: pointer;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
</style>

<style lang="scss" scoped>
::v-deep .el-icon-close:before {
  content: "" !important;
}
::v-deep .el-row {
  border: none !important;
  .el-form-item {
    width: 100%;
  }
}
</style>
