<template>
  <el-dialog
    title="项目移交量产"
    :visible.sync="dialogVisible"
    width="700px"
    v-if="dialogVisible"
    append-to-body
    v-dialogDrag="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="handover-dialog">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px" size="mini">
        <el-form-item label="量产计划员" prop="orderPlanner">
          <el-select
            style="width:200px"
            v-model="form.orderPlanner"
            placeholder="请选择量产计划员"
            filterable
            @change="handleOrderPlannerChange"
          >
            <el-option
              v-for="item in orderPlanners"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <el-table
        :data="productList"
        border
        style="width: 100%"
        size="mini"
      >
        <el-table-column
          prop="productCode"
          label="产品编码"
          width="180"
        />
        <el-table-column
          prop="productName"
          label="产品名称"
        />
      </el-table>

      <div class="dialog-footer">
        <el-button
          size="mini"
          :loading="loading1"
          :disabled="!form.orderPlanner"
          @click="handleUpdateDeliveryPlan"
        >更新发货计划</el-button>
        <el-button
          size="mini"
          :loading="loading2"
          :disabled="!canUpdateDemandForecast"
          @click="handleUpdateDemandForecast"
        >更新需求预测</el-button>
        <el-button
          size="mini"
          :loading="loading3"
          :disabled="!canUpdateDemandSubmission"
          @click="handleUpdateDemandSubmission"
        >更新装车需求提报</el-button>
        <el-button
          size="mini"
          :loading="loading4"
          :disabled="!canUpdateProduct"
          @click="handleUpdateProduct"
        >更新人员权限</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getOrderPlanners, getPorductDetailList, updateDepliveryPlan, updateDemandForecast, updateDemandSubmission, updateProduct } from '@/api/dfpApi/productHandover/index'
import { loading } from 'vxe-table'

export default {
  name: 'HandoverDialog',
  props: {
    approvalStatus: {
      type: String,
      default: 'NOT_SUBMITTED'
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        orderPlanner: ''
      },
      rules: {
        orderPlanner: [
          { required: true, message: '请选择量产计划员', trigger: 'change' }
        ]
      },
      orderPlanners: [],
      canUpdateDemandForecast: false,
      canUpdateDemandSubmission: false,
      canUpdateProduct: false,
      productList: [],
      handoverId: '',
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
    }
  },
  computed: {
    canHandover() {
      return this.approvalStatus === 'APPROVED'
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.getOrderPlanners()
        this.getPorductDetailList()
      }
    }
  },
  methods: {
    open(e) {
      if (!this.canHandover) {
        this.$message.warning('当前状态不可进行移交操作')
        return
      }
      this.dialogVisible = true
      this.handoverId = e
    },
    async getOrderPlanners() {
      try {
        const res = await getOrderPlanners()
        if (res.success) {
          this.orderPlanners = res.data
        } else {
          this.orderPlanners = []
        }
      } catch (error) {
        console.error(error)
      }
    },
    async getPorductDetailList() {
      try {
        const res = await getPorductDetailList({massProductionHandoverId:this.handoverId})
        if (res.success) {
          this.productList = res.data
        } else {
          this.productList = []
          this.$message.error(res.msg || this.$t('noData'))
        }
      } catch (error) {
        console.error(error)
      }
    },
    handleOrderPlannerChange() {
      this.canUpdateDemandForecast = false
      this.canUpdateDemandSubmission = false
      this.canUpdateProduct = false
    },
    async handleUpdateDeliveryPlan() {
      try {
        this.loading1 = true
        const res = await updateDepliveryPlan({
          orderPlanner: this.form.orderPlanner,
          massProductionHandoverId: this.handoverId
        })
        this.loading1 = false
        if (res.success) {
          this.$message.success('更新发货计划成功')
          this.canUpdateDemandForecast = true
        } else {
          this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0})
        }
      } catch (error) {
        this.loading1 = false
        console.error(error)
      }
    },
    async handleUpdateDemandForecast() {
      try {
        this.loading2 = true
        const res = await updateDemandForecast({
          orderPlanner: this.form.orderPlanner,
          massProductionHandoverId: this.handoverId
        })
        this.loading2 = false
        if (res.success) {
          this.$message.success('更新需求预测成功')
          this.canUpdateDemandSubmission = true
        } else {
          this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0})
        }
      } catch (error) {
        this.loading2 = false
        console.error(error)
      }
    },
    async handleUpdateDemandSubmission() {
      try {
        this.loading3 = true
        const res = await updateDemandSubmission({
          orderPlanner: this.form.orderPlanner,
          massProductionHandoverId: this.handoverId
        })
        this.loading3 = false
        if (res.success) {
          this.$message.success('更新装车需求提报成功')
          this.canUpdateProduct = true
        } else {
          this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0})
        }
      } catch (error) {
        this.loading3 = false
        console.error(error)
      }
    },
    async handleUpdateProduct() {
      try {
        this.loading4 = true
        const res = await updateProduct({
          orderPlanner: this.form.orderPlanner,
          massProductionHandoverId: this.handoverId
        })
        this.loading4 = false
        if (res.success) {
          this.$message.success('更新人员权限成功')
          this.$emit('refresh', this.handoverId)
          this.handleClose()
        } else {
          this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0})
        }
      } catch (error) {
        this.loading4 = false
        console.error(error)
      }
    },
    handleClose() {
      this.$refs.form.resetFields()
      this.dialogVisible = false
      this.canUpdateDemandForecast = false
      this.canUpdateDemandSubmission = false
      this.canUpdateProduct = false
    }
  }
}
</script>

<style lang="scss" scoped>
.handover-dialog {
  .dialog-footer {
    margin-top: 20px;
    text-align: center;
    .el-button {
      margin: 0 10px;
    }
  }
}
</style> 