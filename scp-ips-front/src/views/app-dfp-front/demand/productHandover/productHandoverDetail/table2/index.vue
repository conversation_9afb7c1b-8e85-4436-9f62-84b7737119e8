<template>
  <div class="productInfo">
    <div class="productInfo-header">
      <div class="productInfo-header-title">产品信息</div>
      <div>
        <el-button @click="handleAdd" :disabled="!canEdit">新增</el-button>
      </div>
    </div>
    <el-table
      ref="table"
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
      size="mini"
      v-loading="loading"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="本厂编码" prop="productCode" width="120">
        <template slot-scope="scope">
          <el-select
            class="isrequired"
            v-model="scope.row.productCode"
            placeholder="请选择本厂编码"
            @change="(val) => handleProductCodeChange(val, scope.row, true)"
            filterable
            size="mini"
            :disabled="!canEdit"
          >
            <el-option
              v-for="item in productList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" prop="productName" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.productName" size="mini" disabled />
        </template>
      </el-table-column>
      <el-table-column label="零件号" prop="partNumber" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.partNumber" size="mini" disabled />
        </template>
      </el-table-column>
      <el-table-column label="装车位置(小类)" prop="loadingPositionSub" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.loadingPositionSub" size="mini" disabled />
        </template>
      </el-table-column>
      <el-table-column label="包装方式" prop="productBoxRelationId" width="120">
        <template slot-scope="scope">
          <el-select
            class="isrequired"
            v-model="scope.row.productBoxRelationId"
            placeholder="请选择包装方式"
            @change="(val) => handleboxTypeChange(val, scope.row)"
            size="mini"
            :disabled="!canEdit"
          >
            <el-option
              v-for="item in scope.row.boxTypeOptions"
              :key="item.productBoxRelationId"
              :label="item.label"
              :value="item.productBoxRelationId"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="单片玻璃重量" prop="pieceWeight" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.pieceWeight" size="mini" :disabled="!canEdit" />
        </template>
      </el-table-column>
      <el-table-column label="料箱立项数量" prop="boxWeight" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.boxWeight" size="mini" :disabled="!canEdit" />
        </template>
      </el-table-column>
      <el-table-column label="标准装箱片数" prop="boxSpec" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.boxSpec" size="mini" disabled />
        </template>
      </el-table-column>
      <el-table-column label="箱长" prop="boxLength" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.boxLength" size="mini" disabled />
        </template>
      </el-table-column>
      <el-table-column label="箱宽" prop="boxWidth" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.boxWidth" size="mini" disabled />
        </template>
      </el-table-column>
      <el-table-column label="箱高" prop="boxHeight" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.boxHeight" size="mini" disabled />
        </template>
      </el-table-column>
      <el-table-column label="SOP时间" prop="productSop" width="160">
        <template slot-scope="scope">
          {{ scope.row.productSop ? moment(scope.row.productSop).format("YYYY-MM-DD HH:mm:ss") : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="EOP时间" prop="productEop" width="160">
        <template slot-scope="scope">
          {{ scope.row.productEop ? moment(scope.row.productEop).format("YYYY-MM-DD HH:mm:ss") : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="开发类型" prop="produceType" width="120">
        <template slot-scope="scope">
          <el-select
            class="isrequired"
            v-model="scope.row.produceType"
            placeholder="请选择开发类型"
            size="mini"
            :disabled="!canEdit"
          >
            <el-option
              v-for="item in produceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="零件履历" prop="partRecord" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.partRecord" size="mini" :disabled="!canEdit" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleDelete(scope.$index, scope.row)"
            :disabled="!canEdit"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getProductInfo, delDetailApi } from '@/api/dfpApi/productHandover/index'
import { dropdownEnumCollection } from "@/api/dfpApi/dropdown";
import moment from 'moment';

export default {
  name: 'productInfo',
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    productList: {
      type: Array, default: () => []
    },
    isView: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    approvalStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      selectedRows: [],
      produceTypeOptions: [],
    }
  },
  computed: {
    isHandOver() {
      return this.approvalStatus === 'HAND_OVER'
    },
    canEdit() {
      return this.approvalStatus === 'NOT_SUBMITTED'
    }
  },
  mounted(){
    this.getDevelopOption()
  },
  methods: {
    moment,
    async getDevelopOption() {
      try {
        dropdownEnumCollection(['DEVELOP_TYPE']).then(res => {
          if (res.success) {
            this.produceTypeOptions = res.data[0].values
          } else {
            this.produceTypeOptions = []
          }
        })
      } catch (e) {
        console.error(e);
      }
    },
    handleSelectionChange(val) {
      this.selectedRows = val
    },
    handleAdd() {
      if (this.tableData.length > 0) {
        const lastRow = this.tableData[this.tableData.length - 1];
        const missingFields = [];
        if (!lastRow.productCode) missingFields.push('本厂编码');
        if (!lastRow.productBoxRelationId) missingFields.push('包装方式');
        if (!lastRow.produceType) missingFields.push('开发类型');

        if (missingFields.length > 0) {
          let message = '';
          if (missingFields.length === 1) {
            message = `请选择${missingFields[0]}`;
          } else {
            message = `请选择以下信息：${missingFields.join('、')}`;
          }
          this.$message.warning(message);
          return;
        }
      }
      this.tableData.push({
        productCode: '',
        productName: '',
        partNumber: '',
        loadingPositionSub: '',
        productBoxRelationId: '',
        pieceWeight: '',
        boxWeight: '',
        boxSpec: '',
        boxLength: '',
        boxWidth: '',
        boxHeight: '',
        productSop: '',
        productEop: '',
        produceType: '',
        partRecord: '',
      })
    },
    async handleProductCodeChange(value, row, isInit = false) {
      const isDuplicate = this.tableData.some(item => 
        item !== row && item.productCode === value
      );
      
      if (isDuplicate) {
        this.$message.warning('该本厂编码已被选择，请选择其他编码');
        row.productCode = '';
        return;
      }

      if (!isInit) {
        row.productName = '';
        row.partNumber = '';
        row.loadingPositionSub = '';
        row.productSop = '';
        row.productEop = '';
        row.boxType = ''
        row.boxLength = '';
        row.boxWidth = '';
        row.boxHeight = '';
        row.boxSpec = '';
      }
      
      try {
        const res = await getProductInfo([value])
        if (res.success) {
          const targetItem = res.data.find(item => item.productCode === value);
          row.productName = targetItem.detailInfo.productName
          row.partNumber = targetItem.detailInfo.partNumbers[0].label
          row.loadingPositionSub = targetItem.detailInfo.loadingPositionSub
          row.productSop = targetItem.detailInfo.productSop
          row.productEop = targetItem.detailInfo.productEop
          row.boxTypeOptions = targetItem.detailInfo.boxTypes.map(item => ({
            label: item.boxType+'('+item.boxCode+')',
            productBoxRelationId: item.id,
            boxType: item.boxType,
            standardLoad: item.standardLoad,
            boxLength: item.boxLength,
            boxWidth: item.boxWidth,
            boxHeight: item.boxHeight
          }));
          
          if (row.boxTypeOptions.length > 0) {
            if (isInit && row.productBoxRelationId) {
              const selected = row.boxTypeOptions.find(opt => opt.productBoxRelationId === row.productBoxRelationId)
              if (selected) {
                row.boxType = selected.boxType
                row.boxSpec = selected.standardLoad
                row.boxLength = selected.boxLength
                row.boxWidth = selected.boxWidth
                row.boxHeight = selected.boxHeight
              }
            } else {
              row.productBoxRelationId = row.boxTypeOptions[0].productBoxRelationId;
              row.boxType = row.boxTypeOptions[0].boxType
              row.boxSpec = row.boxTypeOptions[0].standardLoad;
              row.boxLength = row.boxTypeOptions[0].boxLength;
              row.boxWidth = row.boxTypeOptions[0].boxWidth;
              row.boxHeight = row.boxTypeOptions[0].boxHeight;
            }
          }
        } else {
          row.boxTypeOptions = []
          this.$message.warning(res.msg || '查询信息失败')
        }
      } catch (e) {
        console.error(e)
      }
    },
    handleboxTypeChange(value, row) {
      row.boxType = ''
      row.boxSpec = '';
      row.boxLength = '';
      row.boxWidth = '';
      row.boxHeight = '';
      
      if (row.boxTypeOptions && row.boxTypeOptions.length > 0) {
        const selectedOption = row.boxTypeOptions.find(option => option.productBoxRelationId === value);
        if (selectedOption) {
          row.boxType = selectedOption.boxType || '';
          row.boxSpec = selectedOption.standardLoad || '';
          row.boxLength = selectedOption.boxLength || '';
          row.boxWidth = selectedOption.boxWidth || '';
          row.boxHeight = selectedOption.boxHeight || '';
        }
      }
    },
    async handleDelete(index, row) {
      if (row.id) {
        try {
          const res = await delDetailApi([row.id])
          if (res.success) {
            this.$message.success('删除成功')
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(res.msg || '删除失败')
          }
        } catch (e) {
          console.error(e)
        }
      } else {
        this.tableData.splice(index, 1)
      }
    },
    async save() {
      if (!this.detailInfo.handoverNumber) {
        this.$message.warning('请先保存基本信息')
        return false
      }
      try {
        const res = await saveDetail({
          handoverNumber: this.detailInfo.handoverNumber,
          details: this.tableData
        })
        if (res.success) {
          this.$message.success('保存成功')
          return true
        } else {
          this.$message.error(res.msg || '保存失败')
          return false
        }
      } catch (e) {
        console.error(e)
        return false
      }
    },
    getTableData() {
      if (this.tableData.length === 0) {
        this.$message.warning('请至少添加一条产品信息');
        return false;
      }
      
      for (let i = 0; i < this.tableData.length; i++) {
        const row = this.tableData[i];
        if (!row.productCode) {
          this.$message.warning(`第${i + 1}行：请选择本厂编码`);
          return false;
        }
        if (!row.productBoxRelationId) {
          this.$message.warning(`第${i + 1}行：请选择包装方式`);
          return false;
        }
        if (!row.produceType) {
          this.$message.warning(`第${i + 1}行：请选择开发类型`);
          return false;
        }
        if (!row.productSop) {
          this.$message.warning(`第${i + 1}行：请维护本厂编码为${row.productCode}的SOP时间`);
          return false;
        }
        if (!row.productEop) {
          this.$message.warning(`第${i + 1}行：请维护本厂编码为${row.productCode}的EOP时间`);
          return false;
        }
      }
      
      return this.tableData.map(item => {
        const { boxTypeOptions, ...rest } = item;
        return rest;
      })
    },
    async setTableData(data){
      if (this.produceTypeOptions.length === 0) {
        await this.getDevelopOption()
      }
      this.tableData = data
      if (data && data.length > 0) {
        this.$nextTick(() => {
          data.forEach(row => {
            if (row.productCode) {
              this.handleProductCodeChange(row.productCode, row, true)
            }
          })
        })
      }
    },
    setViewMode(isView) {
      this.isView = isView
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .isrequired .el-input__inner,
  .el-select__input {
    background-color: #ffeea8 !important;
  }
::v-deep .el-input__inner:disabled {
    background-color: #f3f3f3 !important;
  }

.productInfo {
  padding: 20px;
}
.productInfo-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 10px;
}
.productInfo-header-title {
  padding-left: 10px;
  font-size:15px
}
</style>
