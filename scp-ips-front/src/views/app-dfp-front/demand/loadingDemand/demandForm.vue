<template>
  <div>
    <el-dialog
      title="需求提报"
      width="600px"
      :visible.sync="visible"
      v-if="visible"
      append-to-body
      id="dfp-dialog"
      v-dialogDrag="true"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div class="batchDialog">
        <el-form
          :model="dialogForm"
          :rules="rules"
          ref="dialogForm"
          label-position="left"
          label-width="100px"
          size="mini"
        >
          <el-form-item label="提报方式" prop="submissionType">
            <el-radio-group
              v-model="dialogForm.submissionType"
              @change="typeChange"
              size="mini"
            >
              <el-radio-button label="API">接口同步</el-radio-button>
              <el-radio-button label="FILE">模板上传</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-row
            v-for="(domain, index) in dialogForm.detailDTOList"
            :key="index"
            style="margin-top: 5px"
            type="flex"
            justify="space-between"
          >
            <template v-if="dialogForm.submissionType=='API'">
              <el-col :span="20">
                <el-form-item
                  :prop="'detailDTOList.' + index + '.oemCode'"
                  label="主机厂编码"
                  :rules="{
                  required: true,
                  message: $t('emptyValidate'),
                  trigger: 'blur',
                }">
                  <el-select
                    v-model="domain.oemCode"
                    clearable
                    filterable
                    multiple
                    collapse-tags
                    size="mini"
                    :placeholder="$t('placeholderSelect')"
                  >
                    <el-option
                      v-for="item in oemCodeOptions"
                      :key="item.oemCode"
                      :label="item.oemName + '(' + item.oemCode + ')'"
                      :value="item.oemCode"
                    >
                    </el-option>
                  </el-select>

                </el-form-item>
                <el-form-item>
                  <el-button @click="submitSyncEdi" :loading="ediLoading">同步接口</el-button>
                  <el-button @click="submitSyncDemand" :loading="demandLoading">同步需求</el-button>
                </el-form-item>
              </el-col>
            </template>

            <template v-else-if="dialogForm.submissionType=='FILE'">
              <el-col :span="12">
                <el-form-item
                  prop="templateType"
                  label="模板格式"
                  clearable
                  size="mini"
                  :rules="{
                    required: true,
                    message: $t('emptyValidate'),
                    trigger: 'blur',
                  }"
                >
                  <el-radio-group v-model="dialogForm.templateType" size="mini">
                    <el-radio label="ROW">横置</el-radio>
                    <el-radio label="COL">竖置</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="需求提报"
                  label-width="90px"
                  :prop="'detailDTOList.' + index + '.submissionFile'"
                  :rules="{
                    required: true,
                    message: $t('emptyValidate'),
                    trigger: 'blur',
                  }">
                  <div class="upload-img-btn">
                    <input
                      id="upload"
                      type="file"
                      accept="application/-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                      class="upload-input"
                      @change="fileUp($event, index, 'submissionFile')"
                    >
                    <el-button icon="el-icon-upload">点击上传</el-button>
                  </div>
                <span class="uploadfile-name" v-if="domain.submissionFile">{{ domain.submissionFile.name }}</span>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <el-form-item
            v-if="dialogForm.submissionType=='FILE'"
            label="需求类型"
            prop="projectDemandType"
            style="width: 260px"
            disabled
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
          <el-select
            v-model="dialogForm.projectDemandType"
            clearable
            size="mini"
            :placeholder="$t('placeholderSelect')"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          </el-form-item>
        </el-form>

        <!-- 新增 -->
         <div class="attach-style" v-show="showMore && dialogForm.submissionType=='FILE'">
          <el-form
            :model="attachForm"
            :rules="attachRules"
            ref="attachForm"
            label-position="left"
            label-width="100px"
            size="mini"
          >
            <el-form-item label="附件" :rules="{required: true}">
              <el-button class="addBtn" v-debounce="[addFormItem]"><i class="el-icon-plus"></i></el-button>
            </el-form-item>
            <el-form-item
              v-for="(attach,index) in attachList"
              class="attach-item-style"
              :key="index"
              label="主机厂编码"
              filterable
              :rules="{
                  required: true,
                  message: $t('emptyValidate'),
                  trigger: 'blur',
                }"
            >
              <el-select
                v-model="attach.oemCode"
                clearable
                filterable
                size="mini"
                width="200px"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in oemCodeOptions"
                  :key="item.oemCode"
                  :label="item.oemName + '(' + item.oemCode + ')'"
                  :value="item.oemCode"
                >
                </el-option>
              </el-select>
              <el-upload
                class="attach-upload"
                v-model="attach.file"
                action="#"
                multiple
                :auto-upload="false"
                :on-remove="(file,fileList)=>handleRemove(file,fileList,index)"
                :on-change="(file,fileList)=>handleChange(file,fileList,index)"
                accept=".xlsx, .xls"
                :file-list="fileList">
                <el-button size="small">点击上传</el-button>
                <!-- <div slot="tip" class="el-upload__tip">只能上传不超过 10MB 的文件</div> -->
              </el-upload>
              <el-button @click="removeFormItem(index)">删除</el-button>
            </el-form-item>
          </el-form>
         </div>

        <span slot="footer" class="dialog-footer" v-if="dialogForm.submissionType=='FILE'">
          <!-- <el-button size="mini" type="primary" @click="submitForm('full')" >{{
            $t("full_import")
          }}</el-button> -->
          <el-button size="mini" type="primary" @click="submitForm('increment')" :loading="loading">{{
            $t("increment_import")
          }}</el-button>
          <el-button size="mini" v-debounce="[handleClose]">取消</el-button>
        </span>

<!--        <span slot="footer" class="dialog-footer" v-else>-->
<!--          <el-button size="mini" type="primary" @click="submitForm" :loading="loading">{{-->
<!--            $t("okText")-->
<!--          }}</el-button>-->
<!--        </span>-->
      </div>
    </el-dialog>
  </div>
</template>
<script>
import baseUrl from "@/utils/baseUrl";
import {
  getOemCodeByUserPermission,
} from "@/api/dfpApi/versionManage/originDemandVersion";
import {
  loadingDemandSubmission,
  uploadFile,
  importDemand,
  uploadAttach,
  uploadOneAttach,
  getDemandTypeEnum,
  syncEdi,
  syncDemand
} from "@/api/dfpApi/businessData/loadingDemandSubmission";
export default {
  name: "demandForm",
  components: {},
  props: {
    ifShowImportModal: false,
    originVersionId: "",
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/newProductTrialSubmission/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "newTrialProduct",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "newTrialProduct",
      },
      visible: false,
      dialogForm: {
        submissionType: "API",
        templateType:"",
        detailDTOList: [{
          oemCode: "",
        }]
      },
      rules: {
        submissionType: [
          {
            required: true,
            message: this.$t("placeholderSelect"),
            trigger: "change",
          },
        ],
      },
      oemCodeOptions: [],
      typeOptions:[],
      file: {},
      loading: false,
      // 新增
      showMore:false,
      attachForm:{},
      attachRules:{},
      attachList:[
        {
          oemCode:'',
          file:[]
        }
      ],
      demandMsg:'',
      tempFile: null,
      ediLoading: false,
      demandLoading: false,
    };
  },
  created() {
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {
    getOemCodeByUserPermission().then((res) => {
      this.oemCodeOptions = res.data;
    });
    getDemandTypeEnum().then(response => {
      if (response.success) {
        this.typeOptions = response.data;
      } else {
        this.$message.warning(response.msg || "该部门没有选择需求类型的权限")
      }
    })
    // let enumsKeys = ['com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum'];
    // dropdownEnum({enumKeys: enumsKeys.join(",")}).then((response) => {
    //   if (response.success) {
    //     this.typeOptions = response.data['com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum'];
    //   }
    // });
  },
  watch: {
    ifShowImportModal:{
      handler(val) {
        this.visible = val;
        if (val) {
          getOemCodeByUserPermission().then((res) => {
            this.oemCodeOptions = res.data;
          });
        }
      },
      deep: true,
      immediate: true
    }

  },
  methods: {
    // 新增
    addFormItem() {
      if(this.attachList.every(item => this.isItemFilled(item)) ){
        this.attachList.push({
          oemCode: '',
          file: [],
        });
      } else {
        this.$message.warning('请在添加新附件之前填写附件列表中的所有项目')
      }
    },
    isItemFilled(item){
      return item.oemCode && item.file.length
    },
    removeFormItem(index) {
      if(this.attachList.length >= 2){
        this.attachList.splice(index, 1);
      } else {
        this.$message.warning('至少添加一个附件')
      }
    },
    handleChange(file, fileList,index){
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      } else {
        this.attachList[index].file = fileList.map(item => item.raw)
      }
    },
    // handleExceed(files, fileList) {
    //   this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    // },
    handleRemove(file, fileList,index) {
      this.attachList[index].file = fileList;
    },
    typeChange(e) {
      this.$nextTick(() => {
        if (e == 'FILE') { //模板上传
          this.dialogForm = {
            submissionType: "FILE",
            templateType:"",
            projectDemandType:"",
            detailDTOList: [{
              submissionFile: '',
            }]
          }
        } else if (e=='API') {
          this.dialogForm = {
            submissionType: "API",
            detailDTOList: [{
              oemCode: [],
            }]
          }
        }
      })
    },
    fileUp(event, index, t) {
      const files = event.target.files;
      this.dialogForm.detailDTOList[index][t] = files[0]
      this.tempFile = files[0]
      if(t=='submissionFile'){
        this.showMore = !this.showMore
      }
    },
    // 上传附件
    async incremantUpload(status) {
      try {
        await this.uploadAttachList(status);
        await this.uploadTempFile(status);
      } catch (error) {
        this.$message({showClose: true, message: error?.message || this.$t("operationFailed"), type: 'error', duration: 0,});
        this.handleClose();
      }
    },

    // 上传附件-ONE
    async uploadAttachList(status) {
      const promises = [];
      this.attachList.forEach(item => {
        if (item.file?.length) {
          item.file.forEach(file => {
            const formData = new FormData();
            formData.append('versionCode', this.originVersionId);
            formData.append('oemCode', item.oemCode);
            formData.append('file', file);
            formData.append('uploadStatus', status?'SUCCESS':'FAIL');
            promises.push(uploadOneAttach(formData));
          });
        }
      });

      return Promise.allSettled(promises);
    },
    // 上传附件-TWO
    async uploadTempFile(status) {
      if (!this.tempFile) return;
      const formData = new FormData();
      formData.append('versionCode', this.originVersionId);
      formData.append('oemCode', '');
      formData.append('file', this.tempFile);
      formData.append('uploadStatus', status?'SUCCESS':'FAIL');
      return uploadOneAttach(formData);
    },

    // 上传需求提报
    uploadDemand(code) {
      let submitData = new FormData();
      submitData.append('importType', code);
      submitData.append('submissionFile', this.dialogForm.detailDTOList[0].submissionFile); // 追加文件
      submitData.append('originVersionId', this.originVersionId);
      submitData.append('templateType', this.dialogForm.templateType);
      submitData.append('projectDemandType', this.dialogForm.projectDemandType);
      submitData.append('checkFlag', 'NO');

      importDemand(submitData)
        .then((res) => {
          if (res.success) {
            this.demandMsg = res.msg;
            this.incremantUpload(true).then(() => {
              this.$message.success(this.demandMsg || this.$t('operationSucceeded'))
              this.handleClose();
            });
          } else {
            this.handleFailure(res.msg, code);
            return;
          }
        })
        .catch((error) => {
          this.handleFailure(this.$t("importFailed"), code);
        });
    },

    handleFailure(message, code) {
      if (message && message.includes('发货数量大于预测数量')) {
        this.$confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          this.retryUploadWithCheckFlag(code);
        })
        .catch(()=>{
          this.handleClose();
        })
      } else {
        this.$message({
          showClose: true,
          message: message || this.$t("importFailed"),
          type: 'error',
          duration: 0,
          dangerouslyUseHTMLString: true,
        });
      }
    },

    retryUploadWithCheckFlag(code) {
      let submitData = new FormData();
      submitData.append('importType', code);
      submitData.append('submissionFile', this.dialogForm.detailDTOList[0].submissionFile);
      submitData.append('originVersionId', this.originVersionId);
      submitData.append('templateType', this.dialogForm.templateType);
      submitData.append('projectDemandType', this.dialogForm.projectDemandType);
      submitData.append('checkFlag', 'YES');

      importDemand(submitData).then((res) => {
        if (res.success) {
          this.demandMsg = res.msg;
          this.incremantUpload(true).then(() => {
            this.$message.success(this.demandMsg);
            this.handleClose();
          });
        } else {
          this.incremantUpload(false).finally(() => {
            this.$message({
              showClose: true,
              message: res.msg || this.$t("importFailed"),
              type: 'error',
              duration: 0,
              dangerouslyUseHTMLString: true,
            });
            this.handleClose();
          })
        }
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: this.$t("importFailed"),
          type: 'error',
          duration: 0,
          dangerouslyUseHTMLString: true,
        });
        this.handleClose();
      })
    },
    // 模板上传 2层校验后 先 uploadDemand 上传需求提报 后 incremantUpload 上传多个附件 附件又分了两步
    submitForm(code) {
      this.$refs["dialogForm"].validate((valid) => {
        if (valid) {
          this.loading = true;
          let formData = JSON.parse(JSON.stringify(this.dialogForm))
          let reqList = []
          if (this.dialogForm.submissionType == "FILE") {
            if(this.attachList.every(item => this.isItemFilled(item))){
              this.uploadDemand(code)
            } else {
              this.$message.warning('请选择主机厂编码并上传附件')
              this.loading = false;
            }
          }else if (this.dialogForm.submissionType =="API") {
            formData.detailDTOList = formData.detailDTOList[0].oemCode.map(item => {
              return { oemCode: item}
            })
            formData.originVersionId = this.originVersionId
            // "versionValue": 0
            // 使用Promise.all等待所有请求完成
            Promise.all([reqList])
              .then((responses) => {
                // console.log(responses)
                setTimeout(() => {
                  loadingDemandSubmission(formData).then((res) => {
                    if (res.success) {
                      this.$message.success(res.msg || "操作成功");
                      this.$emit("closeForm");
                    } else {
                      this.handleClose();
                      this.$message({showClose: true, message: res.msg || this.$t("importFailed"), type: 'error', duration: 0});
                    }
                    this.loading = false;
                  })
                  .catch(error => {
                    this.loading = false;
                  });
                }, 200)
              })
              .catch(() => {
                // this.$message.error("文件上传失败！");
                this.loading = false;
                this.handleClose();
              });
          } else{
            formData.detailDTOList.forEach((item,index) => {
              item.originFile = this.dialogForm.detailDTOList[index].originFile
              item.submissionFile = this.dialogForm.detailDTOList[index].originFile
              // this.dialogForm.detailDTOList[index].originFile
              reqList.push(this.uploadFile(item))
            })
            this.$message.success("操作成功");
            this.$emit("closeForm");
            this.loading = false;
            return
          }
        }
      });
    },
    submitSyncEdi(){
      this.ediLoading= true
        syncEdi().then((res) => {
          if (res.success) {
            this.$message.success(res.msg || "同步成功");
            // this.$emit("closeForm");
          } else {
            this.$message.error(res.msg || "同步失败")
            // this.handleClose();
            // this.$message({showClose: true, message: res.msg || this.$t("importFailed"), type: 'error', duration: 0});
          }
          this.ediLoading= false
        })
    },
    submitSyncDemand(){
      this.$refs["dialogForm"].validate((valid) => {
        if (valid) {
          this.demandLoading = true
          let formData = JSON.parse(JSON.stringify(this.dialogForm))
          formData.detailDTOList = formData.detailDTOList[0].oemCode.map(item => {
            return {oemCode: item}
          })
          formData.originVersionId = this.originVersionId
          syncDemand(formData).then((res) => {
            if (res.success) {
              this.$message.success(res.msg || "同步成功");
              // this.$emit("closeForm");
            } else {
              // this.handleClose();
              this.$message.error(res.msg || "同步失败")
            }
            this.demandLoading = false
          })
        }
      })
    },
    uploadFile(res) {
      let formData = new FormData();
      formData.append("originVersionId", this.originVersionId);
      for (let k in res) {
        formData.append(k, res[k]);
      }
      console.log(this.formData,'formdata-----')
      uploadFile(formData).then((res) => {
        if (!res.success) {
          this.handleClose();
          this.$message({showClose: true, message: res.msg || this.$t("importFailed"), type: 'error', duration: 0});

        }
      })
      .catch(error => {
        this.loading = false;
      });
    },
    //废弃
    removeDomain(index) {
      this.dialogForm.detailDTOList.splice(index, 1);
    },
    //废弃
    addDomain() {
      this.dialogForm.detailDTOList.push({
        oemCode: "",
        contentType: "DAY",
        submissionFile: '',
        originFile: '',
      });
    },
    handleClose() {
      this.dialogForm = {
        submissionType: "API",
        templateType:"",
        detailDTOList: [{
          oemCode: "",
        }]
      }
      this.attachList= [
        {
          oemCode:'',
          file:[]
        }
      ]
      this.showMore = false;
      this.loading = false;
      this.$emit("closeForm");
    },
  },
};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}
.upload-input {
  width: 100%;
  height: 32px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  outline: medium none;
  cursor: pointer;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.uploadfile-name {
  display: inline-block;
  position: absolute;
  left: -70px;
  bottom: -24px;
  width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color:#409EFF;
}
</style>
<style lang="scss">
.el-select__tags-text {
  display: inline-block;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.shift-icon-time {
  font-size: 20px;
  margin: 5px 0 0 10px;
}
.attach-style{
  margin-top: 10px;
}
.addBtn{
  float: right;
}
.attach-item-style{
  .el-form-item__content{
    display: flex;
  }
}
.attach-upload{
  max-width: 300px;
  .el-upload-list{
    margin-left: -290px;
  }
}
</style>
