<template>
  <el-dialog
    append-to-body
    title="版本新建-一致性需求预测"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    id="dfp-dialog"
  >
    <div
      style="
        font-size: 12px;
        font-weight: 600;
        margin-left: 74px;
        margin-bottom: 16px;
      "
    >
      计划周期：{{ planPeriod }}
    </div>
    <div style="font-size: 12px; font-weight: 600; margin-left: 38px;  margin-bottom: 16px;">
      需求预测版本号:<el-select
        v-model="demandForecastVersionId"
        placeholder="请选择"
        style="width: 68%"
        size="small"
      >
        <el-option
          v-for="ele in versionList"
          :key="ele.value"
          :label="ele.label"
          :value="ele.value"
        >
        </el-option>
      </el-select>
    </div>
    <div
        style="
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 14px;
      "
    >
      一致性需求预测版本号：{{ versionCode }}
    </div>
    <Auth url="/dfp/consistenceDemandForecastVersion/reviewVersionFlag">
      <div slot="toolBar">
        <div style="font-size: 12px; font-weight: 600; margin-left: 38px;  margin-bottom: 16px;">
          是否为评审版本:
          <el-radio v-model="reviewVersionFlag" label="YES">
            是
          </el-radio>
          <el-radio v-model="reviewVersionFlag" label="NO">
            否
          </el-radio>
        </div>
      </div>
    </Auth>
    <span slot="footer" class="dialog-footer">
      <el-button v-debounce="[handleClose]">取 消</el-button>
      <el-button
        style="background-color: #1890ff; color: white"
        size="small"
        :loading="loading"
        v-debounce="[handleOk]"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import {
  getConsistenceVersion,
  createVersionNew,
  getTargetVersion,
} from "@/api/dfpApi/forecastingPreparation/demandForecastRelease";
import moment from "moment";
export default {
  data() {
    return {
      planPeriod: "",
      versionList: [],
      dialogVisible: false,
      versionCode: "",
      demandForecastVersionId: "",
      reviewVersionFlag: 'NO',
      loading: false
    };
  },
  methods: {
    handleOk() {
      let params = {
        demandForecastVersionId: this.demandForecastVersionId, //需求预测版本号
        planPeriod: this.planPeriod,
        versionCode: this.versionCode,
        reviewVersionFlag: this.reviewVersionFlag
      };
      this.loading = true;
      createVersionNew(params).then((res) => {
        this.loading = false;
        if (res.success) {
          this.dialogVisible = false;
          this.$message.success(res.msg || "新建成功");
          this.$emit("submitAdd");
        } else {
          this.$message.error(res.msg || "新建失败");
        }
        setTimeout(()=>{
          this.reviewVersionFlag = "NO"
        }, 200)
      }).catch(() => {
        this.loading = false;
      })
    },
    show() {
      // 获取当前时间
      let now = moment();
      // 格式化日期为 YYYYMM
      this.planPeriod = now.format("YYYYMM");
      this.dialogVisible = true;
      this.getVersionCodes();
      this.getConsistenceVersion();
    },

    // 获取需求版本号/
    getVersionCodes() {
      let params = {
        planPeriod: this.planPeriod,
      };
      getTargetVersion(params).then((res) => {
        console.log(res);
        if (res.success) {
          let data = res.data || [];
          this.versionList = data;
          if(data.length > 0) {
            this.demandForecastVersionId = data[0].value;
          }
        }
      });
    },

    // 一致性需求版本号
    getConsistenceVersion() {
      let params = {
        planPeriod: this.planPeriod,
      };
      getConsistenceVersion(params).then((res) => {
        console.log(res);
        const { data = '' } = res;
        if (res.success) {
          this.versionCode = data;
        }
      });
    },

    handleClose() {
      this.dialogVisible = false;
      this.planPeriod = "";
      this.versionCode = "";
      this.demandForecastVersionId = "";
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-icon-close:before {
  content: ""!important;
}

.el-row {
  border: none !important;
  .el-form-item {
    width: 100%;
  }
}

</style>
