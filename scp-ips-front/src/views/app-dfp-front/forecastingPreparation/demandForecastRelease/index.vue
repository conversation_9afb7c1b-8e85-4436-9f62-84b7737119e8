<template>
  <div id="lowCode">
    <yhl-lcdp
      ref="lcdp"
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectDfp"
      :sysElements="this.getSysElements"
      @loaderComponent="loaderComponent"
      @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <Table1
          ref="C001"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C001').name"
          @getTable1Row="getTable1Row"
          @getProduct="getProduct"
        ></Table1>
      </template>
      <template slot="C002" slot-scope="data">
        <Table2
          ref="C002"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C002').name"
          :queryData="queryData"
          :oemCode="oemCode"
          @getTable2Row="getTable2Row"
          @saveData="saveData"
        ></Table2>
      </template>
      <template slot="C003" slot-scope="data">
        <Table3
          ref="C003"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C003').name"
          :queryData="queryData"
          :oemCode="oemCode"
          :productList="productList"
        ></Table3>
      </template>
      <template slot="C004" slot-scope="data">
        <Table4
          ref="C004"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C004').name"
          :queryData="queryData"
          :oemCode="oemCode"
        ></Table4>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
  import Table1 from './table1'
  import Table2 from './table2'
  import Table3 from './table3'
  import Table4 from './table4'
  import {consistenceDemandVersionDetail} from "@/api/dfpApi/versionManage/consistenceDemand";

  export default {
    name: 'demandForecastRelease',
    components: {
      Table1,
      Table2,
      Table3,
      Table4
    },
    data() {
      return {
        componentKey: '',
        customContainers: [],
        queryData: {},
        oemCode: '',
        productList: []
      }
    },
    created() {
      this.initParams()
      this.loadCustomContainers()
    },
    methods: {
      initParams() {
        let key = this.handleComponentKey(this.$route.path);
        this.componentKey = key
      },
      // 初始化自定义内置容器
      loadCustomContainers() {
        this.customContainers.push(
          {
            id: 'C001',
            position: {
              x: 0,
              y: 0,
              w: 50,
              h: 8,
            },
            name: '主机厂层级',
            bindElement: {
              type: 'SYS_BUILTIN_PAGE',
              model: 'SYS_BUILTIN_PAGE',
              config: undefined,
            },
          },
          {
            id: 'C002',
            position: {
              x: 0,
              y: 8,
              w: 50,
              h: 8,
            },
            name: '',
            bindElement: {
              type: 'SYS_BUILTIN_PAGE',
              model: 'SYS_BUILTIN_PAGE',
              config: undefined,
            },
          },
          {
            id: 'C003',
            position: {
              x: 0,
              y: 16,
              w: 50,
              h: 8,
            },
            name: '',
            bindElement: {
              type: 'SYS_BUILTIN_PAGE',
              model: 'SYS_BUILTIN_PAGE',
              config: undefined,
            },
          },
          {
            id: 'C004',
            position: {
              x: 0,
              y: 24,
              w: 50,
              h: 8,
            },
            name: '',
            bindElement: {
              type: 'SYS_BUILTIN_PAGE',
              model: 'SYS_BUILTIN_PAGE',
              config: undefined,
            },
          },
        )
      },
      // 自定义页面自动查询方法
      customPageQuery(item, layoutSetConfig) {
        // let _item = JSON.parse(JSON.stringify(item))
        // if (item.id === 'C001') {
        //   if (
        //     item.bindElement.hasOwnProperty('config') &&
        //     item.bindElement.config.hasOwnProperty('conf')
        //   ) {
        //     _item.bindElement.config.conf.id = layoutSetConfig.conf.version
        //     _item.bindElement.config.componentId = layoutSetConfig.conf.version
        //   }
        //   const params = {
        //     conf: _item.bindElement.config,
        //     customExpressions: layoutSetConfig.customExpressions,
        //   }
        //   this.$refs[item.id].setParams(params)
        // }
      },
      // 自定义页面的获取自定义页面参数方法
      getSlotConfig(item) {
        // if (item.id === 'C001') {
        //   return this.$refs[item.id].getCurrentUserPolicy()
        // }
      },
      customPageResize(item) {
        // if(item.id === 'C002' || item.id === 'C001') {
        //   this.$refs[item.id].$refs.yhltable.handleResize()
        // }
        this.$refs[item.id].handleResize()
      },
      loaderComponent(router, id) {
        Promise.resolve(require('@/' + router).default).then((data) => {
          this.$refs.lcdp.setSysObjComponent(data, id)
        })
      },
      getTable1Row(res, data) {
        this.oemCode = res
        this.queryData = data

        setTimeout(() => {
          this.$refs.C002.selectCoreProcessByOem()
          this.$refs.C004.queryLineChart()
        }, 50)
      },
      getProduct(res){
        this.productList = res
      },
      getTable2Row(res) {
        // if (!res || !res.coreProcess) {
        //   this.$message.error('核心工序/产品特性数据没有维护')
        // }
        // 暂时不用
        // this.$refs.C003.productDropDown(res)
        this.$refs.C003.selectProductByCoreProcess(res)
      },
      saveData() {
        const id = this.queryData.versionCode
        consistenceDemandVersionDetail(id).then(response => {
          if (response.success) {
            if (response.data.versionStatus === "PUBLISHED") {
              this.$message.error("本版已经发布，不允许修改！")
            } else {
              this.$refs.C003.saveData(() => {
                this.$refs.C001.selectOemPage()
                this.$refs.C002.selectCoreProcessByOem()
              })
            }
          }
        })
      }
    },
  }
</script>
<style scoped>
  #lowCode {
    width: 100%;
    height: 100%;
  }
</style>
