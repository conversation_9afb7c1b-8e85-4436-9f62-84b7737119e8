<template>
  <div class="login-container">
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      auto-complete="on"
      label-position="left"
    >
      <div class="title-container">
        <h3 class="title">智能供应链计划系统</h3>
      </div>

      <el-form-item prop="username">
        <el-input
          ref="username"
          v-model="loginForm.username"
          placeholder="请输入用户名"
          name="username"
          type="text"
          tabindex="1"
          auto-complete="on"
        >
          <span
            slot="prefix"
            class="svg-container"
          >
            <svg-icon icon-class="user" />
          </span>
        </el-input>
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          :key="passwordType"
          ref="password"
          v-model="loginForm.password"
          :type="passwordType"
          placeholder="请输入密码"
          name="password"
          tabindex="2"
          auto-complete="on"
        >
          <span
            slot="prefix"
            class="svg-container"
          >
            <i class="el-icon-lock" />
          </span>
          <span
            slot="suffix"
            class="show-pwd"
            @click="showPwd"
          >
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-input>
      </el-form-item>

      <el-form-item prop="authCode">
        <el-input
          ref="authCode"
          v-model="loginForm.authCode"
          placeholder="验证码"
          tabindex="2"
          class="authCode-input"
          @keyup.enter.native="handleLogin"
        />
        <div
          class="authCode"
          @click="getAuthCode"
        >
          <img
            :src="authCode"
            alt=""
            style="width: 100%; height: 100%;"
          />
          <img />
        </div>
      </el-form-item>

      <el-button
        :loading="loading"
        type="primary"
        style="width: 100%; margin-bottom: 20px;"
        @click.native.prevent="handleLogin"
      >
        登录
      </el-button>

      <div class="form-footer">
        <p>瑞之泽高级生产排程AMS</p>
        <p>瑞之泽信息科技（上海）有限公司</p>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getAuthCode, getUserSelect } from '@/api/dfpApi/user'
import { encrypt } from '@/utils/aes'
import Cookies from 'js-cookie'
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: 'yhl',
        password: '123456',
        authCode: null,
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        authCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
        ],
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      authCode: null,
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true,
    },
  },
  mounted() {
    this.getAuthCode()
  },
  methods: {
    getAuthCode() {
      getAuthCode().then((res) => {
        const imageUrl = `data:image/png;base64,${btoa(
          new Uint8Array(res).reduce(
            (data, byte) => data + String.fromCharCode(byte),
            '',
          ),
        )}`
        this.authCode = imageUrl
      })
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.getUserSelect()
          this.loading = true
          const form = new FormData()
          form.append('un', this.loginForm.username)
          form.append('pw', encrypt(this.loginForm.password))
          form.append('authCode', this.loginForm.authCode)
          this.$store
            .dispatch('user/login', form)
            .then((res) => {
              this.loading = false
              if (res.success) {

                // localStorage.setItem('module', 'S&OP')
                // localStorage.setItem('scenario', 'scp_sop')
                // localStorage.setItem('tenant', 'yhl')

                // scp_sys_mps_qubpsjke
                localStorage.setItem('module', 'DFP')
                sessionStorage.setItem('module', 'DFP')
                // localStorage.setItem('scenario', 'scp_sys_mps_phgqkgdp')
                // scp_mps
                // localStorage.setItem('scenario', 'scp_dfp')
                // sessionStorage.setItem('scenario', 'scp_dfp')
                localStorage.setItem('scenario', 'scp_fysh')
                sessionStorage.setItem('scenario', 'scp_fysh')
                localStorage.setItem('tenant', 'shanghai')

                this.$store
                  .dispatch('tagsView/delAllViews')
                  .then(({ visitedViews }) => { })

                this.$router.push({ path: this.redirect || '/' })
                getSysElements() // 存入组件页面配置

              } else {
                this.getAuthCode()
                this.$message.warning(res.msg)
              }
            })
            .catch((res) => {
              this.getAuthCode()
              this.$message.warning(res.msg)
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    getUserSelect() {
      let info = {
        key: 'userSelectEnumKey',
        values: []
      }
      getUserSelect().then((res) => {
        if (res.success) {
          info.values = res.data
        }
        sessionStorage.setItem('userSelectEnumKey', JSON.stringify(info))
      }).catch(() => {
        sessionStorage.setItem('userSelectEnumKey', JSON.stringify(info))
      });
    }
  },
}
</script>

<style lang="scss">
/* reset element-ui css */
.login-container {
  height: 100%;
  // background: no-repeat url('~@/assets/login/background.png');
  background-size: 100% 100%;
  .el-input {
    display: inline-block;
    height: 40px;

    input {
      background: transparent;
      height: 40px;
      padding-left: 36px;
    }
  }
  .authCode-input {
    width: 60%;
    input {
      padding-left: 15px;
    }
  }

  .el-form-item {
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: #fff;
    border-radius: 5px;
    color: #000;
  }
}
</style>

<style lang="scss" scoped>
.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  .login-form {
    position: absolute;
    top: 50%;
    margin-top: -240px;
    right: 12%;
    width: 450px;
    overflow: hidden;
    padding: 40px 40px;
    background-color: #fff;

    .authCode {
      width: 35%;
      height: 40px;
      position: absolute;
      top: 0;
      right: 0;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    color: #000;
    width: 30px;
    display: inline-block;
    font-size: 18px;
  }

  .title-container {
    position: relative;

    .title {
      color: #13426e;
      margin: 15px 0 42px;
      font-weight: 700;
      font-size: 2rem;
    }
  }

  .show-pwd {
    margin-right: 5px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    user-select: none;
  }

  .form-footer {
    font-size: 12px;
    font-weight: 400;
    color: #636363;
    line-height: 12px;
  }
}
</style>
