<template>
  <div style="padding: 20px;" class="ruleConfiguration">
    <div>
      <span>规则配置</span>
    </div>
    <div
      v-if="selectedRows.length > 0"
      style="overflow:auto;height:calc(100vh - 245px"
    >
      <el-collapse v-model="activeName" accordion>
        <el-collapse-item
          :name="idx"
          :key="idx"
          v-for="(rule, idx) in ruleList"
        >
          <template slot="title">
            {{
              ruleList[idx][0].fieldName
                ? ruleList[idx][0].fieldName
                : ruleList[idx][0].column_comment
            }}
            <!-- <i
                    class="header-icon el-icon-s-flag"
                    v-if="ruleList[idx][0].expression || ruleList[idx][0].ruleId"
                  ></i> -->
            <div v-if="ruleList[idx][0].expression || ruleList[idx][0].ruleId">
              <i class="header-icon el-icon-s-flag"></i>
              <span class="badge">{{ ruleList[idx].length }}</span>
            </div>
            <!-- <el-button
                  style="float:right"
                    type="primary"
                    size="mini"
                    @click="handelSaveConf(rule)"
                  >
                    保存
                  </el-button> -->
          </template>
          <div>
            <el-form
              :model="ruleForm"
              :rules="rules"
              ref="ruleForm"
              label-width="80px"
            >
              <!-- <div
                      v-if="
                        ruleList[idx].length &&
                        (ruleList[idx][0].expression || ruleList[idx][0].ruleId)
                      "
                    > -->
              <div>
                <el-row v-for="(item, index) in rule" :key="index">
                  <el-col :span="8">
                    <el-form-item label="规则类型:">
                      <el-select
                        v-model="item.source"
                        placeholder="请选择规则类型"
                        style="width: 100%;"
                        size="mini"
                        clearable
                        @change="handelChangeType(rule, idx, item, index)"
                      >
                        <el-option
                          label="固定规则"
                          value="RULEPOOL"
                        ></el-option>
                        <el-option
                          label="自定义规则"
                          value="CUSTOM"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="规则名称:">
                      <el-input
                        v-if="item.source === 'CUSTOM'"
                        size="mini"
                        style="width: 100%;"
                        v-model="item.ruleName"
                      ></el-input>
                      <!-- <el-select
                          v-model="item.ruleName"
                          placeholder="请选择规则类型"
                          style="width: 100%;"
                          size="mini"
                          v-else
                        >
                        </el-select> -->
                      <el-select
                        v-model="item.ruleId"
                        placeholder="请选择"
                        v-else
                        size="mini"
                        style="width: 100%;"
                        clearable
                      >
                        <el-option
                          v-for="item in expressionList"
                          :key="item.id"
                          :label="item.ruleName"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="6">
                          <el-form-item label="规则符号">
                            <el-select
                              v-model="item.connector"
                              placeholder="请选择规则符号"
                              size="mini"
                              clearable
                            >
                              <el-option label="或" value="OR"></el-option>
                              <el-option label="且" value="AND"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col> -->
                  <el-col :span="5" :offset="1" style="line-height: 37px;">
                    <!-- <el-button
                          type="primary"
                          icon="el-icon-plus"
                          size="mini"
                          circle
                          style="margin: 0 5px;"
                          @click="handelAdd(rule, idx, item, index)"
                        ></el-button> -->
                    <el-button
                      type="warning"
                      icon="el-icon-minus"
                      circle
                      size="mini"
                      style="margin: 0 5px;"
                      @click="handelDelete(rule, idx, item, index)"
                    ></el-button>
                    <el-button
                      type="warning"
                      icon="el-icon-edit"
                      circle
                      size="mini"
                      style="margin-right: 5px;"
                      v-if="item.source === 'CUSTOM'"
                      @click="handelConfig(rule, idx, item, index)"
                    ></el-button>
                    <!-- <el-button
                        type="primary"
                        size="mini"
                        v-if="index === 0"
                        @click="handelSaveConf(rule)"
                      >
                        保存
                      </el-button> -->
                  </el-col>
                </el-row>
              </div>
            </el-form>
            <el-row>
              <div style="display: flex; justify-content: center;">
                <el-button
                  type="primary"
                  size="mini"
                  style="cursor: pointer; margin-right: 10px;"
                  @click="handelSaveConf(rule)"
                >
                  保存规则
                </el-button>
                <el-button
                  type="primary"
                  size="mini"
                  style="cursor: pointer; margin-left: 5px;"
                  @click="handelAdd(rule, idx)"
                >
                  添加规则
                </el-button>
              </div>
            </el-row>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <template>
      <DialogForm
          :formVisible="formVisible"
          :infoForm="infoForm"
          :confIdx="confIdx"
          @editForm="editForm"
          :targetModule="form.targetModule"
          :fieldCode="fieldCode"
          :curObjectType="curObjectType"
          @handelClose="handelClose"
        />
    </template>
  </div>
</template>
<script>
import DialogForm from './dialogForm.vue'
import { getList, getObjectTips } from '@/api/dataVerification/rulesManager'
import {
  getModule,
  getFieldcfgModule,
  getObjects,
  getModulefieldcfg,
  getObjectRule,
  saveObject,
  getFiledRule,
  saveConf,
  deleteObject,
  deleteConf,
  getTableIfo,
} from '@/api/dataVerification/ruleConfiguration'
import { getScript } from '@/api/user'
export default {
  name: 'ruleConfiguration',
  components: {
    DialogForm,
  },
  data() {
    return {
      activeName: '0',
      expressionList: [],
      keyList: [],
      ruleList: [],
      selectNode: {},
      tableData: [],
      moduleList: [],
      modulefieldcfgList: [],
      objectList: [],
      filedList: [],
      form: {},
      defaultProps: {
        children: 'fields',
        label: 'label',
      },
      objData: [],
      selectNode: '',
      ruleForm: {},
      confList: [{ ruleType: '', ruleName: '', symbol: '' }],
      formVisible: false,
      objVisible: false,
      infoForm: {},
      objForm: {},
      confIdx: '',
      rules: {
        companyName: [
          { required: true, message: '不能为空', trigger: 'change' },
        ],
        customSystemName: [
          { required: true, message: '不能为空', trigger: 'change' },
        ],
        model: [{ required: true, message: '不能为空', trigger: 'change' }],
        secretKey: [{ required: true, message: '不能为空', trigger: 'change' }],
        encryptionMethod: [
          { required: true, message: '不能为空', trigger: 'change' },
        ],
      },
      selectedRowKeys: [],
      selectRow: {},
      checkedRow: {},
    }
  },
  props: {
    selectedRows: { type: Array, default: [] },
    curObjectType: { type: String, default: '' },
  },
  created() {
    this.getFieldcfgModule()
    // this.getModule()
    this.getExpressionList()
    this.getScript()
  },
  mounted() {},
  methods: {
    getScript() {
      getScript().then((response) => {
        const { success, data } = response.data || {}
        if (success) {
          sessionStorage.setItem('hintFunction', JSON.stringify(data))
        }
      })
    },
    transformData(arr, data) {
      const result = {}
      arr.forEach((item) => {
        const { fieldName, fieldCode } = item
        const dataItem = data[fieldCode] || []
        const mergedItems = dataItem.map((dataObj) => ({
          ...item,
          ...dataObj,
          fieldName,
        }))
        result[fieldName] =
          mergedItems.length > 0
            ? mergedItems
            : [
                {
                  fieldCode,
                  expression: '',
                  source: '',
                  connector: '',
                  ruleId: '',
                  ruleName: '',
                  fieldName,
                },
              ]
      })
      return result
    },
    transformDataTable(arr, data) {
      const result = {}
      arr.forEach((item) => {
        const { column_comment, column_name } = item
        const dataItem = data[column_name] || []
        const mergedItems = dataItem.map((dataObj) => ({
          ...item,
          ...dataObj,
          column_comment,
        }))
        result[column_name] =
          mergedItems.length > 0
            ? mergedItems
            : [
                {
                  column_name,
                  expression: '',
                  source: '',
                  connector: '',
                  ruleId: '',
                  ruleName: '',
                  column_comment,
                  fieldCode: column_name,
                },
              ]
      })
      return result
    },
    //   保存配置
    handelSaveConf(rule) {
      console.log(rule)
      console.log(this.fieldCode, this.validatorCfgId)
      const params = {
        objectRules: rule,
        fieldCode: rule[0].fieldCode,
        validatorCfgId: rule[0].validatorCfgId
          ? rule[0].validatorCfgId
          : this.validatorCfgId,
      }
      for (var i in params.objectRules) {
        params.objectRules[i].validatorCfgId = params.objectRules[i]
          .validatorCfgId
          ? params.objectRules[i].validatorCfgId
          : this.validatorCfgId
        params.objectRules[i].objectType = params.objectRules[i].objectType
          ? params.objectRules[i].objectType
          : this.selectNode.objectType
        params.objectRules[i].dataSourceType = params.objectRules[i]
          .dataSourceType
          ? params.objectRules[i].dataSourceType
          : this.selectNode.dataSourceType
      }

      if (rule.length === 1 && !rule[0].expression && !rule[0].ruleId) {
        this.$message.warning('暂无规则可以保存')
        return
      }
      saveConf(params).then((res) => {
        if (res.data.success) {
          this.$message.success('操作成功')
        } else {
          this.$message.error(res.data.msg || '操作失败')
        }
      })
    },
    getExpressionList() {
      getList()
        .then((res) => {
          if (res.data.success) {
            this.expressionList = res.data.data
            // this.getObjects()
            // this.tableData = res.data
          }
        })
        .catch((err) => {})
    },
    getObjectTips(targetModule) {
      getObjectTips(targetModule)
        .then((res) => {
          const { data } = res.data || {}
          if (data && data.length > 0) {
            const hintObject = JSON.stringify(data)
            sessionStorage.setItem('hintObject', hintObject)
          }
        })
        .catch((error) => {
          this.$message.error('查询失败!')
        })
    },
    getFiledRule(id) {
      getFiledRule(id)
        .then((res) => {
          if (res.data.success) {
            this.validatorCfgId = id
            const transformedData = this.transformData(
              this.filedList,
              res.data.data,
            )
            this.keyList = Object.keys(transformedData)
            console.log(transformedData, this.keyList)
            this.ruleList = transformedData
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {})
    },
    getFiledRuleTable(id) {
      getFiledRule(id)
        .then((res) => {
          if (res.success) {
            this.validatorCfgId = id
            const transformedData = this.transformDataTable(
              this.filedList,
              res.data,
            )
            this.keyList = Object.keys(transformedData)
            console.log(transformedData, this.keyList)
            this.ruleList = transformedData
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {})
    },

    //   保存对象
    handelSaveObject() {
      saveObject(this.form)
        .then((res) => {
          if (res.success) {
            this.getObjects()
            // this.tableData = res.data
          }
        })
        .catch((err) => {})
    },
    getModulefieldcfg(targetModule, moduleCode, objectType) {
      getModulefieldcfg(
        targetModule ? targetModule : this.form.targetModule,
        moduleCode ? moduleCode : this.form.moduleCode,
        objectType ? objectType : this.form.objectType,
      )
        .then((res) => {
          if (res.data.success) {
            this.filedList = res.data.data
            this.getFiledRule(this.selectedRows[0].id)
          }
        })
        .catch((err) => {})
    },
    getTableIfo(objectType) {
      const formData = new FormData()
      formData.append('tableName', objectType)
      getTableIfo(formData)
        .then((res) => {
          if (res.success) {
            this.filedList = res.data
            this.getFiledRuleTable(this.selectedRows[0].id)
          }
        })
        .catch((err) => {})
    },
    getObjects() {
      getObjects(this.form.moduleCode)
        .then((res) => {
          if (res.success) {
            this.objectList = res.data
          }
        })
        .catch((err) => {})
    },
    getFieldcfgModule() {
      getFieldcfgModule()
        .then((res) => {
          if (res.data.success) {
            this.modulefieldcfgList = res.data.data
          }
        })
        .catch((err) => {})
    },
    getModule() {
      getModule()
        .then((res) => {
          if (res.data.success) {
            this.moduleList = res.data.data
          }
        })
        .catch((err) => {})
    },
    handelAdd(rule, idx) {
      console.log(this.ruleList[idx])
      let obj = {
        source: '',
        ruleName: '',
        ruleId: '',
        expression: '',
        fieldCode: rule[0].fieldCode,
        fieldName: rule[0].fieldName,
        connector: '',
      }
      this.ruleList[idx].push(obj)
      console.log(this.ruleList[idx])
      //   this.confList.splice(index + 1, 0, item)
    },
    handelDelete(rule, idx, item, index) {
      console.log(item, rule)
      if (item.id) {
        deleteConf(item.id).then((res) => {
          if (res.data.success) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
        })
      }
      if (index === 0) {
        this.ruleList[idx][0].expression = ''
        this.ruleList[idx][0].ruleId = ''
        this.ruleList[idx][0].source = ''
        this.ruleList[idx][0].connector = ''
      } else {
        this.ruleList[idx].splice(index, 1)
      }
    },
    handelChangeType(rule, idx, item, index) {
      //   this.ruleList[idx][index].expression = ''
      //   this.ruleList[idx][index].ruleId = ''
      //   this.ruleList[idx][index].ruleName = ''
    },

    //   节点点击事件
    handleNodeClick(data) {
      this.selectNode = data.prop
    },
    initTimeData() {
      this.ruleForm = JSON.parse(JSON.stringify(this.infoForm))
      //   this.$refs['ruleForm'].resetFields()
    },
    // 自定义编辑
    handelConfig(rule, idx, item, index) {
      console.log(item)
      this.formVisible = true
      //   this.infoForm.title = '配置'
      this.infoForm = item
      this.confIdx = idx
      this.ruleIndex = index
    },
    handelClose() {
      this.value = ''
      this.formVisible = false
    },
    editForm(res) {
      this.ruleList[this.confIdx][this.ruleIndex] = res
    },
  },
}
</script>
<style scoped>
.badge {
  background-color: #f56c6c;
  border-radius: 10px;
  color: #fff;
  display: inline-block;
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
  text-align: center;
  white-space: nowrap;
  border: 1px solid #fff;
}
</style>
