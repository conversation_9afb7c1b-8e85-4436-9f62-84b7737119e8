<template>
  <div class="deliveryDynamicTracking" v-loading="loading">
    <div class="title">交 付 动 态 跟 踪 平 台</div>
    <div class="select-list">
      <div class="select-li">
        工厂: 
        <el-select
          style="width: 140px;display:inline-block;"
          v-model="scenario"
          @change="getOptionList"
          size="mini"
          filterable
          :placeholder="$t('selectHolder')"
        >
          <el-option
            v-for="item in scenarioList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div class="select-li">
        生产组织: 
        <el-select
          style="width: 100px;display:inline-block;"
          v-model="orgId"
          size="mini"
          filterable
          @change="standardResourceDropdown"
          :placeholder="$t('selectHolder')"
        >
          <el-option
            v-for="item in orgList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div class="select-li">
        产线组: 
        <el-select
          style="width: 160px;display:inline-block;"
          v-model="standardResourceId"
          size="mini"
          filterable
          @change="selectGroupTask"
          :placeholder="$t('selectHolder')"
        >
          <el-option
            v-for="item in standardResourceIdList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
      <!-- <div class="select-li">
        产线: 
        <el-select
          style="width: 200px;display:inline-block;"
          v-model="physicalResourceId"
          size="mini"
          filterable
          @change="getApiInfo"
          :placeholder="$t('selectHolder')"
        >
          <el-option
            v-for="item in physicalResourceList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div> -->
      <div class="select-li el-tag-list">
        <el-tag
          v-for="item in physicalResourceList"
          :key="item.physicalResourceCode"
          size="medium"
          effect="dark"
          :type="physicalResourceId === item.physicalResourceId ? '' : item.hasTask === 'true' ? 'success' : 'info'"
          @click="getPhysicalResourceId(item.physicalResourceId)"
          style="margin: 0 2px;cursor: pointer;">
          {{ item.physicalResourceCode }}
        </el-tag>
      </div>
    </div>
    <div class="content">
      <div class="left">
        <div style="height: 60%;">
          <div class="left-title" :class="{'left-title-alert': titleAlert }">
            请用企业微信扫码反馈{{ this.tableData1.productCode || 'XXX' }}产品的进度！！！
            <svg-icon icon-class="rightHand" />
            <svg-icon icon-class="rightHand" />
            <svg-icon icon-class="rightHand" @click="audoiTest"/>
          </div>
          <div class="table" style="height: 26%;">
            <table border="1">
              <tr style="height: 51%;background: #f8cbad;">
                <td rowspan="2" width="10%">动态跟踪TOP1</td>
                <td width="20%">本厂编号</td>
                <td width="36%">要求开始~结束时间</td>
                <td width="12%">上道工序产量</td>
                <td width="11%">总交付数量</td>
                <td width="11%">实际产量</td>
              </tr>
              <tr style="height: 49%;">
                <td>{{tableData1.productCode}}</td>
                <td>{{tableData1.startTime ? tableData1.startTime + '~' + tableData1.endTime : ''}}</td>
                <td>{{tableData1.preOperationQuantity}}</td>
                <td>{{tableData1.quantity}}</td>
                <td>{{tableData1.finishedQuantity}}</td>
              </tr>
            </table>
          </div>
          <div class="table" style="height: 48%;">
            <table border="1" style="border-top: 0;">
              <tr style="height: 25%;background: #f6dfd1;">
                <td rowspan="4" width="10%">节点跟踪明细</td>
                <td width="20%">本厂编号</td>
                <td width="48%">要求开始~结束时间</td>
                <td width="11%">总交付数量</td>
                <td width="11%">实际产量</td>
              </tr>
              
              <tr v-for="(item, index) in tableData2" :key="index" style="height: 25%;">
                <td v-if="index === 0" :rowspan="tableData2.length">{{item.productCode}}</td>
                <td>{{item.startTime ? item.startTime + '~' + item.endTime : ''}}</td>
                <td>{{item.plannedQuantity}}</td>
                <td>{{item.finishedQuantity}}</td>
              </tr>
            </table>
          </div>
        </div>
        <div style="height: 35%;margin-top: 5%;">
          <div class="table" style="height: 50%;">
            <table border="1">
              <tr style="height: 51%;background: #c0dbf1;">
                <td rowspan="2" width="10%">动态跟踪TOP2</td>
                <td width="20%">本厂编号</td>
                <td width="48%">要求开始~结束时间</td>
                <td width="11%">总交付数量</td>
                <td width="11%">实际产量</td>
              </tr>
              <tr style="height: 49%;">
                <td>{{tableData3.productCode}}</td>
                <td>{{tableData3.startTime ? tableData3.startTime + '~' + tableData3.endTime : ''}}</td>
                <td>{{tableData3.quantity}}</td>
                <td>{{tableData3.finishedQuantity}}</td>
              </tr>
            </table>
          </div>
          <div class="table" style="height: 50%;">
            <table border="1" style="border-top: 0;">
              <tr style="height: 51%;background: #c0dbf1;">
                <td rowspan="2" width="10%">动态跟踪TOP3</td>
                <td width="20%">本厂编号</td>
                <td width="48%">要求开始~结束时间</td>
                <td width="11%">总交付数量</td>
                <td width="11%">实际产量</td>
              </tr>
              <tr style="height: 49%;">
                <td>{{tableData4.productCode}}</td>
                <td>{{tableData4.startTime ? tableData4.startTime + '~' + tableData4.endTime : ''}}</td>
                <td>{{tableData4.quantity}}</td>
                <td>{{tableData4.finishedQuantity}}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right-top">
           <!-- :style="{ border: qrCodeUrl ? '' : '1px solid #000;' }" -->
          <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="">
        </div>
        <div class="roll-list">
          <!-- <div class="roll-title">滚动通知</div> -->
          <ScrollNotification :messages="messages" />
        </div>
      </div>
    </div>
    <audio class="alert-audio" id="alert-timely" src="../../assets/alert/timely.mp3" loop></audio>
    <audio class="alert-audio" id="alert-extension" src="../../assets/alert/extension.mp3" loop></audio>
  </div>
</template>

<script>
import { listResourceDropDown, orgOption, standardResourceDropdown, physicalResourceDropdown, selectGroupTask,
  selectTaskByPhysicalResourceId, selectSubTaskByTaskId, qrCode, rollingNotificationQuery, alertQuery } from '@/api/deliveryDynamicTracking'
import moment from "moment";
import ScrollNotification from './rollList.vue';
import baseUrl from '@/utils/baseUrl';
// import { wsConnect } from '@/utils/websocket';
export default {
  name: 'deliveryDynamicTracking',
  components: {
    ScrollNotification
  },
  data() {
    return {
      loading: false,
      scenario: '',
      scenarioList: [],
      orgId: '',
      orgList: [],
      standardResourceId: "",
      standardResourceIdList: [],
      physicalResourceId: "",
      physicalResourceList: [],
      tableData1: {},
      tableData2: [{}],
      tableData3: {},
      tableData4: {},
      messages: [],
      interval: null,
      qrCodeUrl: '',
      titleAlert: false,
      test: false,
      taskMap: []
    }
  },
  created() {
    // this.orgOption();
    this.listResourceDropDown();
  },
  beforeDestroy() {
    clearInterval(this.interval); // 清除定时器，防止内存泄漏
  },
  methods: {
    moment,
    audoiTest() {
      if (!this.test) {
        document.getElementById('alert-timely').play();
      } else {
        document.getElementById('alert-timely').pause();
      }
      this.test = !this.test
    },
    listResourceDropDown() {
      listResourceDropDown().then(res => {
        this.scenarioList = res.data
      })
    },
    getOptionList() {
      this.orgOption();
    },
    orgOption() {
      this.orgId = "";
      this.orgList = [];
      this.standardResourceId = "";
      this.standardResourceIdList = [];
      this.physicalResourceId = "";
      this.physicalResourceList = [];
      this.clearInfo();
      orgOption({scenario: this.scenario}).then(res => {
        this.orgList = res.data
      })
    },
    standardResourceDropdown(e) {
      this.standardResourceIdList = []
      this.physicalResourceList = []
      this.standardResourceId = "";
      this.physicalResourceId = "";
      this.clearInfo();
      if (!e) {
        return
      }
      let code = this.orgList.find(n => {
        return n.value == e
      }).label
      standardResourceDropdown({
        organizationCode: code,
        scenario: this.scenario
      }).then((res) => {
        const { success, data, msg } = res
        if (success) {
          this.standardResourceIdList = data
        }
      })
    },
    physicalResourceDropdown(e) {
      this.physicalResourceList = []
      this.physicalResourceId = '';
      this.clearInfo();
      if (!e || !this.orgId) {
        return
      }
      let code = this.orgList.find(n => {
        return n.value == this.orgId
      }).label
      physicalResourceDropdown({
        organizationCode: code,
        standardResourceId: e,
        scenario: this.scenario
      }).then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.physicalResourceList = data
        }
      })
    },
    selectGroupTask(e) {
      this.physicalResourceList = []
      this.physicalResourceId = '';
      this.clearInfo();
      if (!e) {
        return
      }
      selectGroupTask({
        standardResourceId: e,
        scenario: this.scenario
      }).then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.physicalResourceList = data
        }
      })
    },
    getPhysicalResourceId(e) {
      this.physicalResourceId = e
      this.getApiInfo();
    },
    getApiInfo() {
      this.clearInfo();
      this.selectTaskByPhysicalResourceId('1');
      this.rollingNotificationQuery();
      if (this.interval) {
        clearInterval(this.interval)
      } 
      this.interval = setInterval(()=> {
        this.selectTaskByPhysicalResourceId();
        this.rollingNotificationQuery();
      }, 5000); // 每5秒轮询一次
      // this.wsConnect();
    },
    wsConnect() {
      // // 创建ws连接
      // let url = `wss://bpim-test.fuyaogroup.com/api/mps/ws/alert?scenario=${this.scenario}&physicalResourceId=${this.physicalResourceId}`
      // const ws = new WebSocket(url);
      // ws.onopen = () => {
      //     console.log('WebSocket 连接已经建立。');
      //     ws.send('Hello, server!');
      // };
      // ws.onmessage = (event) => {
      //     console.log('收到服务器消息：', event.data);
      // };
      // ws.onerror = (event) => {
      //     console.error('WebSocket 连接出现错误：', event);
      // };
      // ws.onclose = () => {
      //     console.log('WebSocket 连接已经关闭。');
      // }
    },
    selectTaskByPhysicalResourceId(t) {
      if (t === '1') {
        this.loading = true;
      }
      selectTaskByPhysicalResourceId({
        physicalResourceId: this.physicalResourceId,
        scenario: this.scenario
      }).then((res) => {
        if (t === '1') {
          this.loading = false;
        }
        const { success, data,  msg } = res
        if (success && data.length) {
          // console.log('selectTaskByPhysicalResourceId', data)
          this.taskMap = data[0].taskMap
          this.selectSubTaskByTaskId(data[0].id);
          this.alertQuery(data[0].id);
          this.tableData1 = data[0] || {};
          this.setTime(this.tableData1);
          this.tableData3 = data[1] || {};
          this.setTime(this.tableData3);
          this.tableData4 = data[2] || {};
          this.setTime(this.tableData4);
        } else {
          this.clearInfo();
          this.taskMap = []
        }
      })
    },
    setTime(e) {
      if (!e.startTime) {
        return
      }
      e.startTime = moment(e.startTime).format("YYYY-MM-DD HH:mm")
      e.endTime = moment(e.endTime).format("YYYY-MM-DD HH:mm")
    },
    selectSubTaskByTaskId(id) {
      selectSubTaskByTaskId({
        taskId: id,
        scenario: this.scenario
      }).then((res) => {
        const { success, data,  msg } = res
        if (success && data.length) {
          this.tableData2 = JSON.parse(JSON.stringify(data));
          this.tableData2.forEach(e => {
            e.startTime = moment(e.startTime).format("YYYY-MM-DD HH:mm")
            e.endTime = moment(e.endTime).format("YYYY-MM-DD HH:mm")
          })

          if (Date.now() < data[0].startTime) {
            let arr = data.filter(n => {
              return n.plannedQuantity - n.finishedQuantity > 0
            })
            if (arr.length > 0) {
              this.qrCode(arr[0].id)
            } else {
              this.qrCode(data[0].id)
            }
            return
          }

          if (Date.now() > data[data.length - 1].endTime) {
            this.qrCode(data[data.length - 1].id)
            return
          }

          let arr = data.filter(n => {
            return n.startTime < Date.now() && Date.now() < n.endTime && (n.plannedQuantity - n.finishedQuantity > 0)
          })
          if (arr.length > 0) {
            this.qrCode(arr[0].id)
          } else {
            this.qrCode(data[data.length - 1].id)
          }
        } else {
          this.tableData2 = [{}];
        }
      })
    },
    qrCode(id) {
      qrCode({
        trackingBizId: id,
        scenario: this.scenario
      }).then((res) => {
        if (res) {
          this.qrCodeUrl = baseUrl.mps + '/dynamicDeliveryTrackingTask/qrCode?trackingBizId=' + id + '&scenario=' + this.scenario;
        } else {
          this.qrCodeUrl = ''
        }
      })
    },
    rollingNotificationQuery() {
      rollingNotificationQuery({
        physicalResourceId: this.physicalResourceId,
        scenario: this.scenario
      }).then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.messages = data.map((m, index) => {
            return (index + 1) + ':' + m
          })
          // console.log('rollingNotificationQuery', data)
        } else {
          this.messages = []
        }
      })
    },
    alertQuery(id) {
      let formData = new FormData();
      formData.append('physicalResourceId', this.physicalResourceId);
      formData.append('scenario', this.scenario);
      formData.append('taskId', id);
      alertQuery(formData).then((res) => {
        const { success, data,  msg } = res
        if (success) {
          if (data.alertType === 'YELLOW_ALERT') {
            document.getElementById('alert-timely').pause();
            document.getElementById('alert-timely').play();
            this.titleAlert = true
            return;
          }
          if (data.alertType === 'RED_ALERT') {
            // document.getElementById('alert-extension').pause();
            // document.getElementById('alert-extension').play();

            document.getElementById('alert-timely').pause();
            document.getElementById('alert-timely').play();
            this.titleAlert = true
            return;
          }
          if (data.alertType === 'NORMAL') {
            this.titleAlert = false
            document.getElementById('alert-timely').pause();
            // document.getElementById('alert-extension').pause();
            return;
          }

          if (data.alertType === 'ERROR') {
            this.$message.warning(data.message)
            return;
          }

          // {
          //   "alertType": "YELLOW_ALERT",
          //   "message": "时段 [10:00-12:00] 报工未达标，请及时处理！",
          //   "periodKey": "10:00-12:00",
          //   "isAlerting": true
          // }
          // 非最后时段报警
          // {
          //   "alertType": "RED_ALERT",
          //   "message": "时段 [12:00-14:00] 报工未达标，请及时处理！",
          //   "periodKey": "12:00-14:00",
          //   "isAlerting": true
          // }
          // 最后时段报警
          // {
          //   "alertType": "NORMAL",
          //   "message": "时段 [10:00-12:00] 报工已达标！",
          //   "periodKey": "10:00-12:00",
          //   "isAlerting": false
          // }
          // 非最后时段报警取消
          // {
          //   "alertType": "NORMAL",
          //   "message": "时段 [12:00-14:00] 告警已取消！",
          //   "periodKey": "12:00-14:00",
          //   "isAlerting": false
          // }
          // 最后时段报警取消
          // {
          //   "alertType": "ERROR",
          //   "message": "报工逻辑有问题，系统错误！"
          // }
        } else {
        }
      })
    },
    clearInfo() {
      this.tableData1 = {};
      this.tableData2 = [{}];
      this.tableData3 = {};
      this.tableData4 = {};

      this.titleAlert = false
      document.getElementById('alert-timely').pause();

      this.qrCodeUrl = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.deliveryDynamicTracking {
  height: 100%;
  padding: 0 15px;
  overflow: hidden;
  position: relative;
  .title {
    padding: 20px 0 20px;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
  }
  .select-list {
    display: flex;
    justify-content: flex-start;
    .select-li {
      font-size: 13px;
      margin-left: 15px;
      .el-tag--danger {
        background-color: #f00;
        border-color: #f00;
      }
    }
    .el-tag-list {
      display: flex;
      flex-wrap: nowrap;
      max-width: 48vw;
      overflow-x: auto;
      margin-left: 15px;
    }
  }
  .content {
    display: flex;
    height: calc(100% - 120px);
    .left {
      width: 74%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .left-title {
        height: 26%;
        margin-top: 10px;
        padding: 0 30px;
        font-size: 18px;
        background-color: #63b502;
        color: #fff;
        display: flex;
        align-items: center; /* 垂直居中 */
      }
      /* 定义闪烁的动画 */
      @keyframes blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0; }
      }
      /* 应用动画到元素 */
      .left-title-alert {
        background-color: #f00;
        animation: blink 2s infinite; /* 持续时间1秒，无限次重复 */
      }
      .table {
        display: flex;
        height: 37%;
        table {
          border-collapse: collapse; /* 合并边框 */
          box-sizing: content-box;
          width: 100%; /* 表格宽度 */
          text-align: center;
          font-size: 18px;
        }
      }
    }
    .right {
      width: 26%;
      margin-left: 2%;
      margin-top: 10px;
      .right-top {
        height: 58%;
        text-align: center;
        img {
          height: 100%;
        }
      }
      .roll-list {
        height: calc(40% + 4px);
        margin-top: 5px;
        border: 1px solid #000;
      }
    }
  }
  .alert-audio {
    position: absolute;
    width: 1px;
    height: 1px;
    left: 1000px;
  }
}
</style>