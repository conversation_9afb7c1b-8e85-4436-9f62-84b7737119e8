<template>
  <el-dialog
    :title="title"
    width="800px"
    :visible.sync="dialogVisible"
    v-if="dialogVisible"
    append-to-body
    id="ips-bpm-dialog"
    v-dialogDrag="true"
    :before-close="handleClose">
    <div class="batchDialog">
      <div class="batch-search">
        <span class="batch-title">{{$t('bpmGroup_searchUser')}}</span>
        <!-- <el-input
          size="mini"
          :placeholder="$t('bpmGroup_number')"
          v-model="batchSearch.id"
          class="el-input"
          clearable
        /> -->
        <el-autocomplete
          size="mini"
          class="el-input"
          v-model="batchSearch"
          :fetch-suggestions="querySearch"
          placeholder="请输入内容"
          :trigger-on-focus="false"
          @select="handleSelect"
        ></el-autocomplete>
        <!-- <el-button
          size="mini"
          style="margin-left: 14px"
          type="primary"
          @click="handleSearch()"
        >{{$t('searchButton')}}</el-button> -->
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        height="250"
        @selection-change="handleSelectionChange"
        class="batch-table"
        size="mini"
      >
        <el-table-column  type="selection" width="55"></el-table-column>
        <el-table-column prop="id" :label="$t('bpmGroup_number')"></el-table-column>
        <el-table-column :label="$t('bpmGroup_userName')">
          <span slot-scope="scope">
            {{ (scope.row.firstName || '') + (scope.row.lastName || '') }}
          </span>
        </el-table-column>
        <el-table-column prop="email" :label="$t('bpmGroup_mail')"></el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{$t('cancelText')}}</el-button>
      <el-button size="mini" type="primary" @click="submitForm">{{$t('okText')}}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { userList, membership } from '@/api/bpm/user';
export default {
  name: 'bpmModel',
  props: { 
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: this.$t('bpmGroup_bindOrUnbind'),
      batchSearch: '',
      tableData: [],
      originalTableData: [],
      selectList: [],
      groupId: ''
    }
  },
  mounted() {
  },
  methods: {
    getList() {
      if (!this.groupId) {
        return
      }
      const params = {
        groupId: this.groupId
      }
      userList(params)
        .then(res => {
          const { success, data, msg } = res.data || {};
          if (success) {
            this.originalTableData = data
            this.tableData = JSON.parse(JSON.stringify(data))
            this.setSelection()
          } else {
            this.$message.warning(msg || this.$t('noData'))
          }
        })
        .catch(err => {
          this.$message.error(err.data.msg || this.$t('queryFailed'))
        })
    },
    addForm(id) {
      this.groupId = id
      this.dialogVisible = true
      this.getList()
    },
    handleSelectionChange(res) {
      this.selectList = res.map(m => {
        return m.id
      })
    },
    querySearch(res, cb) {
      if (res) {
        let arr = this.originalTableData.filter(e => {
          return e.id.indexOf(res) > -1
        })
        let _arr = arr.map(m => {
          return { label: m.id, value: m.id }
        })
        cb(_arr)
      }
    },
    handleSelect(res) {
      this.tableData.find((row, index) => {
        if (row.id === res.value) {
          this.$refs.multipleTable.bodyWrapper.scrollTop = index * 37
          // setTimeout(() => {
          //   this.$refs.multipleTable.toggleRowSelection(row)
          // }, 400)
        }
        return row.id === res.value
      })
    },
    // 检索
    handleSearch() {
      let name = this.batchSearch.id
      if (!name) {
        this.tableData = this.originalTableData
        this.setSelection()
        return
      }
      this.tableData = this.originalTableData.filter(e => {
        return e.id.indexOf(name) > -1
      })
      this.setSelection()
    },
    setSelection() {
      setTimeout(() => {
        this.$refs.multipleTable.clearSelection()
        this.tableData.forEach(row => {
          if (row.binding) {
            this.$refs.multipleTable.toggleRowSelection(row)
          }
        })
      }, 100)
    },
    handleClose () {
      this.batchSearch = ''
      this.dialogVisible = false
      this.tableData = []
      this.selectList = []
      this.originalTableData = []
      this.groupId = ''
    },
    submitForm() {
      if (this.selectList.length == 0) {
        this.$message.warning(this.$t('selectData'))
        return 
      }
      const info = {
        userIds: this.selectList.join(',') || '',
        pageFrom: 'GROUP',
        groupIds: this.groupId
      }
      membership(info)
      .then(res => {
        const { success, data, msg } = res.data || {};
        if (success) {
          this.$message.success(this.$t('operationSucceeded'))
          this.handleClose()
          this.$emit('submitAdd')
        } else {
          this.$message.warning(msg || this.$t('operationFailed'))
        }
      })
      .catch(err => {
        this.$message.error(err.data.msg || this.$t('operationFailed'))
      })
    }
  }
}
</script>
<style lang="scss">
#ips-bpm-dialog{
  .batchDialog {
    .batch-search {
      display: flex;
      align-items: center;
      .el-input {
        width: 150px;
      }
    }
    .batch-title {
      display: inline-block;
      width: 80px;
      text-align: right;
      margin-left: 10px;
    }
    .batch-table {
      width: 100%;
      border: 1px solid #eee;
      border-bottom: 0;
      margin-top: 15px;
    }
    .el-pagination {
      text-align: right;
    }
  }
} 
</style>