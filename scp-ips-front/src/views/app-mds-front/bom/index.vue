<template>
  <div id="lowCode">
    <yhl-lcdp
      ref="lcdp"
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectMds"
      :sysElements="this.getSysElements"
      @loaderComponent="loaderComponent"
      @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <SearchBar ref="C001" @getInfo="getInfo" @handelPre="handelPre" @handelNext="handelNext"/>
      </template>
      <template slot="C002" slot-scope="data" >
        <Bom
          ref="C002"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C002').name"
          :queryInfo="queryInfo"
          @getRouting="getRouting"
        ></Bom>
      </template>
      <template slot="C003" slot-scope="data">
        <Routing
          ref="C003"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C003').name"
          :routingId="routingId"
        ></Routing>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
import Bom from './top'
import Routing from './bottom'
import SearchBar from './searchBar.vue'

export default {
  name: 'bom',
  components: {
    Bom,
    Routing,
    SearchBar,
  },
  data() {
    return {
      componentKey: '',
      customContainers: [],
      queryInfo: {},
      routingId: '',
    }
  },
  created() {
    this.initParams()
    this.loadCustomContainers()
  },
  methods: {
    initParams() {
      let key = this.handleComponentKey(this.$route.path);
      this.componentKey = key
    },
    // 初始化自定义内置容器
    loadCustomContainers() {
      this.customContainers.push(
        {
          id: 'C001',
          position: {
            x: 0,
            y: 0,
            w: 50,
            h: 2,
          },
          name: 'BOM',
          bindElement: {
            type: 'SYS_BUILTIN_PAGE',
            model: 'SYS_BUILTIN_PAGE',
            config: undefined,
          },
        },
        {
          id: 'C002',
          position: {
            x: 0,
            y: 2,
            w: 50,
            h: 9,
            // h: 18,
          },
          name: 'BOM',
          bindElement: {
            type: 'SYS_BUILTIN_PAGE',
            model: 'SYS_BUILTIN_PAGE',
            config: undefined,
          },
        },
        {
          id: 'C003',
          position: {
            x: 0,
            y: 11,
            w: 50,
            h: 9,
          },
          name: 'BOM树',
          bindElement: {
            type: 'SYS_BUILTIN_PAGE',
            model: 'SYS_BUILTIN_PAGE',
            config: undefined,
          },
        },
      )
    },
    // 自定义页面自动查询方法
    customPageQuery(item, layoutSetConfig) {
      // let _item = JSON.parse(JSON.stringify(item))
      // if (item.id === 'C001' || item.id === 'C002') {
      //   if (item.bindElement.hasOwnProperty('config') && item.bindElement.config.hasOwnProperty('conf')) {
      //     _item.bindElement.config.conf.id = layoutSetConfig.conf.version
      //     _item.bindElement.config.componentId = layoutSetConfig.conf.version
      //   }
      //   const params = {
      //     conf: _item.bindElement.config,
      //     customExpressions: layoutSetConfig.customExpressions
      //   }
      //   this.$refs[item.id].setParams(params)
      //   this.$refs[item.id].QueryComplate()
      // }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig(item) {
      // if (item.id === 'C001' || item.id === 'C002') {
      //   return this.$refs[item.id].getCurrentUserPolicy()
      // }
    },
    customPageResize(item) {
      console.log(item)
      // this.$refs[item.id].$refs.yhltable.handleResize()
    },
    loaderComponent(router, id) {
      Promise.resolve(require('@/' + router).default).then((data) => {
        this.$refs.lcdp.setSysObjComponent(data, id)
      })
    },
    getInfo(e) {
      console.log(e)
      this.queryInfo = e
      setTimeout(() => {
        this.$refs['C002'].search()
        // this.$refs['C002'].getTree()
        // this.$refs['C002'].getBomList()
      }, 100)
    },
    getRouting(e) {
      console.log(e)
      this.routingId = e
    },
    handelPre() {
      this.$refs['C002'].handelPre()
    },
    handelNext() {
      this.$refs['C002'].handelNext()
    },
  },
}
</script>
<style scoped>
#lowCode {
  width: 100%;
  height: 100%;
}
</style>
