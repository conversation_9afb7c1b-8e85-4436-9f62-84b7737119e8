<template>
 <div style="display: inline-block">
    <el-button
      size="medium"
      icon="el-icon-circle-plus-outline"
      v-debounce="[addForm]"
      >{{ $t("addText") }}</el-button
    >
    <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">{{
      $t("editText")
    }}</el-button>
    <yhl-dialog
      ref="yhlDialog"
      :title="titleName"
      :dialogVisible="dialogVisible"
      @handleClose="handleClose"
      @handleComplete="handleComplete"
      :optionSet="optionSet"
      :fields="fields"
      :tabs="tabs"
      :config="config"
      :selectData="selectData"
      :urlObject="this.getUrlObjectMds"
      :objectType="objectType"
      @changeField="changeField"
      :newDefaultDataFun="newDefaultDataFun"
      :itemData="itemData"
      :actionModel="actionModel"
      @setDialogConfig="setConfigSet"
      v-if="dialogVisible"
    >
    </yhl-dialog>
  </div>
</template>
<script>
import { createTransportRouting, updateTransportRouting } from '@/api/mdsApi/transport/index';
import moment from 'moment'
export default {
  name: "yhl-dialog-test",
  props: {
    titleName: { type: String, default: () => "" }, 
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
  },
  data() {
    return {
      dialogVisible: false,
      optionSet: {}, // 设置属性
      fields: [], // form 表单
      tabs: [], // tabs
      config: {}, // 配置
      urlObject: {}, //
      selectData: [], // 下拉数据集合
      objectType: "",
      actionModel: "ADD", // 新增
      itemData: {}, // 修改的时候传递的数据
      fieldsList: [],
    };
  },
  created() {
    this.tabs = [];
    this.fields = [
      {
        prop: "routingCode",
        label: this.$t('transportRouting_routingCode'),
        dataType: "CHARACTER",
        showModel: "INPUT",
        seqNum: 5,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "",
        fnotNull: "YES", // 是否必填
      },
      {
        prop: "effectiveTime",
        label: this.$t('transportRouting_effectiveTime'),
        dataType: "DATE",
        showModel: "DATE",
        seqNum: 7,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "",
        fnotNull: "NO", // 是否必填
      },
      {
        prop: "expiryTime",
        label: this.$t('transportRouting_expiryTime'),
        dataType: "DATE",
        showModel: "DATE",
        seqNum: 7,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "",
        fnotNull: "NO", // 是否必填
      },
      {
        prop: "enabled",
        label: this.$t('enabledText'),
        dataType: "CHARACTER",
        showModel: "SWITCH",
        seqNum: 2,
        fshow: "YES",
        fnotNull: "NO", // 是否必填
        showWidth: "BASE",
        showTabs: "",
      },
      {
        prop: "remark",
        label: this.$t('remarkText'),
        dataType: "CHARACTER",
        showModel: "INPUT",
        seqNum: 9,
        fshow: "YES",
        showWidth: "BASE",
        showTabs: "",
        fnotNull: "NO", // 是否必填
      },
    ];
    this.fieldsList = JSON.parse(JSON.stringify(this.fields));
  },
  methods: {
    // 新增
    addForm() {
      this.actionModel = "ADD";
      this.dialogVisible = true;
    },
    // 修改
    editForm() {
      this.fields = JSON.parse(JSON.stringify(this.fieldsList));
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        return this.$message.warning(this.$t("onlyOneData"));
      }
      let info = this.rowInfo;
      if (info.id) {
        this.actionModel = "EDIT";
        this.dialogVisible = true;
        this.itemData = {
          routingCode: info.routingCode,
          effectiveTime: info.effectiveTime,
          expiryTime: info.expiryTime,
          enabled: info.enabled,
          remark: info.remark,
        }
      }
    },
    handleClose() {
      this.dialogVisible = false;
      this.selectData = []
    },
    // 保存
    handleComplete(obj) {
      let form = JSON.parse(JSON.stringify(obj))
      if (this.actionModel == "ADD") {
        createTransportRouting(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t("addSucceeded"));
            this.handleClose();
            this.$emit("submitAdd");
          } else {
            this.$message.error(res.msg || this.$t("addFailed"));
          }
        });
      }
      if (this.actionModel == "EDIT") {
        form.id = this.rowInfo.id;
        updateTransportRouting(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t("editSucceeded"));
            this.$parent.SelectionChange([]);
            this.handleClose();
            this.$emit("submitAdd");
          } else {
            this.$message.error(res.msg || this.$t("editFailed"));
          }
        });
      }
    },
    // 动态下拉框
    changeField(field, rowData) {
      console.log("changeField", field, rowData);
    },
    // 初始化数据给form赋值
    newDefaultDataFun(resolve) {
      resolve({
        enabled: 'YES'
      });
    },
    // 获取配置
    getConfigSet() {
      return JSON.parse(JSON.stringify(this.config));
    },
    // 更新配置
    setConfigSet(config) {
      if (config !== null && config !== undefined && config !== "") {
        this.config = JSON.parse(JSON.stringify(config));
      }
    },
    mergeArrayByFeild(targetArray, sourceArray, field) {
      const sourceMap = new Map(sourceArray.map(item => [item[field], item]));
      const targetMap = new Map(targetArray.map(item => [item[field], item]));
      targetArray.forEach(item => {
        const sourceItem = sourceMap.get(item[field]);
        if (sourceItem) {
          Object.assign(item, sourceItem);
        }
      })
      sourceArray.forEach(item => {
        const targetItem = targetMap.get(item[field]);
        if (!targetItem) {
          targetArray.push(item)
        }
      })
    },
  },
};
</script>
