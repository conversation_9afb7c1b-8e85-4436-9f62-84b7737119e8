<template>
  <div id="yhl-dialog-test" v-if="dialogVisible">
    <yhl-dialog
      ref="yhlDialog"
      :title="title"
      :dialogVisible="dialogVisible"
      @handleClose="handleClose"
      @handleComplete="handleComplete"
      :optionSet="optionSet"
      :fields="fields"
      :tabs="tabs"
      :config="config"
      :selectData="selectData"
      :urlObject="this.getUrlObjectMds"
      :objectType="objectType"
      @changeField="changeField"
      :newDefaultDataFun="newDefaultDataFun"
      :itemData="itemData"
      :actionModel="actionModel"
      @setDialogConfig="setConfigSet"
      v-if="dialogVisible"
    >
      <!-- <span slot="footer-after">此处可以自定义内容</span> -->
    </yhl-dialog>
  </div>
</template>
<script>
import {
  createSubstituteMaterial,
  updateAlternativeMaterial,
  dropdownStockPoint,
  cascadeDropdownProduct,
  treeDropdownProduct,
  alternativeProductDetailId,
} from "@/api/mdsApi/itemManagement/index";
export default {
  name: "yhl-dialog-test",
  props: {
    standardResourceId: { type: String, default: () => "" },
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
    enums: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      title: "替代组件",
      dialogVisible: false,
      optionSet: {}, // 设置属性
      fields: [], // form 表单
      tabs: [], // tabs
      config: {}, // 配置
      urlObject: {}, //
      selectData: [], // 下拉数据集合
      objectType: "",
      actionModel: "ADD", // 新增
      itemData: {}, // 修改的时候传递的数据
      currencyUnitOptions: [], // 货币单位
      countUnitOptions: [], // 计数单位
      isto: 0,
      specList: [], // 规格
      fieldsList: [],
      treeData: [], // 所属生产组织
    };
  },
  created() {
    this.tabs = [
      {
        id: "basicInformation",
        tabName: this.$t("basicInformation"),
        seqNum: 1,
      },
    ];
    this.fields = [
      {
        prop: "stockPointId",
        label: this.$t("substituteMaterial_stockPointCode"),
        dataType: "CHARACTER",
        showModel: "INPUT",
        dataType: "CHARACTER",
        showModel: "SELECT",
        seqNum: 1,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "basicInformation",
        fnotNull: "YES", // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: false, // 筛选
          model: "DATA", // 枚举   sql   DATA  ENUM
          //   sql: "", // sql比配
          // enumKey: "com.yhl.scp.mds.basic.resource.enums.CapacityTypeEnum", //  key
          valueColumn: "value", // 显示下拉的label的id
          labelColumn: "label", // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      {
        prop: "productId",
        label: this.$t("substituteMaterial_rawProductCode"),
        dataType: "CHARACTER",
        showModel: "INPUT",
        dataType: "CHARACTER",
        showModel: "SELECT",
        seqNum: 2,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "basicInformation",
        fnotNull: "YES", // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: false, // 筛选
          model: "DATA", // 枚举   sql   DATA  ENUM
          //   sql: "", // sql比配
          // enumKey: "com.yhl.scp.mds.basic.resource.enums.CapacityTypeEnum", //  key
          valueColumn: "value", // 显示下拉的label的id
          labelColumn: "label", // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      {
        prop: "altProductId",
        label: this.$t("substituteMaterial_altProductCode"),
        dataType: "CHARACTER",
        showModel: "INPUT",
        dataType: "CHARACTER",
        showModel: "SELECT",
        seqNum: 3,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "basicInformation",
        fnotNull: "YES", // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: false, // 筛选
          model: "DATA", // 枚举   sql   DATA  ENUM
          //   sql: "", // sql比配
          // enumKey: "com.yhl.scp.mds.basic.resource.enums.CapacityTypeEnum", //  key
          valueColumn: "value", // 显示下拉的label的id
          labelColumn: "label", // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      {
        prop: "altRatio",
        label: this.$t("substituteMaterial_altRatio"),
        dataType: "NUMERICAL",
        showModel: "INPUT",
        numericalSet: {
          checkModel: "afterZoreOrZore", // 数值校验规则
        },
        seqNum: 4,
        fshow: "YES",
        fnotNull: "YES", // 是否必填
        showWidth: "BASE",
        showTabs: "basicInformation",
        placeholder: this.$t("placeholderInput"),
        inputOption: {
          append: "%",
        },
      },
      {
        prop: "useMode",
        label: this.$t("substituteMaterial_useMode"),
        dataType: "CHARACTER",
        showModel: "INPUT",
        dataType: "CHARACTER",
        showModel: "SELECT",
        seqNum: 5,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "basicInformation",
        fnotNull: "YES", // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: false, // 筛选
          model: "DATA", // 枚举   sql   DATA  ENUM
          //   sql: "", // sql比配
          // enumKey: "com.yhl.scp.mds.basic.resource.enums.CapacityTypeEnum", //  key
          valueColumn: "value", // 显示下拉的label的id
          labelColumn: "label", // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      {
        prop: "useRatio",
        label: this.$t("substituteMaterial_useRatio"),
        dataType: "NUMERICAL",
        showModel: "INPUT",
        numericalSet: {
          checkModel: "afterZoreOrZore", // 数值校验规则
        },
        seqNum: 6,
        fshow: "YES",
        fnotNull: "NO", // 是否必填
        showWidth: "BASE",
        showTabs: "basicInformation",
        placeholder: this.$t("placeholderInput"),
        inputOption: {
          append: "%",
        },
      },
      {
        prop: "ratioTolerance",
        label: this.$t("substituteMaterial_ratioTolerance"),
        dataType: "NUMERICAL",
        showModel: "INPUT",
        numericalSet: {
          checkModel: "afterZoreOrZore", // 数值校验规则
        },
        seqNum: 7,
        fshow: "YES",
        fnotNull: "NO", // 是否必填
        showWidth: "BASE",
        showTabs: "basicInformation",
        placeholder: this.$t("placeholderInput"),
        inputOption: {
          append: "%",
        },
      },
      {
        prop: "exhaustionStrategy",
        label: this.$t("substituteMaterial_exhaustionStrategy"),
        dataType: "CHARACTER",
        showModel: "INPUT",
        dataType: "CHARACTER",
        showModel: "SELECT",
        seqNum: 8,
        fshow: "YES",
        fnewEdit: "YES",
        fupdateEdit: "YES",
        showWidth: "BASE",
        showTabs: "basicInformation",
        fnotNull: "NO", // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: false, // 筛选
          model: "DATA", // 枚举   sql   DATA  ENUM
          //   sql: "", // sql比配
          // enumKey: "com.yhl.scp.mds.basic.resource.enums.CapacityTypeEnum", //  key
          valueColumn: "value", // 显示下拉的label的id
          labelColumn: "label", // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      {
        prop: "priority",
        label: this.$t("substituteMaterial_priority"),
        dataType: "NUMERICAL",
        showModel: "INPUT",
        numericalSet: {
          checkModel: "afterZoreOrZoreInt", // 数值校验规则
        },
        seqNum: 9,
        fshow: "YES",
        fnotNull: "YES", // 是否必填
        showWidth: "BASE",
        showTabs: "basicInformation",
        placeholder: this.$t("placeholderInput"),
      },

   
      {
        prop: "effectiveTime",
        label: this.$t("substituteMaterial_effectiveTime"),
        dataType: "CHARACTER",
        showModel: "DATETIME",
        seqNum: 10,
        fshow: "YES",
        fnotNull: "NO", // 是否必填
        showWidth: "BASE",
        showTabs: "basicInformation",
        placeholder: this.$t("placeholderInput"),
      },
      {
        prop: "expiryTime",
        label: this.$t("substituteMaterial_expiryTime"),
        dataType: "CHARACTER",
        showModel: "DATETIME",
        seqNum: 11,
        fshow: "YES",
        fnotNull: "NO", // 是否必填
        showWidth: "BASE",
        showTabs: "basicInformation",
        placeholder: this.$t("placeholderInput"),
      },
    
    ];
    this.fieldsList = JSON.parse(JSON.stringify(this.fields));
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.dropdownStockPoint();

         this.enums.forEach((item) => {
          if (item.key == "com.yhl.scp.mds.basic.product.enums.UseModeEnum") {
            let obj = [
            {
              id: "useMode",
              values: item.values,
            },
          ];
          this.mergeArrayByFeild(this.selectData, obj, "id");
          }
          if (
            item.key == "com.yhl.scp.mds.basic.product.enums.ExhaustionStrategyEnum"
          ) {
            let obj = [
            {
              id: "exhaustionStrategy",
              values: item.values,
            },
          ];
          this.mergeArrayByFeild(this.selectData, obj, "id");
          }
        });
      }
    },
  },
  methods: {
     // 获取库存点
     dropdownStockPoint() {
        dropdownStockPoint().then((res) => {
        if (res.success) {
          let arr = res.data;
          let obj = [
            {
              id: "stockPointId",
              values: arr,
            },
          ];
          this.mergeArrayByFeild(this.selectData, obj, "id");
        }
      });
    },

    // 主料
    cascadeDropdownProduct(data) {
        if(!data){
            let obj1 = [
            {
              id: "productId",
              values: [],
            },
          ];
          let obj2= [
            {
              id: "altProductId",
              values: [],
            },
          ];
          this.mergeArrayByFeild(this.selectData, obj1, "id");
          this.mergeArrayByFeild(this.selectData, obj2, "id");
          return
        }
        let params={
            stockPointId:data
        }
        cascadeDropdownProduct(params).then((res) => {
        if (res.success) {
          let arr = res.data;
          let obj1 = [
            {
              id: "productId",
              values: arr,
            },
          ];
          let obj2 = [
            {
              id: "altProductId",
              values: arr,
            },
          ];
          this.mergeArrayByFeild(this.selectData, obj1, "id");
          this.mergeArrayByFeild(this.selectData, obj2, "id");
        }
      });
    },
    mergeArrayByFeild(targetArray, sourceArray, field) {
      const sourceMap = new Map(sourceArray.map((item) => [item[field], item]));
      const targetMap = new Map(targetArray.map((item) => [item[field], item]));
      targetArray.forEach((item) => {
        const sourceItem = sourceMap.get(item[field]);
        if (sourceItem) {
          Object.assign(item, sourceItem);
        }
      });
      sourceArray.forEach((item) => {
        const targetItem = targetMap.get(item[field]);
        if (!targetItem) {
          targetArray.push(item);
        }
      });
    },

    // 获取配置
    getConfigSet() {
      return JSON.parse(JSON.stringify(this.config));
    },
    // 更新配置
    setConfigSet(config) {
      if (config !== null && config !== undefined && config !== "") {
        this.config = JSON.parse(JSON.stringify(config));
      }
    },

    // 新增
    addForm() {
      this.actionModel = "ADD";
      this.dialogVisible = true;
    },
    // 修改 editForm
    editForm() {
      this.fields = JSON.parse(JSON.stringify(this.fieldsList));
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      let info = this.rowInfo;
      for (const key in info) {
        info[key] = this.setValueEmpty(info[key]);
      }
      if (info.id) {
        alternativeProductDetailId(info.id).then((res) => {
          if (res.success) {
            let resData = res.data;
            this.itemData = resData;
            this.actionModel = "EDIT";
            this.dialogVisible = true;
            // 修改
            console.log("修改数据", this.itemData);
            this.itemData.altRatio=this.$mut(this.itemData.altRatio,100)
            this.itemData.useRatio=this.$mut(this.itemData.useRatio,100)
            this.itemData.ratioTolerance=this.$mut(this.itemData.ratioTolerance,100)
            this.cascadeDropdownProduct(this.itemData.stockPointId) 
        }
        });
      }
    },
    setValueEmpty(value) {
      return value === "" || value === null ? undefined : value;
    },
    handleClose() {
      this.dialogVisible = false;
      this.specList = [];
    },
    // 非校验  自己填写
    handleComplete(obj) {
      // TODO 具体的业务处理
      let form = JSON.parse(JSON.stringify(obj));
        form.altRatio=this.$dev(form.altRatio,100)
        form.useRatio=this.$dev(form.useRatio,100)
        form.ratioTolerance=this.$dev(form.ratioTolerance,100)
        if (this.actionModel == "ADD") {
        createSubstituteMaterial(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t("addSucceeded"));
            this.handleClose();
            this.$emit("submitAdd");
          } else {
            this.$message.error(res.msg || this.$t("addFailed"));
          }
        });
      }
      if (this.actionModel == "EDIT") {
        form.id = this.rowInfo.id;
        updateAlternativeMaterial(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t("editSucceeded"));
            this.$parent.SelectionChange([]);
            this.handleClose();
            this.$emit("submitAdd");
          } else {
            this.$message.error(res.msg || this.$t("editFailed"));
          }
        });
      }
    },
    // 动态下拉框
    changeField(field, rowData) {
      console.log("changeField", field, rowData);
      if(field.prop=='stockPointId'){
        rowData.productId=null
        rowData.altProductId=null
        this.cascadeDropdownProduct(rowData.stockPointId)
      }
    },
    // 初始化数据
    newDefaultDataFun(resolve) {
      resolve({
        enabled: "YES",
      });
    },
  },
};
</script>
<style></style>
