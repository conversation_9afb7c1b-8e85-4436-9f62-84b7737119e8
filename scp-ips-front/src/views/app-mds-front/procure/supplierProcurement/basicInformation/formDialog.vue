<template>
  <div style="display: inline-block">
    <el-button
      size="medium"
      icon="el-icon-circle-plus-outline"
      v-debounce="[addForm]"
      >{{ $t("addText") }}</el-button
    >
    <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">{{
      $t("editText")
    }}</el-button>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        v-if="activeName === 'first'"
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="120px"
        size="mini"
      >
        <!-- 物料编码 :label="$t('basicInformation_productCode')"
物料名称 :label="$t('basicInformation_productName')"
时间颗粒度 :label="$t('basicInformation_timeUnit')"
开始日 :label="$t('basicInformation_startDate')"
结束日 :label="$t('basicInformation_endDate')" -->
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item
              :label="$t('basicInformation_productCode')"
              prop="productId"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.productId"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in productOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="$t('basicInformation_timeUnit')">
              <el-select
                style="width: 100%"
                v-model="ruleForm.timeUnit"
                size="small"
                clearable
                filterable
                @change="timeChange"
                :placeholder="$t('placeholderInput')"
              >
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item :label="$t('basicInformation_startDate')">
              <el-date-picker
                style="width: 100%"
                size="small"
                @change="dateChange"
                v-model="ruleForm.startDate"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('placeholderTime')"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="$t('basicInformation_endDate')">
              <el-date-picker
                style="width: 100%"
                size="small"
                @change="dateChange"
                v-model="ruleForm.endDate"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('placeholderTime')"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item :label="$t('basicInformation_enabled')">
              <el-switch size="small" disabled v-model="ruleForm.enabled"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]">{{
          $t("cancelText")
        }}</el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">{{
          $t("okText")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  createSupplierProduct,
  updateSupplierProduct,
} from "@/api/mdsApi/basicInformation/index";
import TreeSelect from "@/components/TreeSelect/index.vue";
import { unitList } from "@/api/mdsApi/unit"; // 单位维护
import { dropdownEnum } from "@/api/mdsApi/itemManagement/index"; // 枚举单位
import { treeDropdownProduct } from "@/api/mdsApi/itemManagement/index";
import moment from "moment";
export default {
  name: "supplierProcurement",
  components: {
    TreeSelect,
  },
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
  },
  data() {
    return {
      activeName: "first",
      dialogVisible: false,
      title: "",
      ruleForm: {
        productId: undefined,
        timeUnit: undefined,
        startDate: undefined,
        endDate: undefined,
        enabled: false,
      },
      rules: {
        productId: [
          {
            required: true,
            message:
              this.$t("placeholderSelect") +
              this.$t("basicInformation_productCode"),
            trigger: "blur",
          },
        ],
      },
      timeOptions: [], // 时间单位
      productOptions: [], // 物品
    };
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        // 单位
        this.timeOptions = [];
        this.getUnitList();
        // 物品
        this.productOptions = [];
        this.getTreeDropdownProduct();
      }
    },
  },
  mounted() {
    // this.dropdownEnum(
    //   "com.yhl.scp.mds.basic.resource.enums.ResourceTypeEnum",
    //   "resourceTypeOptions"
    // );
  },
  methods: {
    // 时间控制
    timeChange() {
      if (this.ruleForm.timeUnit) {
        this.ruleForm.startDate = undefined;
        this.ruleForm.endDate = undefined;
      }
    },
    dateChange() {
      if (this.ruleForm.startDate || this.ruleForm.endDate) {
        this.ruleForm.timeUnit = undefined;
      }
    },
    // 返回全部的单位
    getUnitList() {
      //   let params = {
      //     pageNum: 1,
      //     pageSize: 10000,
      //   };
      this.timeOptions = [];
      //   unitList(params).then((res) => {
      //     if (res.success) {
      //       this.unitOptions = res.data.list;
      //       if (this.unitOptions.length) {
      //         this.unitOptions.forEach((item) => {
      //           // 获取时间单位
      //           if (item.unitType == "TIME") {
      //             this.timeOptions.push(item);
      //             item.unitLableCode = `${item.unitDesc}(${item.unitCode})`;
      //             if (!this.ruleForm.timeUnit && item.primaryUnit == "YES") {
      //               this.ruleForm.timeUnit = item.id;
      //             }
      //           }
      //         });
      //       }
      //     }
      //   });
      let enumKeys = "com.yhl.scp.mds.basic.enums.TimeUnitEnum";
      dropdownEnum({ enumKeys }).then((res) => {
        if (res.success) {
          this.timeOptions = res.data[enumKeys];
          this.timeOptions = this.timeOptions.filter(
            (item) => item.value != "DECADE" && item.value != "TEN_DAY"
          );
          console.log(this.timeOptions, "单位维护");
        }
      });
    },
    // 物品代码
    getTreeDropdownProduct() {
      let info = {
        expandDepth: 0,
      };
      treeDropdownProduct(info)
        .then((res) => {
          if (res.success) {
            this.productOptions = res.data;
          }
        })
        .catch((err) => {});
    },
    addForm() {
      this.dialogVisible = true;
      this.title = this.$t("addText");
    },
    editForm() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      this.dialogVisible = true;
      this.title = this.$t("editText");
      const info = this.rowInfo;
      if (info.id) {
        this.ruleForm = {
          productId: info.productId,
          timeUnit: info.timeUnit,
          startDate: info.startDate
            ? moment(info.startDate).format("yyyy-MM-DD")
            : undefined,
          endDate: info.endDate
            ? moment(info.endDate).format("yyyy-MM-DD")
            : undefined,
          enabled: info.enabled == "YES" ? true : false,
        };
      }
    },
    setValueEmpty(value) {
      return value === "" || value === null ? undefined : value;
    },
    getSelectTree(val) {
      this.ruleForm.organizationId = val[0];
    },
    handleClose() {
      this.dialogVisible = false;
      this.activeName = "first";
      this.ruleForm = {
        productId: undefined,
        timeUnit: undefined,
        startDate: undefined,
        endDate: undefined,
        enabled: false,
      };
      this.$refs["ruleForm"].resetFields();
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm));
          form.enabled = form.enabled ? "YES" : "NO";
          if (form.startDate && form.endDate) {
            if (form.startDate > form.endDate) {
              return this.$message.error(
                this.$t("basicInformation_startDate") +
                  this.$t("unableGreaterThan") +
                  this.$t("basicInformation_endDate")
              );
            }
          }
          if (this.title == this.$t("addText")) {
            createSupplierProduct(form)
              .then((res) => {
                if (res.success) {
                  this.$message.success(this.$t("addSucceeded"));
                  this.handleClose();
                  this.$emit("submitAdd");
                } else {
                  this.$message.error(res.msg || this.$t("addFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("addFailed"));
              });
            return;
          }
          if (this.title == this.$t("editText")) {
            form.id = this.rowInfo.id;
            updateSupplierProduct(form)
              .then((res) => {
                if (res.success) {
                  this.$message.success(this.$t("editSucceeded"));
                  this.$parent.SelectionChange([]);
                  this.handleClose();
                  this.$emit("submitAdd");
                } else {
                  this.$message.error(res.msg || this.$t("editFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("editFailed"));
              });
            return;
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
