import Layout from "@/layout";
import { formateRouter } from '@/utils/formateRouter'
const dfpRouter = [
  // {
  //   path: "/base/*",
  //   name: "base",
  //   children: [{ path: "**", component: Layout }],
  // },
  {
    path: "/basicParameters",
    component: Layout,
    redirect: "/basicParameters",
    meta: { title: "基础参数", icon: "el-icon-s-claim" },
    children: [

      {
        path: "/economicBatchProduction",
        name: "economicBatchProduction",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/economicBatchProduction/index"),
        meta: { title: "生产经济批量", keepAlive: true },
      },

      {
        path: "/basicParameters/partsSummary",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/partsSummary/index.vue"),
        name: "partsSummary",
        meta: { title: "零件需求汇总", keepAlive: true },
      },
      {
        path: "/basicParameters/partMapping",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/partMapping/index.vue"),
        name: "partMapping",
        meta: { title: "零件映射关系", keepAlive: true },
      },
      {
        path: "/basicParameters/inventoryData",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/inventoryData/index.vue"),
        name: "inventoryData",
        meta: { title: "实时库存数据", keepAlive: true },
      },
      {
        path: "/basicParameters/mainPlantLineData",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/mainPlantLineData/index.vue"),
        name: "mainPlantLineData",
        meta: { title: "主机厂产线资源", keepAlive: true },
      },
      // {
      //   path: "/basicParameters/stockInfo",
      //   component: () =>
      //   import("@/views/app-dfp-front/basicParameters/stockInfo/index.vue"),
      //   name: "stockInfo",
      //   meta: { title: "主机厂库存信息", keepAlive: true },
      // },
      {
        path: "/basicParameters/mainPlantVehicleInfo",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/mainPlantVehicleInfo/index.vue"),
        name: "mainPlantVehicleInfo",
        meta: { title: "主机厂车型信息", keepAlive: true },
      },

      {
        path: "/basicParameters/vehicleModelMapping",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/vehicleModelMapping/index.vue"),
        name: "vehicleModelMapping",
        meta: { title: "主机厂车型映射", keepAlive: true },
      },
      {
        path: "/basicParameters/oemStockPointMap",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/oemStockPointMap/index.vue"),
        name: "oemStockPointMap",
        meta: { title: "主机厂与库存点关系", keepAlive: true },
      },
      {
        path: "/basicParameters/oemSchedule",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/oemSchedule/index.vue"),
        name: "oemSchedule",
        meta: { title: "主机厂运输时间表", keepAlive: true },
      },
      {
        path: "/basicParameters/lineMap",
        component: () => import("@/views/app-dfp-front/basicParameters/lineMap/index.vue"),
        name: "lineMap",
        meta: { title: "产线映射", keepAlive: true },
      },

      {
        path: "/basicParameters/algorithmGeneralConfiguration",
        component: () =>
          import(
            "@/views/app-dfp-front/basicParameters/algorithmGeneralConfiguration/index.vue"
          ),
        name: "algorithmGeneralConfiguration",
        meta: { title: "算法通用配置", keepAlive: true },
      },
      {
        path: "/basicParameters/featureLibrary",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/featureLibrary/index.vue"),
        name: "featureLibrary",
        meta: { title: "特征库", keepAlive: true },
      },
      {
        path: "/basicParameters/seasonalCoefficient",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/seasonalCoefficient/index.vue"),
        name: "seasonalCoefficient",
        meta: { title: "季节性系数", keepAlive: true },
      },
      //   {
      //     path: '/basicParameters/featureLibrary/index',
      //     component: () => import('@/views/app-dfp-front/basicParameters/featureLibrary/index.vue'),
      //     name: 'featureLibrary',
      //     meta: { title: '服务水平系数',keepAlive: false}
      //   },
      {
        path: "/basicParameters/customDistribution",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/customDistribution/index.vue"),
        name: "customDistribution",
        meta: { title: "自定义分布", keepAlive: true },
      },
      {
        path: "/basicParameters/festivalHoliday",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/festivalHoliday/index.vue"),
        name: "festivalHoliday",
        meta: { title: "节假日", keepAlive: true },
      },
      {
        path: "/basicParameters/modelLibrary",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/modelLibrary/index.vue"),
        name: "modelLibrary",
        meta: { title: "模型库", keepAlive: true },
      },
    ],
  },
  {
    path: "/foundation",
    component: Layout,
    meta: { title: "基础信息", icon: "nested" },
    redirect: "/foundation",
    children: [
      {
        path: "/foundation/oem",
        component: () => import("@/views/app-dfp-front/foundation/oem/index"),
        name: "oem",
        meta: { title: "主机厂", keepAlive: true },
      },
      {
        path: "/foundation/saleOrganize",
        component: () => import("@/views/app-dfp-front/foundation/saleOrganize/index"),
        name: "saleOrganize",
        meta: { title: "销售组织", keepAlive: true },
      },
    ]
  },
  {
    path: "/requirementPreprocessing",
    component: Layout,
    redirect: "/requirementPreprocessing",
    meta: { title: "需求预处理", icon: "el-icon-s-claim" },
    children: [
      {
        path: "/requirementPreprocessing/promotionCalendar",
        component: () =>
          import(
            "@/views/app-dfp-front/requirementPreprocessing/promotionCalendar/index.vue"
          ),
        name: "promotionCalendar",
        meta: { title: "促销日历", keepAlive: true },
      },
      {
        path: "/requirementPreprocessing/productFuturePrice",
        component: () =>
          import(
            "@/views/app-dfp-front/requirementPreprocessing/productFuturePrice/index.vue"
          ),
        name: "productFuturePrice",
        meta: { title: "产品未来价格", keepAlive: true },
      },
      {
        path: "/requirementPreprocessing/correlationProduct",
        component: () =>
          import(
            "@/views/app-dfp-front/requirementPreprocessing/correlationProduct/index.vue"
          ),
        name: "correlationProduct",
        meta: { title: "物料关联关系", keepAlive: true },
      },
      {
        path: "/requirementPreprocessing/newProductPlan",
        component: () =>
          import("@/views/app-dfp-front/requirementPreprocessing/newProductPlan/index.vue"),
        name: "newProductPlan",
        meta: { title: "新品计划", keepAlive: true },
      },
      //   {
      //     path: '/requirementPreprocessing/distributionPlan',
      //     component: () =>
      //       import(
      //         '@/views/app-dfp-front/requirementPreprocessing/distributionPlan/index.vue'
      //       ),
      //     name: 'distributionPlan',
      //     meta: { title: '分货计划', keepAlive: true },
      //   },
      {
        path: "/requirementPreprocessing/historicalDemand",
        component: () =>
          import("@/views/app-dfp-front/requirementPreprocessing/historicalDemand/index.vue"),
        name: "historicalDemand",
        meta: { title: "历史需求", keepAlive: true },
      },
      {
        path: "/requirementPreprocessing/requirementCleaning",
        component: () =>
          import(
            "@/views/app-dfp-front/requirementPreprocessing/requirementCleaning/index.vue"
          ),
        name: "requirementCleaning",
        meta: { title: "需求清洗", keepAlive: true },
      },
      {
        path: "/requirementPreprocessing/annualDemandTarget",
        component: () =>
          import(
            "@/views/app-dfp-front/requirementPreprocessing/annualDemandTarget/index.vue"
          ),
        name: "annualDemandTarget",
        meta: { title: "年度需求目标", keepAlive: true },
      },
    ],
  },
  {
    path: "/versionManage",
    component: Layout,
    redirect: "/versionManage",
    meta: { title: "版本管理", icon: "el-icon-s-claim" },
    children: [
      {
        path: "/versionManage/originalVersion",
        component: () =>
          import("@/views/app-dfp-front/versionManage/originalVersion/index.vue"),
        name: "originalVersion",
        meta: { title: "原始需求版本", keepAlive: true },
      },
      {
        path: "/versionManage/daily",
        component: () => import("@/views/app-dfp-front/versionManage/dailyVersion/index.vue"),
        name: "dailyVersion",
        meta: { title: "日需求版本", keepAlive: true },
      },
      {
        path: "/versionManage/rollingVersion",
        component: () =>
          import("@/views/app-dfp-front/versionManage/rollingVersion/index.vue"),
        name: "rollingVersion",
        meta: { title: "滚动预测版本", keepAlive: true },
      },
      {
        path: "/versionManage/algorithmForecast",
        component: () =>
          import("@/views/app-dfp-front/versionManage/algorithmForecast/index.vue"),
        name: "algorithmForecast",
        meta: { title: "算法预测版本", keepAlive: true },
      },
      {
        path: "/versionManage/deliveryPlanVersion",
        component: () =>
          import("@/views/app-dfp-front/versionManage/deliveryPlanVersion/index.vue"),
        name: "deliveryPlanVersion",
        meta: { title: "发货计划版本", keepAlive: true },
      },
      {
        path: "/versionManage/demandVersion",
        component: () =>
          import("@/views/app-dfp-front/versionManage/demandVersion/index.vue"),
        name: "demandVersion",
        meta: { title: "需求预测版本", keepAlive: true },
      },
      {
        path: "/versionManage/consistenceDemand",
        component: () =>
          import("@/views/app-dfp-front/versionManage/consistenceDemand/index.vue"),
        name: "consistenceDemand",
        meta: { title: "一致性需求预测版本", keepAlive: true },
      },
    ],
  },
  {
    path: "/supplyAndDemandMatching",
    component: Layout,
    redirect: "/supplyAndDemandMatching",
    meta: { title: "供需匹配", icon: "el-icon-s-claim" },
    children: [
      {
        path: "/supplyAndDemandMatching/grossCapacity",
        component: () =>
          import("@/views/app-dfp-front/supplyAndDemandMatching/grossCapacity/index.vue"),
        name: "grossCapacity",
        meta: { title: "粗能力维护", keepAlive: true },
      },
      //   {
      //     path: '/supplyAndDemandMatching/forecastResult',
      //     component: () =>
      //       import('@/views/app-dfp-front/supplyAndDemandMatching/forecastResult/index.vue'),
      //     name: 'forecastResult',
      //     meta: { title: '组织日产能', keepAlive: true },
      //   },
      {
        path: "/supplyAndDemandMatching/organizationDailyCapacity",
        component: () =>
          import(
            "@/views/app-dfp-front/supplyAndDemandMatching/organizationDailyCapacity/index.vue"
          ),
        name: "organizationDailyCapacity",
        meta: { title: "组织日产能", keepAlive: true },
      },
      {
        path: "/supplyAndDemandMatching/supplyAllocation",
        component: () =>
          import("@/views/app-dfp-front/supplyAndDemandMatching/supplyAllocation/index.vue"),
        name: "supplyAllocation",
        meta: { title: "渠道供应分配", keepAlive: true },
      },
    ],
  },
  {
    path: "/business",
    component: Layout,
    redirect: "/business",
    meta: { title: "业务信息", icon: "el-icon-s-claim" },
    children: [
      // {
      //   path: "/business/industryData",
      //   component: Layout,
      //   redirect: "/business/industryData",
      //   meta: { title: "行业信息", icon: "el-icon-s-claim" },
      //   children: [
      //   ],
      // },
      {
        path: "/business/industryData/autoSales",
        component: () =>
          import("@/views/app-dfp-front/business/industryData/autoSales/index.vue"),
        name: "autoSales",
        meta: { title: "乘用车销量与售价", keepAlive: true },
      },
      {
        path: "/business/industryData/industryNews",
        component: () =>
          import("@/views/app-dfp-front/business/industryData/industryNews/index.vue"),
        name: "industryNews",
        meta: { title: "行业资讯搜索", keepAlive: true },
      },
      {
        path: "/business/industryData/marketShare",
        component: () =>
          import("@/views/app-dfp-front/business/industryData/marketShare/index.vue"),
        name: "marketShare",
        meta: { title: "车型供货份额（特定位置）", keepAlive: true },
      },
      {
        path: "/business/market",
        component: () => import("@/views/app-dfp-front/business/market/index"),
        name: "market",
        meta: { title: "市场信息", keepAlive: true },
      },
      {
        path: "/business/policyInformation",
        component: () => import("@/views/app-dfp-front/business/policyInformation/index"),
        name: "market",
        meta: { title: "政策信息", keepAlive: true },
      },
      {
        path: "/business/holdings",
        component: () => import("@/views/app-dfp-front/business/holdings/index"),
        name: "holdings",
        meta: { title: "车型市场保有量", keepAlive: true },
      },
      {
        path: "/business/oemInventory",
        component: () => import("@/views/app-dfp-front/business/oemInventory/index"),
        name: "oemInventory",
        meta: { title: "主机厂库存信息", keepAlive: true },
      },
      {
        path: "/business/oemInventorySubmission",
        component: () => import("@/views/app-dfp-front/business/oemInventorySubmission/index"),
        name: "oemInventorySubmission",
        meta: { title: "中转库与主机厂库存提报", keepAlive: true },
      },
      {
        path: "/deliveryPlan/selectInventoryAndDelivery",
        component: () => import("@/views/app-dfp-front/deliveryPlan/selectInventoryAndDelivery/index"),
        name: "selectInventoryAndDelivery",
        meta: { title: "查询工序库存与日计划发货量", keepAlive: true },
      },
    ],
  },
  {
    path: "/businessData",
    component: Layout,
    meta: { title: "业务数据", icon: "nested" },
    redirect: "/businessData/market",
    children: [
      {
        path: "/businessData/warehouseRelease",
        component: () => import("@/views/app-dfp-front/businessData/warehouseRelease/index"),
        name: "warehouseRelease",
        meta: { title: "仓库收发货数据", keepAlive: true },
      },
      {
        path: "/businessData/subsideWarehouseRelease",
        component: () => import("@/views/app-dfp-front/businessData/subsideWarehouseRelease/index"),
        name: "subsideWarehouseRelease",
        meta: { title: "子公司至中转库收发货数据", keepAlive: true },
      },
    ],
  },
  {
    path: "/demandForecast",
    component: Layout,
    redirect: "/demandForecast",
    meta: { title: "需求预测", icon: "el-icon-s-claim" },
    children: [
      {
        path: "/demandForecast/demandForecastReview",
        component: () => import("@/views/app-dfp-front/demandForecast/demandForecastReview/index.vue"),
        name: "demandForecastReview",
        meta: { title: "需求预测评审", keepAlive: true },
      },
      {
        path: "/forecastingPreparation/executionMonitoring",
        component: () =>
          import("@/views/app-dfp-front/forecastingPreparation/executionMonitoring/index.vue"),
        name: "executionMonitoring",
        meta: { title: "执行监控", keepAlive: true },
      },
      {
        path: "/preparationDemand",
        name: "preparationDemand",
        component: () =>
          import("@/views/app-dfp-front/basicParameters/preparationDemand/index"),
        meta: { title: "需求预测编制", keepAlive: true },
      },
      {
        path: "/historyForecast",
        name: "historyForecast",
        component: () =>
          import("@/views/app-dfp-front/demandForecast/historyForecast/index"),
        meta: { title: "历史需求预测", keepAlive: true },
      },
      {
        path: "/sourceFileManage",
        name: "sourceFileManage",
        component: () =>
          import("@/views/app-dfp-front/demandForecast/sourceFileManage/index"),
        meta: { title: "源文件管理", keepAlive: true },
      },
      {
        path: "/highRiskReview",
        name: "highRiskReview",
        component: () =>
          import("@/views/app-dfp-front/demandForecast/highRiskReview/index"),
        meta: { title: " 高风险产品订单评审", keepAlive: true },
      },
      {
        path: "/transportRoutingResource",
        name: "transportRoutingResource",
        component: () =>
          import("@/views/app-dfp-front/demandForecast/transportRoutingResource/index"),
        meta: { title: "运输路径与资源关系", keepAlive: true },
      },
      {
        path: "/forecastingPreparation/forecastReview",
        component: () =>
          import("@/views/app-dfp-front/forecastingPreparation/forecastReview/index.vue"),
        name: "forecastReview",
        meta: { title: "预测复盘", keepAlive: true },
      },
      {
        path: "/forecastingPreparation/demandForecastRelease",
        component: () =>
          import("@/views/app-dfp-front/forecastingPreparation/demandForecastRelease/index.vue"),
        name: "demandForecastRelease",
        meta: { title: "需求预测发布", keepAlive: true },
      },
      {
        path: "/forecastSummary",
        name: "forecastSummary",
        component: () =>
          import("@/views/app-dfp-front/demandForecast/forecastSummary/index"),
        meta: { title: "预测汇总表", keepAlive: true },
      },
      {
        path: "/demandForecast/calendar",
        component: () =>
          import("@/views/app-dfp-front/demandForecast/calendar/index.vue"),
        name: "calendar",
        meta: { title: "装车日历", keepAlive: true },
      },
    ]
  },
  {
    path: "/forecastAlgorithm",
    component: Layout,
    redirect: "/forecastAlgorithm",
    meta: { title: "预测算法", icon: "el-icon-s-claim" },
    children: [
      {
        path: "/forecastAlgorithm/algorithmResult",
        component: () => import("@/views/app-dfp-front/forecastAlgorithm/algorithmResult/index.vue"),
        name: "algorithmResult",
        meta: { title: "预测算法结果", keepAlive: true }, // 此页面keepAlive不能为true
      },
      {
        path: "/forecastAlgorithm/factorAnalysis",
        component: () => import("@/views/app-dfp-front/forecastAlgorithm/factorAnalysis/index.vue"),
        name: "factorAnalysis",
        meta: { title: "预测因子分析", keepAlive: true },
      },
    ]
  },
  {
    path: "/riskLevel",
    component: Layout,
    redirect: "/riskLevel",
    meta: { title: "风险等级", icon: "el-icon-s-claim" },
    children: [
      {
        path: "/riskLevel/oem",
        component: () => import("@/views/app-dfp-front/riskLevel/oem/index.vue"),
        name: "riskLevelOem",
        meta: { title: "主机厂风险等级", keepAlive: true },
      },
      {
        path: "/riskLevel/material",
        component: () => import("@/views/app-dfp-front/riskLevel/material/index.vue"),
        name: "riskLevelMaterial",
        meta: { title: "零件风险等级", keepAlive: true },
      },
    ],
  },
  {
    path: "/demand",
    component: Layout,
    redirect: "/demand",
    meta: { title: "需求管理", icon: "el-icon-s-claim" },
    children: [
      // {
      //   path: "/demand/productHandover",
      //   component: () => import("@/views/app-dfp-front/demand/productHandover/productHandoverList/index.vue"),
      //   name: "productHandover",
      //   meta: { title: "量产移交", keepAlive: true },
      // },
      // {
      //   path: "/demand/productHandoverDetail",
      //   component: () => import("@/views/app-dfp-front/demand/productHandover/productHandoverDetail/index.vue"),
      //   name: "productHandoverDetail",
      //   meta: { title: "量产移交详情", keepAlive: true },
      // },
      {
        path: "/demand/massProductionHandoverLog",
        component: () => import("@/views/app-dfp-front/demand/productHandover/massProductionHandoverLog/index.vue"),
        name: "massProductionHandoverLog",
        meta: { title: "量产移交日志", keepAlive: true },
      },
      {
        path: "/demand/productHandover",
        component: () => import("@/views/app-dfp-front/demand/productHandover/productHandoverList/index.vue"),
        name: "productHandover",
        meta: { title: "量产移交", keepAlive: true },
      },
      {
        path: "/demand/productHandoverDetail",
        component: () => import("@/views/app-dfp-front/demand/productHandover/productHandoverDetail/index.vue"),
        name: "productHandoverDetail",
        meta: { title: "量产移交详情", keepAlive: true },
      },
      {
        path: "/demand/loadingDemand",
        component: () => import("@/views/app-dfp-front/demand/loadingDemand/index.vue"),
        name: "loadingDemand",
        meta: { title: "装车需求提报", keepAlive: true },
      },
      {
        path: "/demand/divisionDemand",
        component: () => import("@/views/app-dfp-front/demand/divisionDemand/index.vue"),
        name: "divisionDemand",
        meta: { title: "外事业部需求提报", keepAlive: true },
      },
      {
        path: "/demand/supplierSubmission",
        component: () => import("@/views/app-dfp-front/demand/supplierSubmission/index.vue"),
        name: "supplierSubmission",
        meta: { title: "车型全供应商预测提报", keepAlive: true },
      },
      {
        path: "/demand/yearForecast",
        component: () => import("@/views/app-dfp-front/demand/yearForecast/index.vue"),
        name: "yearForecast",
        meta: { title: "年度预测提报", keepAlive: true },
      },
      {
        path: "/demand/newProject",
        component: () => import("@/views/app-dfp-front/demand/newProject/index.vue"),
        name: "newProject",
        meta: { title: "项目预测提报", keepAlive: true },
      },
      {
        path: "/project/forecastVersion",
        component: () => import("@/views/app-dfp-front/demand/forecastVersion/index.vue"),
        name: "forecastVersion",
        meta: { title: "项目预测版本", keepAlive: true },
      },
    ],
  },
  {
    path: "/deliveryPlan",
    component: Layout,
    meta: { title: "发货计划", icon: "nested" },
    redirect: "/deliveryPlan/deliveryPlanFormat",
    children: [
      {
        path: "/deliveryPlan/deliveryPlanFormat",
        component: () => import("@/views/app-dfp-front/deliveryPlan/deliveryPlanFormat/index"),
        name: "deliveryPlanFormat",
        meta: { title: "发货计划编制", keepAlive: true },
      },
      {
        path: "/deliveryPlan/deliveryPlanRelease",
        component: () => import("@/views/app-dfp-front/deliveryPlan/deliveryPlanRelease/index"),
        name: "deliveryPlanRelease",
        meta: { title: "发货计划发布", keepAlive: true },
      },
      {
        path: "/deliveryPlan/deliveryDockingOrder",
        component: () => import("@/views/app-dfp-front/deliveryPlan/deliveryDockingOrder/index"),
        name: "deliveryDockingOrder",
        meta: { title: "发货对接单", keepAlive: true },
      },
      {
        path: "/deliveryPlan/deliveryDackingDetail",
        component: () => import("@/views/app-dfp-front/deliveryPlan/deliveryDackingDetail/index"),
        name: "deliveryDackingDetail",
        meta: { title: "发货对接单详情", keepAlive: true },
      },
      {
        path: "/deliveryPlan/undeliveryReport",
        component: () => import("@/views/app-dfp-front/deliveryPlan/undeliveryReport/index"),
        name: "undeliveryReport",
        meta: { title: "未发布发货计划预警报表", keepAlive: true },
      },
      {
        path: "/deliveryPlan/statisticalTable",
        component: () => import("@/views/app-dfp-front/deliveryPlan/statisticalTable/index"),
        name: "statisticalTable",
        meta: { title: "发货计划发布统计表", keepAlive: true },
      },
    ]
  },
  {
    path: "/basicData",
    component: Layout,
    meta: { title: "基础数据", icon: "nested" },
    redirect: "/basicData/transportResource",
    children: [
      {
        path: "/basicData/transportResource",
        component: () => import("@/views/app-dfp-front/basicData/transportResource/index"),
        name: "transportResource",
        meta: { title: "运输资源信息", keepAlive: true },
      },
      {
        path: "/basicData/relationshipChange",
        component: () => import("@/views/app-dfp-front/basicData/relationshipChange/index"),
        name: "relationshipChange",
        meta: { title: "新旧物料关系表", keepAlive: true },
      },
      {
        path: "/basicData/transportRouting",
        component: () => import("@/views/app-dfp-front/basicData/transportRouting/index"),
        name: "transportRouting",
        meta: { title: "运输路径", keepAlive: true },
      },
      {
        path: "/basicData/safetyInventory",
        component: () => import("@/views/app-dfp-front/basicData/safetyInventory/index"),
        name: "safetyInventory",
        meta: { title: "安全库存管理", keepAlive: true },
      },
      {
        path: "/basicData/versionCompare",
        component: () => import("@/views/app-dfp-front/basicData/versionCompare/index"),
        name: "versionCompare",
        meta: { title: "版本对比", keepAlive: true },
      },
    ],
  },
  {
    path: "/executeTracking",
    component: Layout,
    meta: { title: "执行追踪", icon: "nested" },
    redirect: "/executeTracking/overloadAlert",
    children: [
      {
        path: "/executeTracking/overloadAlert",
        component: () => import("@/views/app-dfp-front/executeTracking/overloadAlert/index"),
        name: "overloadAlert",
        meta: { title: "超负荷预警报表", keepAlive: true },
      },
      {
        path: "/executeTracking/inventoryAlert",
        component: () => import("@/views/app-dfp-front/executeTracking/inventoryAlert/index"),
        name: "inventoryAlert",
        meta: { title: "库存预警报表", keepAlive: true },
      },
      {
        path: "/executeTracking/demandForecastAccuracy",
        component: () => import("@/views/app-dfp-front/executeTracking/demandForecastAccuracy/index"),
        name: "demandForecastAccuracy",
        meta: {title: "需求预测准确率", keepAlive: true},
      },
      {
        path: "/executeTracking/deliveryCompare",
        component: () => import("@/views/app-dfp-front/executeTracking/deliveryCompare/index"),
        name: "deliveryCompare",
        meta: { title: "日发货与预测监控(产品)", keepAlive: true },
      },
      {
        path: "/executeTracking/deliveryCompareVehicle",
        component: () => import("@/views/app-dfp-front/executeTracking/deliveryCompareVehicle/index"),
        name: "deliveryCompareVehicle",
        meta: { title: "日发货与预测监控(车型)", keepAlive: true },
      },
      {
        path: "/deliveryPlan/demandDeliveryProduction",
        component: () => import("@/views/app-dfp-front/deliveryPlan/demandDeliveryProduction/index"),
        name: "demandDeliveryProduction",
        meta: { title: "需求&发货&排产报表", keepAlive: true },
      },
      {
        path: "/executeTracking/customerOrderWeek",
        component: () => import("@/views/app-dfp-front/executeTracking/customerOrderWeek/index"),
        name: "customerOrderWeek",
        meta: { title: "客户订单周报", keepAlive: true },
      },
    ]
  },
];

formateRouter(dfpRouter, '/base/portalDfp')
export default dfpRouter;
