import Vue from 'vue'
import Router from 'vue-router'
import lowCode from '@/views/lowCode/index.vue'
import childRouter from './modules/child'
Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import mainLayout from '@/layout/mainLayout'
import sdsRouter from './modules/sds'
import mdsRouter from './mdsRouter.js'
import mpsRouter from './mpsRouter.js'
import dfpRouter from './dfpRouter.js'
import mrpRouter from './mrpRouter.js'

export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true,
  },
  {
    path: '/building',
    component: () => import('@/views/building'),
    hidden: true,
  },
  {
    path: '/bpmFlow',
    component: () => import('@/views/bpm/bpmFlow/index.vue'),
    hidden: true,
  },
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '/',
        name: '首页',
        affix: true,
        component: () => import('@/views/index/index'),
        meta: { title: '首页', icon: 'dashboard', affix: true, keepAlive: true }
      },
    ],
  },
  {
    path: '/auth/call_back',
    component: () => import('@/views/whitePage/index'),
    hidden: true,
  },
  {
    path: '/deliveryDynamicTracking',
    name: 'deliveryDynamicTracking',
    component: () => import('@/views/deliveryDynamicTracking/index'),
  },
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  ...mdsRouter,
  ...mpsRouter,
  ...dfpRouter,
  ...mrpRouter,

  // childRouter,
  // {
  //   path: '/base/portalSds/*',
  //   name: 'portalSds',
  //   component: Layout,

  // },
  // {
  //   path: '/base/portalDps/*',
  //   name: 'portalDps',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalMds/*',
  //   name: 'portalMds',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalSop/*',
  //   name: 'portalSop',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalMps/*',
  //   name: 'portalMps',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalMrp/*',
  //   name: 'portalMrp',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalOds/*',
  //   name: 'portalOds',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalWfp/*',
  //   name: 'portalWfp',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalAms/*',
  //   name: 'portalAms',
  //   component: Layout,
  // },
  // {
  //   path: '/base/portalDfp/*',
  //   name: 'portalDfp',
  //   component: Layout,
  // },

  // {
  //   path: '/base/*',
  //   name: 'base',
  //   // component: () => import('../views/base/index'),
  //   children: [{ path: '**', component: Layout }],
  // },
  {
    path: '/dc',
    component: Layout,
    redirect: '/dc/user',
    name: 'dc',
    meta: {
      title: '一体化平台',
      icon: 'nested',
      keepAlive: true,
    },
    children: [
      {
        path: '/dc/user/role',
        component: () => import('@/views/dc/User/Role/index'),
        name: 'meterageUnit',
        meta: { title: '权限管理', keepAlive: true },
      },
      {
        path: '/dc/dataPermission',
        component: () => import('@/views/dc/dataPermission/index'),
        name: 'dataPermission',
        meta: { title: '数据权限管理', keepAlive: true },
      },
      {
        path: '/dc/system/menu',
        component: () => import('@/views/dc/System/Menu/index'),
        name: 'menu',
        meta: { title: '菜单管理', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'Menu',
        //   icon: 'menu'
        // }
      },
      {
        path: '/dc/system/warningSql',
        component: () => import('@/views/dc/System/warningSql/index'),
        name: 'menu',
        meta: {title: '预警配置', keepAlive: true},
        // meta: {
        //   keepAlive: true,
        //   title: 'Menu',
        //   icon: 'menu'
        // }
      },
      {
        path: '/dc/system/api',
        component: () => import('@/views/dc/System/api/index'),
        name: 'api',
        meta: {title: '接口配置', keepAlive: true},
        // meta: {
        //   keepAlive: true,
        //   title: 'Menu',
        //   icon: 'menu'
        // }
      },
      {
        path: '/dc/system/apiSyncCtrl',
        component: () => import('@/views/dc/System/apiSyncCtrl/index'),
        name: 'apiSyncCtrl',
        meta: {title: '接口同步时间', keepAlive: true},
      },
      {
        path: '/dc/system/apiLog',
        component: () => import('@/views/dc/System/apiLog/index'),
        name: 'apiLog',
        meta: { title: '接口日志', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'Menu',
        //   icon: 'menu'
        // }
      },
      {
        path: '/dc/system/log',
        component: () => import('@/views/dc/System/log/index'),
        name: 'menu',
        meta: { title: '算法日志', keepAlive: true },
      },
      {
        path: '/dc/monitorLogs',
        component: () => import('@/views/dc/monitorLogs/index'),
        name: 'menu',
        meta: { title: '业务监控日志', keepAlive: true },
      },
      {
        path: '/dc/tenant',
        component: () => import('@/views/dc/tenant/index'),
        name: 'tenant',
        meta: { title: '租户管理', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'Tenant',
        //   icon: 'Tenant'
        // }
      },
      {
        path: '/dc/taskManage',
        component: () => import('@/views/dc/taskManage/index'),
        name: 'menu',
        meta: { title: '任务管理', keepAlive: true },
      },
      {
        name: 'user',
        path: '/dc/User/User',
        component: () => import('@/views/dc/User/User/index'),
        meta: { title: '账号管理', keepAlive: true },

        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },
      {
        name: 'user',
        path: '/dc/scenario',
        component: () => import('@/views/dc/scenario/index'),
        meta: { title: '场景管理', keepAlive: true },

        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },
      {
        name: 'setting',
        path: '/dc/setting',
        component: () => import('@/views/dc/setting/index'),
        meta: { title: '工具栏管理', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },

      {
        name: 'importLog',
        path: '/dc/importLog',
        component: () => import('@/views/dc/importLog/index'),
        meta: { title: '导入日志', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },
      {
        name: 'valueSet',
        path: '/dc/valueSet',
        component: () => import('@/views/dc/valueSet/index'),
        meta: { title: '字典表', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },


      {
        name: 'objectFields',
        path: '/dc/objectFields',
        component: () => import('@/views/dc/objectFields/index'),
        meta: { title: '对象字段配置', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },
      {
        name: 'unitSetting',
        path: '/dc/unitSetting',
        component: () => import('@/views/dc/unitSetting/index'),
        meta: { title: '对象单位配置', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },
      {
        name: 'extension',
        path: '/dc/extension',
        component: () => import('@/views/dc/extension/index'),
        meta: { title: '扩展点管理', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },
      {
        name: 'extension',
        path: '/dc/extension',
        component: () => import('@/views/dc/extension/index'),
        meta: { title: '扩展点管理', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'AccountManagement',
        //   icon: 'user'
        // }
      },
      {
        path: '/dc/moduleScenario',
        component: () => import('@/views/dc/moduleScenario/index'),
        name: 'moduleScenario',
        meta: { title: '模块衔接', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'Menu',
        //   icon: 'menu'
        // }
      },
      {
        path: '/dc/dataConversion',
        component: () => import('@/views/dc/dataConversion/index'),
        name: 'dataConversion',
        meta: { title: '数据交换配置', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'Menu',
        //   icon: 'menu'
        // }
      },
      {
        path: '/dc/redisKeyManagement',
        component: () => import('@/views/dc/redisKeyManagement/index'),
        name: 'redisKeyManagement',
        meta: { title: 'redis key管理', keepAlive: true },
        // meta: {
        //   keepAlive: true,
        //   title: 'Menu',
        //   icon: 'menu'
        // }
      },
    ],
    // children: [
    //   {
    //     path: '/supplyModel/meterageUnit',
    //     component: mainLayout,
    //     name: 'meterageUnit',
    //     meta: { title: '计量单位' },
    //     redirect: '/supplyModel/meterageUnit/meterageUnit',

    //   }
    // ]
  },
  {
    name: '调度中心',
    component: Layout,
    path: '/job',
    redirect: '/job/jobinfo',
    meta: {
      keepAlive: true,
      title: '流程管理',
      icon: '',
    },
    children: [
      {
        name: '任务管理',
        path: '/job/jobinfo',
        component: () => import('@/views//job/jobinfo/index.vue'),
        meta: {
          keepAlive: true,
          title: '任务管理',
          icon: '',
        },
      },
      {
        name: '调度日志',
        path: '/job/joblog',
        component: () => import('@/views//job/joblog/index.vue'),
        meta: {
          keepAlive: true,
          title: '任务管理',
          icon: '',
        },
      },
      {
        name: '执行器管理',
        path: '/job/jobgroup',
        component: () => import('@/views//job/jobgroup/index.vue'),
        meta: {
          keepAlive: true,
          title: '任务管理',
          icon: '',
        },
      },
    ],
  },
  {
    name: '数据校验管理',
    component: Layout,
    path: '/dataVerification',
    redirect: '/dataVerification/rulesManager',
    meta: {
      keepAlive: true,
      title: '数据校验管理',
      icon: '',
    },
    children: [
      {
        name: '规则管理',
        path: '/dataVerification/rulesManager',
        component: () =>
          import('@/views/dataVerification/rulesManager/index.vue'),
        meta: {
          keepAlive: true,
          title: '规则管理',
          icon: '',
        },
      },
      {
        name: '规则配置',
        path: '/dataVerification/ruleConfiguration',
        component: () =>
          import('@/views/dataVerification/ruleConfiguration/index.vue'),
        meta: {
          keepAlive: true,
          title: '规则配置',
          icon: '',
        },
      },
    ],
  },
  {
    name: '流程管理',
    component: Layout,
    path: '/bpm',
    redirect: '/bpm/bpmModel',
    meta: {
      keepAlive: true,
      title: '流程管理',
      icon: '',
    },
    children: [
      {
        name: '流程模型',
        path: '/bpm/bpmModel',
        component: () => import('@/views/bpm/bpmModel/index.vue'),
        meta: {
          keepAlive: true,
          title: '流程模型',
          icon: '',
        },
      },
      {
        name: '流程定义',
        path: '/bpm/bpmProcess',
        component: () => import('@/views/bpm/bpmProcess/index.vue'),
        meta: {
          keepAlive: true,
          title: '流程定义',
          icon: '',
        },
      },
      {
        name: '流程监控',
        path: '/bpm/bpmRunning',
        component: () => import('@/views/bpm/bpmRunning/index.vue'),
        meta: {
          keepAlive: true,
          title: '流程监控',
          icon: '',
        },
      },
      {
        name: '流程历史',
        path: '/bpm/bpmHistory',
        component: () => import('@/views/bpm/bpmHistory/index.vue'),
        meta: {
          keepAlive: true,
          title: '流程历史',
          icon: '',
        },
      },
      {
        name: '流程用户',
        path: '/bpm/bpmUser',
        component: () => import('@/views/bpm/bpmUser/index.vue'),
        meta: {
          keepAlive: true,
          title: '流程用户',
          icon: '',
        },
      },
      {
        name: '流程用户组',
        path: '/bpm/bpmGroup',
        component: () => import('@/views/bpm/bpmGroup/index.vue'),
        meta: {
          keepAlive: true,
          title: '流程用户组',
          icon: '',
        },
      },
    ],
  },
  { path: '*', redirect: '/404', hidden: true },
]

const createRouter = () =>
  new Router({
    mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

// 重定向时报错，用这个不让他报错
const routerPush = Router.prototype.push
Router.prototype.push = function (location) {
  return routerPush.call(this, location).catch((error) => error)
}

export default router
