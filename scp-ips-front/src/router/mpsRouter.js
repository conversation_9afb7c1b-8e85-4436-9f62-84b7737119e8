
import Layout from "@/layout";
import { formateRouter } from '@/utils/formateRouter'
const mpsRouter = [
  // {
  //   path: '/base/*',
  //   name: 'base',
  //   // component: () => import('../views/base/index'),
  //   children: [{ path: '**', component: Layout }],
  // },
  {
    path: '/parameters',
    component: Layout,
    redirect: '/parameters/planPeriod',
    meta: {
      title: "计划策略",
      icon: "el-icon-s-operation",
    },
    children: [
      {
        path: '/parameters/planPeriod',
        component: () => import('@/views/app-mps-front/parameters/planPeriod/index.vue'),
        name: 'parametersPlanPeriod',
        meta: { title: '计划期间' }
      },
    ]
  },
  {
    path: '/feedback',
    component: Layout,
    redirect: '/feedback/executionFeedback',
    meta: {
      title: "执行反馈",
      icon: "el-icon-s-operation",
    },
    children: [
      {
        path: '/feedback/planBatchFeedback',
        component: () => import('@/views/app-mps-front/feedback/planBatchFeedback/index.vue'),
        name: 'planBatchFeedback',
        meta: { title: '计划批次反馈', keepAlive: true }
      },
      {
        path: '/feedback/processFeedback',
        component: () => import('@/views/app-mps-front/feedback/processFeedback/index.vue'),
        name: 'processFeedback',
        meta: { title: '工序生产反馈', keepAlive: true }
      },
      {
        path: '/feedback/putFeedback',
        component: () => import('@/views/app-mps-front/feedback/putFeedback/index.vue'),
        name: 'putFeedback',
        meta: { title: '入库反馈', keepAlive: true }
      },
      {
        path: '/feedback/materialRequisitionFeedback',
        component: () => import('@/views/app-mps-front/feedback/materialRequisitionFeedback/index.vue'),
        name: 'materialRequisitionFeedback',
        meta: { title: '领料反馈', keepAlive: true }
      }
    ]
  },
  {
    path: '/optimizedScheme',
    component: Layout,
    redirect: '/optimizedScheme/calculationExecution',
    meta: {
      title: "优化计算",
      icon: "el-icon-s-operation",
    },
    children: [
      {
        path: '/optimizedScheme/calculationExecution',
        component: () => import('@/views/app-mps-front/optimizedScheme/calculationExecution/index.vue'),
        name: 'calculationExecution',
        meta: { title: '优化计算执行', keepAlive: true }
      },
      {
        path: '/optimizedScheme/strategyConfiguration',
        component: () => import('@/views/app-mps-front/optimizedScheme/strategyConfiguration/index.vue'),
        name: 'strategyConfiguration',
        meta: { title: '优化策略配置', keepAlive: true }
      }
    ]
  },
  {
    path: '/resourcePlan',
    component: Layout,
    meta: { title: '资源计划', icon: 'nested' },
    redirect: '/resourcePlan/resourcePlan',
    children: [
      {
        path: '/resourcePlan/resourcePlan',
        component: () => import('@/views/app-mps-front/resourcePlan/resourcePlan/index'),
        name: 'resourcePlan',
        meta: { title: '资源生产计划', keepAlive: true },
      },
      {
        path: '/resourcePlan/resourceLoad',
        component: () =>
          import('@/views/app-mps-front/resourcePlan/resourceLoad/index'),
        name: 'resourceLoad',
        meta: { title: '能力负荷计划', keepAlive: true },
      },
      {
        path: '/resourcePlan/resourceLoadOld',
        component: () =>
          import('@/views/app-mps-front/resourcePlan/resourceLoadOld/index'),
        name: 'resourceLoadOld',
        meta: { title: '能力负荷计划old', keepAlive: true },
      },
      {
        path: '/resourcePlan/orderSupplyChain',
        component: () => import('@/views/app-mps-front/resourcePlan/orderSupplyChain/index.vue'),
        name: 'orderSupplyChain',
        meta: { title: '需求供应链', keepAlive: true }
      },
    ],
  },
  {
    path: "/foundation",
    component: Layout,
    meta: { title: "基础信息", icon: "nested" },
    redirect: "/foundation",
    children: [
      {
        path: "/production/productResources",
        component: () => import("@/views/app-mps-front/foundation/productResources/index"),
        name: "productResources",
        meta: { title: "生产资源", keepAlive: true },
      },
      {
        path: "/foundation/highValueMaterials",
        component: () => import("@/views/app-mps-front/foundation/highValueMaterials/index"),
        name: "highValueMaterials",
        meta: { title: "高价值物料", keepAlive: true },
      },
      {
        path: "/foundation/selfSupply",
        component: () => import("@/views/app-mps-front/foundation/selfSupply/index"),
        name: "selfSupply",
        meta: { title: "自给件需求", keepAlive: true },
      },
      {
        path: "/foundation/productionLeadTime",
        component: () => import("@/views/app-mps-front/foundation/productionLeadTime/index"),
        name: "productionLeadTime",
        meta: { title: '生产提前期', keepAlive: true },
      },
      {
        path: "/foundation/moldChangeTime",
        component: () => import("@/views/app-mps-front/foundation/moldChangeTime/index"),
        name: "moldChangeTime",
        meta: { title: '换模换型时间', keepAlive: true },
      },
      {
        path: "/foundation/coatingChangeTime",
        component: () => import("@/views/app-mps-front/foundation/coatingChangeTime/index"),
        name: "coatingChangeTime",
        meta: { title: '镀膜切换时间', keepAlive: true },
      },
      {
        path: "/foundation/equipmentEfficiency",
        component: () => import("@/views/app-mps-front/foundation/equipmentEfficiency/index"),
        name: "equipmentEfficiency",
        meta: { title: '特殊工艺设备效率', keepAlive: true },
      },
      {
        path: "/foundation/coatingMaintenanceAmount",
        component: () => import("@/views/app-mps-front/foundation/coatingMaintenanceAmount/index"),
        name: "coatingMaintenanceAmount",
        meta: { title: '镀膜保养量', keepAlive: true },
      },
      // {
      //   path: "/foundation/resourceRelations",
      //   component: () => import("@/views/app-mps-front/foundation/resourceRelations/index"),
      //   name: "resourceRelations",
      //   meta: {title: '产品资源生产关系', keepAlive: true},
      // },
      {
        path: "/foundation/productTakt",
        component: () => import("@/views/app-mps-front/foundation/productTakt/index"),
        name: "productTakt",
        meta: { title: '生产节拍', keepAlive: true },
      },
      {
        path: "/foundation/resourceOee",
        component: () => import("@/views/app-mps-front/foundation/resourceOee/index"),
        name: "resourceOee",
        meta: { title: '工序设备生产效率', keepAlive: true },
      },
      {
        path: "/foundation/productSubstitutionRelationship",
        component: () => import("@/views/app-mps-front/foundation/productSubstitutionRelationship/index"),
        name: "productSubstitutionRelationship",
        meta: { title: '物料替代关系', keepAlive: true },
      },
      {
        path: "/foundation/subInventoryCargoLocationInformation",
        component: () => import("@/views/app-mps-front/foundation/subInventoryCargoLocationInformation/index"),
        name: "subInventoryCargoLocationInformation",
        meta: { title: '子库存货位信息', keepAlive: true },
      },
      {
        path: "/foundation/productionCapacity",
        component: () => import("@/views/app-mps-front/foundation/productionCapacity/index"),
        name: "productSubstitutionRelationship",
        meta: { title: '工序后库容量', keepAlive: true },
      },
      {
        path: "/foundation/productionLimit",
        component: () => import("@/views/app-mps-front/foundation/productionLimit/index"),
        name: "productionLimit",
        meta: { title: '特殊工艺产能约束', keepAlive: true },
      },
      {
        path: "/foundation/productionBeat",
        component: () => import("@/views/app-mps-front/foundation/productionBeat/index"),
        name: "productionBeat",
        meta: { title: '特殊工艺产品节拍', keepAlive: true },
      },
    ]
  },
  {
    path: "/capacityBalance",
    component: Layout,
    meta: { title: "产能平衡", icon: "nested" },
    redirect: "/capacityBalance",
    children: [
      {
        path: "/capacityBalance/balanceManage",
        component: () => import("@/views/app-mps-front/capacityBalance/balanceManage/index"),
        name: "balanceManage",
        meta: { title: "产能平衡管理", keepAlive: true },
      },
      {
        path: "/capacityBalance/balanceWeek",
        component: () => import("@/views/app-mps-front/capacityBalance/balanceWeek/index"),
        name: "balanceWeek",
        meta: { title: "周产能平衡", keepAlive: true },
      },
      {
        path: "/capacityBalance/balanceVersion",
        component: () => import("@/views/app-mps-front/capacityBalance/balanceVersion/index"),
        name: "balanceVersion",
        meta: { title: "产能平衡对比", keepAlive: true },
      },
      {
        path: "/capacityBalance/versionRecord",
        component: () => import("@/views/app-mps-front/capacityBalance/versionRecord/index"),
        name: "versionRecord",
        meta: { title: "产能平衡版本", keepAlive: true },
      },
    ]
  },
  {
    path: "/planExecute",
    component: Layout,
    meta: { title: "生产计划执行与监控", icon: "nested" },
    redirect: "/planExecute",
    children: [
      {
        path: "/planExecute/reportFeedback",
        component: () => import("@/views/app-mps-front/planExecute/reportFeedback/index"),
        name: "reportFeedback",
        meta: { title: "生产报工反馈", keepAlive: true },
      },
      {
        path: "/planExecute/abnormalFeedback",
        component: () => import("@/views/app-mps-front/planExecute/abnormalFeedback/index"),
        name: "abnormalFeedback",
        meta: { title: "生产异常反馈", keepAlive: true },
      },
      {
        path: "/productToolingRelationship",
        component: () => import("@/views/app-mps-front/productToolingRelationship/index"),
        name: "productToolingRelationship",
        meta: { title: "产品工装关系", keepAlive: true },
      },
      {
        path: "/planExecute/mainProductionPlan",
        component: () => import("@/views/app-mps-front/planExecute/mainProductionPlan/index"),
        name: "mainProductionPlan",
        meta: { title: "主生产计划", keepAlive: true },
      },
      {
        path: "/planExecute/temporaryProductionPlan",
        component: () => import("@/views/app-mps-front/planExecute/temporaryProductionPlan/index"),
        name: "temporaryProductionPlan",
        meta: { title: "计划单下发", keepAlive: true },
      },
      {
        path: "/planExecute/materialAllocation",
        component: () => import("@/views/app-mps-front/planExecute/materialAllocation/index"),
        name: "materialAllocation",
        meta: { title: "生产齐套监控", keepAlive: true },
      },
      {
        path: "/planExecute/algorithmConstraintRule",
        component: () => import("@/views/app-mps-front/planExecute/algorithmConstraintRule/index"),
        name: "algorithmConstraintRule",
        meta: { title: "算法约束规则", keepAlive: true },
      },
      {
        path: "/planExecute/planApproval",
        component: () => import("@/views/app-mps-front/planExecute/planApproval/index"),
        name: "planApproval",
        meta: { title: "计划下发审批", keepAlive: true },
      },
    ]
  },
  {
    path: "/outsourceTransferSummary",
    component: Layout,
    meta: { title: "委外转产需求", icon: "nested" },
    redirect: "/outsourceTransferSummary",
    children: [
      {
        path: "/outsourceTransferSummary/manage",
        component: () => import("@/views/app-mps-front/outsourceTransferSummary/index"),
        name: "outsourceTransferSummaryManage",
        meta: { title: "委外转产需求汇总", keepAlive: true },
      },
      {
        path: "/outsourceTransferSummary/calculate",
        component: () => import("@/views/app-mps-front/outsourceTransferCalculation/index"),
        name: "outsourceTransferCalculation",
        meta: { title: "委外转产材料需求计算", keepAlive: true },
      },
    ]
  },
  {
    path: "/deliveryTrackingOverview",
    component: Layout,
    meta: { title: "交付跟踪总览", icon: "nested" },
    redirect: "/deliveryTrackingOverview",
    children: [
      {
        path: "/deliveryTrackingOverview/index",
        component: () => import("@/views/app-mps-front/deliveryTrackingOverview/index"),
        name: "deliveryTrackingOverview",
        meta: { title: "交付跟踪总览", keepAlive: true },
      },
    ]
  },
  {
    path: "/achievementRateReport",
    component: Layout,
    meta: { title: "生产计划达成率报表", icon: "nested" },
    redirect: "/achievementRateReport",
    children: [
      {
        path: "/achievementRateReport/index",
        component: () => import("@/views/app-mps-front/achievementRateReport/index"),
        name: "achievementRateReport",
        meta: { title: "生产计划达成率报表", keepAlive: true },
      },
    ]
  },
  {
    path: "/capacityLoadReport",
    component: Layout,
    meta: { title: "月周日产能负荷报表", icon: "nested" },
    redirect: "/capacityLoadReport",
    children: [
      {
        path: "/capacityLoadReport/index",
        component: () => import("@/views/app-mps-front/capacityLoadReport/index"),
        name: "capacityLoadReport",
        meta: { title: "月周日产能负荷报表", keepAlive: true },
      },
    ]
  },
  // {
  //   path: "/outsourceTransferSummary",
  //   component: () => import("@/views/app-mps-front/outsourceTransferSummary/index"),
  //   name: "outsourceTransferSummary",
  //   meta: {title: "委外转产需求汇总", icon: "nested", keepAlive: true},
  // },
  //   { path: '*', redirect: '/404', hidden: true }
];
formateRouter(mpsRouter, '/base/portalMps')
export default mpsRouter;
