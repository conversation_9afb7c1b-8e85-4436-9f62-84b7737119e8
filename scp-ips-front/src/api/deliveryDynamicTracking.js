import request from '@/utils/requestNoLogin'
import baseUrl from '@/utils/baseUrl';

// 工厂
export function listResourceDropDown() {
  return request({
    url: baseUrl.mps +'/dynamicDeliveryTrackingTask/listResourceDropDown',
    method: 'get',
  })
}

// 交付动态跟踪平台查询
export function selectTaskByPhysicalResourceId(params) {
  return request({
    url: baseUrl.mps +'/dynamicDeliveryTrackingTask/selectTaskByPhysicalResourceId',
    method: 'get',
    params
  })
}

// 获取二维码
export function qrCode(params) {
  return request({
    url: baseUrl.mps +'/dynamicDeliveryTrackingTask/qrCode',
    method: 'get',
    params
  })
}

// 交付动态跟踪平台-明细查询
export function selectSubTaskByTaskId(params) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTrackingSubTask/selectSubTaskByTaskId',
    method: 'get',
    params
  })
}

// 交付动态跟踪平台-滚动查询
export function rollingNotificationQuery(params) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTrackingTask/rollingNotificationQuery',
    method: 'get',
    params
  })
}

// 生产组织
export function orgOption(params) {
  return request({
      url: baseUrl.mps + '/dynamicDeliveryTracking/org',
      method: 'get',
      params
  })
}

// 工序
export function standardResourceDropdown(params) {
  return request({
      url: baseUrl.mps + '/masterPlan/standardResourceDropdown',
      method: 'get',
      params
  })
}

// 产线下拉
export function physicalResourceDropdown(params) {
  return request({
      url: baseUrl.mps + '/masterPlan/physicalResourceDropdown',
      method: 'get',
      params
  })
}

// 告警查询
export function alertQuery(data) {
  return request({
      url: baseUrl.mps + '/dynamicDeliveryTrackingSubTask/alertQuery',
      method: 'post',
      data
  })
}

// 获取产线
export function selectGroupTask(params) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTrackingTask/selectGroupTask',
    method: 'get',
    params
  })
}
