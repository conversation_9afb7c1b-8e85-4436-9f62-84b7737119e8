import { service } from '@/utils/djRequest';
import baseUrl from '@/utils/baseUrl';
import Qs from 'qs';
import Axios from 'axios';
import moment from "moment";

// 根据菜单获取组件
export function fetchComponent(data, KEY) {
  return service({
    url: `${baseUrl.base}/componentinfo/resource/component`,
    method: 'get',
    headers: {
      componentKey: KEY
    },
    params: data
  });
}
// 获取版本信息
export function fetchVersions(data, KEY) {
  return service({
    url: `${baseUrl.base}/componentinfo/versions`,
    method: 'get',
    headers: {
      componentKey: KEY
    },
    params: data
  });
}
// 新增/修改组件配置信息
export function createOrUpdateComs(data, KEY) {
  return service({
    url: `${baseUrl.base}/componentinfo/createOrUpdate`,
    method: 'post',
    headers: {
      componentKey: KEY
    },
    //data: Qs.stringify(data)
    data
  });
}
// 根据版本删除视图版本
export function delComponent(id) {
  return service({
    url: `${baseUrl.base}/componentinfo/delete/${id}`,
    method: 'post',
  })
}
// 根据id获取组件信息
export function fetchComponentinfo(id, KEY) {
  return service({
    url: `${baseUrl.base}/componentinfo/${id}`,
    method: 'get',
    headers: {
      componentKey: KEY
    }
  });
}
//保存表达式
export function updateExpression(data, KEY, type) {
  return service({
    url: `${baseUrl.base}/componentinfo/expression`,
    method: 'post',
    data,
    //data: Qs.stringify(data),
    headers: {
      componentKey: KEY,
      objectType: type
    }
  });
}
//删除表达式
export function delExpressions(id) {
  return service({
    url: `${baseUrl.base}/componentinfo/expression/delete/${id}`,
    method: 'post'
  });
}
// 更新表达式（建哥说这个接口暂时屏蔽）
export function calculateExression(KEY) {
  return service({
    url: `${baseUrl.base}/exressioncalculate/calcute`,
    method: 'get',
    params: {
      componentKey: KEY,
      objectType: type
    }
  });
}

// 枚举
//是否有效
export function enums(data) {
  return service({
    url: `${baseUrl.base}/enums`,
    method: 'get',
    params: data
  });
}

// 页面增删改查
// table数据查询
export function fetchList(data, myurl, method, key, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';

  let url = `${baseUrl.schedule}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    },
    params: params
  });
}
// table数据查询
export function fetchDcpList(data, myurl, method, key, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';

  let url = `${baseUrl.dcp}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    },
    params: params
  });
}
// table数据查询(报表字段命名,8762端口)
export function fetchListRep(data, myurl, method, key, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';

  let url = `${baseUrl.auth}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    },
    params: params
  });
}

// table数据查询(工序计划排程)
export function fetchListProductPeriod(data, myurl, method, key, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';

  let url = `${baseUrl.schedule}/${myurl}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    },
    params: params
  });
}

// table数据查询(产线达成页面)
export function fetchListPro(data, myurl, method, key) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let url = `${baseUrl.schedule}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}&dateType=${data.dateType}&isExceptional=${data.isExceptional}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    }
  });
}

// table数据查询(生产异常统计页面)
export function fetchListAbn(data, myurl, method, key) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let url = `${baseUrl.schedule}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}&abnormalType=${data.abnormalType}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    }
  });
}

// table数据查询(生产异常统计页面)
export function fetchListBalance(data, myurl, method, key) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let url = `${baseUrl.schedule}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}&dateType=${data.dateType}&rowSpan=${data.rowSpan}&startTime=${data.startTime}&endTime=${data.endTime}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    }
  });
}

// table数据查询(计划下发履历)
export function fetchListTableColumnsResource(data, myurl, method, key) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let url = `${baseUrl.schedule}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}&creator=${data.creator}&creatorName=${data.creatorName}&createTime=${data.createTime}&resourceId=${data.resourceId}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    }
  });
}

// 资源产能负荷
export function fetchListResourceLoad(data, myurl, method, key) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let url = `${baseUrl.schedule}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}&operationIds=${data.operationIds}&resourceId=${data.resourceId}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    }
  });
}

// table数据查询(工艺路径Tabs)
export function fetchListRoutingTabs(data, myurl, method, key) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let url = `${baseUrl.schedule}/${myurl}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}&stepId=${data.stepId}`;
  return service({
    url,
    method: method,
    headers: {
      componentKey: key
    }
  });
}

// 流程请求
export function fetchListBPM(data, url, method, key, params) {
  let sortParam =
    data.sortParam && data.sortParam.length > 0
      ? encodeURI(JSON.stringify(data.sortParam))
      : ''
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : ''
  let expandDepth = ``
  if (data.expandDepth != undefined && data.expandDepth != null) {
    expandDepth = `&expandDepth=${data.expandDepth}`
  }
  let _url = `${baseUrl.auth}/${url}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}${expandDepth}`
  return service({
    url: _url,
    method: method,
    headers: {
      componentKey: key,
    },
    params: params,
  })
}

// 删除-- 拼接url
export function deleteFnNew(data, myurl, key) {
  return service({
    url: `${baseUrl.schedule}/${myurl}/${data}`,
    method: 'post',
    headers: {
      componentKey: key
    }
  });
}

// 导出模板
export function myExportTemplate(exportDataUrl) {
  const url = `${baseUrl.schedule}/${exportDataUrl}`;
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = url;
  a.click();
}

// 导出数据
// export function myExportData(exportDataUrl, data) {
//   let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
//   let queryCriteriaParam =
//     data.queryCriteriaParam && data.queryCriteriaParam.length > 0
//       ? encodeURI(JSON.stringify(data.queryCriteriaParam))
//       : '';
//   const url = `${baseUrl.schedule}/${exportDataUrl}?sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
//   const a = document.createElement('a');
//   a.style.display = 'none';
//   a.href = url;
//   a.click();
// }
export function myExportData(exportDataUrl, data) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  const url = `${baseUrl.schedule}/${exportDataUrl}?sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
  Axios.get(url, {
    responseType: 'blob',
    headers: {
      scenario: sessionStorage.getItem('scenario') || '',
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType
    }
  }).then(res => {
    let a = document.createElement('a');
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers['content-disposition'];
    const fileName = window.decodeURI(contentDisposition.split('filename=')[1]);
    a.setAttribute('href', objUrl);
    a.setAttribute('download', fileName);
    a.click();
  });
}
// 导出数据(生产异常统计)
// export function myExportDataAbn(exportDataUrl, data) {
//   const url = `${baseUrl.schedule}/${exportDataUrl}?abnormalType=${data.abnormalType}`;
//   const a = document.createElement('a');
//   a.style.display = 'none';
//   a.href = url;
//   a.click();
// }
export function myExportDataAbn(exportDataUrl, data) {
  const url = `${baseUrl.schedule}/${exportDataUrl}?abnormalType=${data.abnormalType}`;
  Axios.get(url, {
    responseType: 'blob',
    headers: {
      scenario: sessionStorage.getItem('scenario') || '',
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType
    }
  }).then(res => {
    let a = document.createElement('a');
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers['content-disposition'];
    const fileName = window.decodeURI(contentDisposition.split('filename=')[1]);
    a.setAttribute('href', objUrl);
    a.setAttribute('download', fileName);
    a.click();
  });
}
//导出数据(产线达成统计)
export function myExportDataRes(exportDataUrl, data) {
  const url = `${baseUrl.schedule}/${exportDataUrl}?`;
  Axios.get(url, {
    responseType: 'blob',
    headers: {
      scenario: sessionStorage.getItem('scenario') || '',
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType
    },
    params: data
  }).then(res => {
    let a = document.createElement('a');
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers['content-disposition'];
    const fileName = window.decodeURI(contentDisposition.split('filename=')[1]);
    a.setAttribute('href', objUrl);
    a.setAttribute('download', fileName);
    a.click();
  });
}
//接口同步最后更新时间
export function interfaceWarning(id) {
  return service({
    url: `${baseUrl.base}/extApiLog/interfaceWarning/${id}`,
    method: 'get',
  })
}
export function handleInterfaceWarning(id) {
  return interfaceWarning(id).then(res => {
    if (res.data.success && res.data.data) {
      const { status, requestTime } = res.data.data;
      const dateTime = moment(requestTime).format('YYYY-MM-DD HH:mm:ss');
      if (status === 'SUCCESS') {
            return {
              status: 'SUCCESS',
              text: `状态:成功,最后更新时间:${dateTime}`,
              color: '#67C23A'
            };
          } else if (status === 'ERROR') {
            return {
              status: 'ERROR',
              text: `状态:失败,数据在:${dateTime} 更新失败`,
              color: '#F56C6C'
            };
          }
    }

    // 异常情况统一处理
    return {
      status: 'ERROR',
      text: '接口返回数据异常',
      color: '#F56C6C'
    };

  }).catch(error => {
    return {
      status: 'ERROR',
      text: '请求失败，请稍后再试',
      color: '#F56C6C'
    };
  });
}

