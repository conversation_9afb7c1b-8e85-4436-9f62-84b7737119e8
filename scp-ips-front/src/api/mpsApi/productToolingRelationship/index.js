import request from '@/utils/requestMps/request';
import baseUrl from '@/utils/baseUrl';

let currentUrl =  baseUrl.mps;

export const newStockPointCreate = (data) => {
  return request({
    url: `${currentUrl}/productFixtureRelation/create`,
    method: 'post',
    data
  })
};
export const deleteApi = (data) => {
  return request({
    url: `${currentUrl}/productFixtureRelation/delete`,
    method: 'post',
    data
  })
};

export const newStockPointUpdate = (data) => {
  return request({
    url: `${currentUrl}/productFixtureRelation/update`,
    method: 'post',
    data
  })
};

//获取组织下拉
export const getOrganizationDropDown = () => {
  return request({
    url: `${currentUrl}/proAbnormalFeedback/org`,
    method: 'get'
  })
};

//获取生产工单号下拉
export const getWorkOrderNumberDropDown = () => {
  return request({
    url: `${currentUrl}/proAbnormalFeedback/selectProductOrderCodes`,
    method: 'get'
  })
};

//获取资源下拉
export const getResourceDropDown = () => {
  return request({
    url: `${currentUrl}/proAbnormalFeedback/selectPhysicalResource`,
    method: 'get'
  })
};
// 获取标准资源-(可根据库存点组织)
export function getStandarResource(params) {
  return request({
    url: baseUrl.mps + '/productFixtureRelation/getStandardResource',
    method: 'get',
    params
  })
}

// 获取物理资源-根据标准资源
export function getPhysicalResourceById(params) {
  return request({
    url: baseUrl.mps + '/productFixtureRelation/getPhysicalResourceByStandardId',
    method: 'get',
    params
  })
}
