import request from "@/utils/requestMps/request";
import baseUrl from "@/utils/baseUrl";

// 采购订单
export function detailBohStock(data) {
  return request({
    url: baseUrl.sds + "/bohStock/detail/" + data,
    method: "get",   
  });
}
// 删除 

export function deleteWorkOrder(data) {
  return request({
    url: baseUrl.sds + "/workOrder/delete",
    method: "post",
    data,
  });
}
// 新增
export function createWorkOrder(data) {
  return request({
    url: baseUrl.sds + "/workOrder/createSpec",
    method: "post",
    data,
  });
}
// 修改
export function updateWorkOrder(data) {
  return request({
    url: baseUrl.sds + "/workOrder/updateSpec",
    method: "post",
    data,
  });
}
// 公共接口 枚举获取
export function dropdownEnum(params) {
  return request({
    url: baseUrl.mps + "/dropdown/enum",
    method: "get", 
    params, 
  });
}

// 用户指定路径
export function dropdownByProductId(data) {
  return request({
    url: baseUrl.mds + `/routing/dropdownByParams`,
    method: "post",
    data,
  });
}

// 制造订单展开
export function workOrderSynchronize(data) {
  return request({
    url: baseUrl.sds + `/workOrder/workOrderSynchronize`,
    method: "post",
    data,
  });
}
