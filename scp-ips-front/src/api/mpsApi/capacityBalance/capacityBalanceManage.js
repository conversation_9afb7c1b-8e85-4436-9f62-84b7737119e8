import request from '@/utils/requestMps/request'
import baseUrl from '@/utils/baseUrl'
import Axios from 'axios';


let currentService =  baseUrl.mps

// 产能平衡规则-取值-当前的设定
export function getRuleList(params) {
  return request({
    url: currentService + '/paramCapacityBalance/queryParam',
    method: 'get',
    params
  })
}
// 产能平衡规则-参数
export function getRuleOptions(params) {
  return request({
    url: currentService + '/paramCapacityBalance/resourceChooseRule',
    method: 'get',
    params
  })
}
// 产能平衡规则-变化
export function ruleInsert(data) {
  return request({
    url: currentService + '/paramCapacityBalance/insert',
    method: 'post',
    data
  })
}




// 产能明细-资源下拉
export function resourceDropdown(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/candidateResourceQuery',
    method: 'post',
    data
  })
}


// 产能明细-锁定解锁
export function lockStatusBatch(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/lockStatusBatch',
    method: 'post',
    data
  })
}
// 产能明细-编辑
export function updateApi(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/lineEdit',
    method: 'post',
    data
  })
}
// 产能明细-批量编辑
export function updateApiBatch(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/batchEdit',
    method: 'post',
    data
  })
}

// 委外调整
export function outsourcingAdjustment(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/outsourcingAdjustment',
    method: 'post',
    data
  })
}

// 产能平衡明细调整
export function candidateResourceQuery(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/candidateResourceQuery',
    method: 'post',
    data
  })
}

// 产能平衡明细-编辑-资源下拉
export function resourceDropDown(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/resourceDropDown',
    method: 'post',
    data
  })
}

// 根据预测月份获取最新版本号
export function getLatestVersionCode(params) {
  return request({
    url: currentService + '/capacityBalanceVersion/getLatestVersionCode',
    method: 'get',
    params
  })
}

// 发布
export function publishVersion(data) {
  return request({
    url: currentService + '/capacityBalanceVersion/publishVersion',
    method: 'post',
    data
  })
}

// 版本数据对比 产能明细
export function contrastCapacitySupplyRelationship(params) {
  return request({
    url: currentService + '/capacityBalanceVersion/contrastCapacitySupplyRelationship',
    method: 'get',
    params
  })
}
// 产能平衡计算
export function capacityBalanceExecute(data) {
  return request({
    url: currentService + '/capacityBalance/capacityBalanceExecute',
    method: 'post',
    data
  })
}
// 周产能平衡计算
export function capacityWeekBalanceExecute(data) {
  return request({
    url: currentService + '/capacityBalance/capacityWeekBalanceExecute',
    method: 'post',
    data
  })
}
// 变更同步
export function changeSynchronizationLock(data) {
  return request({
    url: currentService + '/capacityBalance/changeSynchronizationLock',
    method: 'post',
    data
  })
}

// 新路径编码
export function getProductCode(params) {
  return request({
    url: baseUrl.mds + '/newProductStockPoint/selectProductCodeLike',
    method: 'get',
    params
  })
}

// 库存点下拉
export function dropDownEnable() {
  return request({
    url: baseUrl.mds +'/newStockPoint/dropDownEnable',
    method: 'get',
  })
}


// 工厂下拉
export function productionOrganization(params) {
  return request({
    url: baseUrl.mds +'/productionOrganization/page',
    method: 'get',
    params
  })
}
// 设备组
export function standardResource(params) {
  return request({
    url: baseUrl.mds +'/standardResource/page',
    method: 'get',
    params
  })
}
// 设备
export function physicalResource(params) {
  return request({
    url: baseUrl.mds +'/physicalResource/mainResource/page',
    method: 'get',
    params
  })
}
// 版本数据对比
export function contrastCapacityLoad(params) {
  return request({
    url: baseUrl.mps +'/capacityBalanceVersion/contrastCapacityLoad',
    method: 'get',
    params
  })
}

// 工序下拉
export function standardStep(params) {
  return request({
    url: baseUrl.mds +'/standardStep/page',
    method: 'get',
    params
  })
}

// 删除
export function capacitySupplyRelationshipDelete(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/delete',
    method: 'post',
    data
  })
}

// 删除
export function capacitySupplyRelationshipGetList(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/selectCollect',
    method: 'post',
    data
  })
}

// 产能明细-产线下拉-根据人员权限
export function resourceLineDropDown(params) {
  return request({
    url: baseUrl.mds +'/physicalResource/resourceLineDropDownByUser',
    method: 'get',
    params
  })
}

// 分配异常明细
export function exceptionDetails(params) {
  return request({
    url: baseUrl.mps +'/capacitySupplyRelationshipException/page',
    method: 'get',
    params
  })
}

// 周产能平衡-最新计算时间
export function maxVersionTime(params){
  return request({
    url: baseUrl.mps +'/capacityBalanceVersion/weekMaxVersionTime',
    method: 'get',
    params
  })
}

// 产能平衡版本-历史发布版本
export function historyVersionList(params){
  return request({
    url: baseUrl.mps +'/capacityBalanceVersion/versionDropdown',
    method: 'get',
    params
  })
}

// 产能平衡版本-根据版本号获取预测信息
export function getVersionInfo(params) {
  return request({
    url: currentService + '/capacityBalanceVersion/selectVersionInfoByVersionCode',
    method: 'get',
    params
  })
}

// 产能平衡版本-产能明细 月
export function selectCollectByVersion(data) {
  return request({
    url: currentService + '/capacitySupplyRelationship/selectCollectByVersion',
    method: 'post',
    data
  })
}

// 产能平衡管理-班次查询
export function capacityShift(params) {
  return request({
    url: currentService + '/capacityLoad/capacityShift',
    method: 'get',
    params
  })
}

export function exportAreaCapacityShift() {
  let urlData = currentService + '/capacityLoad/exportAreaCapacityShift';
  Axios.get(urlData, {
    responseType: "blob",
    headers: {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    },
  }).then((res) => {
    let a = document.createElement("a");
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers['content-disposition'];
    const fileName = window.decodeURI(contentDisposition.split("attachment;filename*=utf-8'zh_cn'")[1]);
    a.setAttribute('href', objUrl);
    a.setAttribute('download', fileName);
    a.click();
  }).catch((error) => {
    console.error("导出失败：", error);
  });
}