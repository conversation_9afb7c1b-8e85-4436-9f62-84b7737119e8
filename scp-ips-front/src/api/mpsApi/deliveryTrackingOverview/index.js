import request from '@/utils/requestMps/request'
import baseUrl from '@/utils/baseUrl';
// 生产组织
export function orgOption(params) {
  return request({
      url: baseUrl.mps + '/dynamicDeliveryTracking/org',
      method: 'get',
      params
  })
}
// 工序下拉
export function operationStep(params) {
  return request({
      url: baseUrl.mps + '/dynamicDeliveryTracking/operationStep',
      method: 'get',
      params
  })
}

// 产品编码
export function masterPlanProductDropDown(params) {
  return request({
      url: baseUrl.mds + '/newProductStockPoint/masterPlanProductDropDown',
      method: 'get',
      params
  })
}

// 动态追踪报表
export function deliveryTrackingView(data) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTrackingTask/deliveryTrackingView',
    method: 'post',
    data
  })
}

// 取消紧急交付
export function doUnPublish(data) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTracking/doUnPublish',
    method: 'post',
    autoShowMsg: true,
    data
  })
}

// 明细查询
export function getDetailApi(params) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTrackingSubTask/selectSubTaskByTaskIdAndReport',
    method: 'get',
    autoShowMsg: true,
    params
  })
}
