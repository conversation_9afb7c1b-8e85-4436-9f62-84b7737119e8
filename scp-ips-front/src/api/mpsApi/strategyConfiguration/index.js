import request from '@/utils/requestMps/request'
import baseUrl from '@/utils/baseUrl';
// 计算方案查询
export function pageCalculatePlan() {
  return request({
    url: baseUrl.mps + '/calculatePlan/page',
    method: 'get',
  })
}
// 新增
export function createCalculatePlanType(data) {
  return request({
    url: baseUrl.mps + '/calculatePlanType/create',
    method: 'post',
    data
  })
}
// 删除
export function deleteCalculatePlanType(data) {
  return request({
    url: baseUrl.mps + '/calculatePlanType/delete',
    method: 'post',
    data
  })
}
// 修改
export function updateCalculatePlanType(data) {
  return request({
    url: baseUrl.mps + '/calculatePlanType/update',
    method: 'post',
    data
  })
}
// 设为默认方案
export function setupDefault(params) {
  return request({
    url: baseUrl.mps + '/calculatePlanType/setupDefault',
    method: 'get',
    params
  })
}
// 获取对应计算类型下面的计划方案
export function planenums(params) {
  return request({
    url: baseUrl.mps + '/calculatePlanType/planenums',
    method: 'get',
    params,
  })
}
// 查询方案的计算模块
export function selectByCalculatePlanTypeId(params) {
  return request({
    url: baseUrl.mps + '/calculateModule/selectByCalculatePlanTypeId',
    method: 'get',
    params,
  })
}
// 计算模块调整位置
export function adjustLocation(params) {
  return request({
    url: baseUrl.mps + '/calculateModule/adjustLocation',
    method: 'get',
    params,
  })
}
// 新增模块
export function createCalculateModule(data) {
  return request({
    url: baseUrl.mps + '/calculateModule/create',
    method: 'post',
    data
  })
}
// 修改模块
export function updateCalculateModule(data) {
  return request({
    url: baseUrl.mps + '/calculateModule/update',
    method: 'post',
    data
  })
}
// 删除模块
export function deleteCalculateModule(data) {
  return request({
    url: baseUrl.mps + '/calculateModule/delete',
    method: 'post',
    data
  })
}
// 计划下发 - 根据计算模块id查询详情
export function detailPlanIssue(params) {
  return request({
    url: baseUrl.mps + '/calculateModulePlanIssue/queryDetail',
    method: 'get',
    params,
  })
}
// 计划下发 - 新增
export function createPlanIssue(data) {
  return request({
    url: baseUrl.mps + '/calculateModulePlanIssue/create',
    method: 'post',
    data
  })
}

// 排程优化 - 根据计算模块id查询详情
export function detailOptimize(params) {
  return request({
    url: baseUrl.mps + '/calculateModuleSchedulingOptimize/queryDetail',
    method: 'get',
    params,
  })
}
// 排程优化 - 新增
export function createOptimize(data) {
  return request({
    url: baseUrl.mps + '/calculateModuleSchedulingOptimize/create',
    method: 'post',
    data
  })
}

// 取消计划 - 根据计算模块id查询详情
export function detailCancelPlan(params) {
  return request({
    url: baseUrl.mps + '/calculateModuleCancelPlan/queryDetail',
    method: 'get',
    params,
  })
}
// 取消计划 - 新增
export function createCancelPlan(data) {
  return request({
    url: baseUrl.mps + '/calculateModuleCancelPlan/create',
    method: 'post',
    data,
  })
}
// 冻结状态取值
export function lockStatus(params) {
  return request({
    url: baseUrl.mps + '/calculateModuleCancelPlan/lockStatus',
    method: 'get',
    params,
  })
}
// 取消计划的工序状态取值
export function operationStatus(params) {
  return request({
    url: baseUrl.mps + '/calculateModuleCancelPlan/operationStatus',
    method: 'get',
    params,
  })
}
// 筛选字段列表
export function fieldsOptions(params) {
  return request({
    url: baseUrl.mps + '/filtercondition/fields',
    method: 'get',
    params,
  })
}
// 符号列表
export function signsOptions(params) {
  return request({
    url: baseUrl.mps + '/filtercondition/signs',
    method: 'get',
    params,
  })
}
