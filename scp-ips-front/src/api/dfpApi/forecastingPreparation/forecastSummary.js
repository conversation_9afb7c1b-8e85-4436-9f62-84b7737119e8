import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'
const currentPath = baseUrl.dfp
import Axios from 'axios'

export function queryForecastSummaryReport(data) {
  return request({
    url: currentPath + '/consistenceDemandForecastData/queryForecastSummaryReport',
    method: 'post',
    data,
  })
}

export function exportData(data) {
  const url = currentPath + '/consistenceDemandForecastData/exportData';
  Axios.post(url, data ,{
    responseType: 'blob',
    headers: {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    }
  }).then(res => {
    let a = document.createElement('a');
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers['content-disposition'];
    const fileName = window.decodeURI(contentDisposition.split('filename=')[1]);
    a.setAttribute('href', objUrl);
    a.setAttribute('download', fileName);
    a.click();
  });
}