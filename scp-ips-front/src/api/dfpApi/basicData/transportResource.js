import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'

let currentUrl = baseUrl.dfp;
let currentUrlMds = baseUrl.mds;


// export function batchInvalidApi(data) {
//   return request({
//     url: currentUrl + '/transportResource/batchInvalidApi',
//     method: 'post',
//     data
//   })
// }
export function deleteApi(data) {
  return request({
    url: currentUrl + '/transportResource/delete',
    method: 'post',
    data
  })
}
export function createApi(data) {
  return request({
    url: currentUrl + '/transportResource/create',
    method: 'post',
    data
  })
}
export function updateApi(data) {
  return request({
    url: currentUrl + '/transportResource/update',
    method: 'post',
    data
  })
}
export function detailApi(id) {
  return request({
    url: currentUrl + `/transportResource/detail/${id}`,
    method: 'get',
  })
}

export function syncApi() {
  return request({
    url: currentUrl + '/transportResource/sync',
    method: 'post',
  })
}


// 资源类型下拉
export function dropDownResourceType(params) {
  return request({
    url: currentUrl + `/transportResource/resourceType/dropDown`,
    method: 'get',
    params
  })
}
// 资源名称下拉
export function dropDownResource(params) {
  return request({
    url: currentUrl + `/transportResource/resourceName/dropDown`,
    method: 'get',
    params
  })
}
// 销售组织代码--Enable--丢弃
// export function saleOrganizeDropDownEnable() {
//   return request({
//     url: currentUrlMds + `/saleOrganize/dropDownEnable`,
//     method: 'get',
//   })
// }
// 销售组织代码--Enable
export function saleOrganizeDropDownEnable() {
  return request({
    url: currentUrl + `/saleOrganize/dropDownEnable`,
    method: 'get',
  })
}