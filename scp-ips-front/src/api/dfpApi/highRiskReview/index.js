import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'

// 列表数据
export function getTableData(data, url, method, key, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURIComponent(JSON.stringify(data.queryCriteriaParam))
      : '';
  let _url = `${baseUrl.dfp}/${url}?organizationCode=${data.organizationCode}&pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
  return request({
    url: _url,
    method: method,
    headers: {
      componentKey: key
    },
    params: params
  });
}

// 高风险产品订单评审 下拉
export function getHighRiskOptions(params) {
  return request({
    url: baseUrl.dfp + `/highRiskProductOrder/dropdowns`,
    method: 'get',
    params
  })
}

// 标准工艺列表
export function getStandardStepList(params) {
  return request({
    url: baseUrl.dfp + `/highRiskProductOrder/standardStepList`,
    method: 'get',
    params
  })
}

// 获取表2数据
export function getMaterialData(data) {
  return request({
    url: baseUrl.mrp + `/inventoryOrderRelationship/pageCustom`,
    method: 'post',
    data
  })
}