import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl';
// 计划批次反馈
export function detailFeedbackPlanUnits(data) {
  return request({
    url: baseUrl.sds + '/feedbackPlanUnit/detail/' + data,
    method: 'get',
  })
}
export function deleteFeedbackPlanUnits(data) {
  return request({
    url: baseUrl.sds + '/feedbackPlanUnit/delete',
    method: 'post',
    data
  })
}
export function createFeedbackPlanUnits(data) {
  return request({
    url: baseUrl.sds + '/feedbackPlanUnit/create',
    method: 'post',
    data
  })
}
export function updateFeedbackPlanUnits(data) {
  return request({
    url: baseUrl.sds + '/feedbackPlanUnit/update',
    method: 'post',
    data
  })
}

// 工序生产反馈
export function detailFeedbackOperations(data) {
  return request({
    url: baseUrl.sds + '/feedbackOperation/detail/' + data,
    method: 'get',
  })
}
export function deleteFeedbackOperations(data) {
  return request({
    url: baseUrl.sds + '/feedbackOperation/delete',
    method: 'post',
    data
  })
}
export function createFeedbackOperations(data) {
  return request({
    url: baseUrl.sds + '/feedbackOperation/create',
    method: 'post',
    data
  })
}
export function updateFeedbackOperations(data) {
  return request({
    url: baseUrl.sds + '/feedbackOperation/update',
    method: 'post',
    data
  })
}

// 入库反馈
export function detailFeedbackStorage(data) {
  return request({
    url: baseUrl.sds + '/feedbackStocking/detail/' + data,
    method: 'get',
  })
}
export function deleteFeedbackStorage(data) {
  return request({
    url: baseUrl.sds + '/feedbackStocking/delete',
    method: 'post',
    data
  })
}
export function createFeedbackStorage(data) {
  return request({
    url: baseUrl.sds + '/feedbackStocking/create',
    method: 'post',
    data
  })
}
export function updateFeedbackStorage(data) {
  return request({
    url: baseUrl.sds + '/feedbackStocking/update',
    method: 'post',
    data
  })
}

// 领料反馈
export function detailFeedbackPicking(data) {
  return request({
    url: baseUrl.sds + '/feedbackPicking/detail/' + data,
    method: 'get',
  })
}
export function deleteFeedbackPicking(data) {
  return request({
    url: baseUrl.sds + '/feedbackPicking/delete',
    method: 'post',
    data
  })
}
export function createFeedbackPicking(data) {
  return request({
    url: baseUrl.sds + '/feedbackPicking/create',
    method: 'post',
    data
  })
}
export function updateFeedbackPicking(data) {
  return request({
    url: baseUrl.sds + '/feedbackPicking/update',
    method: 'post',
    data
  })
}
