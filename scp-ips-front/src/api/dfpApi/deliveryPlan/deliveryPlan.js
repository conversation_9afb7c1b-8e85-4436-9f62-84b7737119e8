import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'
import Axios from 'axios';

export function queryInventoryShiftData(data) {
  return request({
    url: baseUrl.dfp + '/inventoryShift/queryInventoryShiftData',
    method: 'post',
    data
  })
}

// 版本号
export function targetVersion(params) {
  return request({
    url: baseUrl.dfp + '/deliveryPlanVersion/targetVersion',
    method: 'get',
    params
  })
}

// 获取本厂编码-通过主机厂
export function getProductCodeByOemCodeEdi(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getProductCodeByOemCodeEdi',
    method: 'get',
    params
  })
}

// 发货对接单详情
// 自动生成发货对接单号
export function generateDeliveryDockingNumber(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/generateDeliveryDockingNumber',
    method: 'get',
    params
  })
}
// 通过本厂编码取物料信息字段
export function getNewProductStockPoint(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getNewProductStockPoint',
    method: 'get',
    params
  })
}
// 获取原始需求版本号
export function getOriginDemandVersionCode(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getOriginDemandVersionCode',
    method: 'get',
    params
  })
}
// 通过版本号与主机厂编码获取装车需求表中本厂编码
export function getProductCodeByOemCodeAndVersionCodeFromLoadDemand(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getProductCodeByOemCodeAndVersionCodeFromLoadDemand',
    method: 'get',
    params
  })
}
// 通过版本号与主机厂编码获取发货计划表中本厂编码
export function getProductCodeByOemCode(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getProductCodeByOemCode',
    method: 'get',
    params
  })
}
// 获取发货计划版本号
export function getDeliveryPlanVersionCode(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getDeliveryPlanVersionCode',
    method: 'get',
    params
  })
}
// 获取供应类型-贸易类型
export function getBusinessAndMarketType(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getBusinessAndMarketType',
    method: 'get',
    params
  })
}

// 主机厂地址
export function getCustomerAddress(params) {
  return request({
    url: baseUrl.dfp + '/oemAddressInventoryLog/oemAddressDropdown',
    method: 'get',
    params
  })
}


// 获取线路编码
export function getRoutingCode(params) {
  return request({
    url: baseUrl.dfp + '/transportRouting/allRoutingCode/dropDown',
    method: 'get',
    params
  })
}
// 获取运输方式
export function getTransportMode(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getTransportMode',
    method: 'get',
    params
  })
}
// 获取货位
export function getLocation(transitStockCode) {
  return request({
    url: baseUrl.dfp + `/oemAddressInventoryLog/stockLocationDropdown?transitStockCode=${transitStockCode}`,
    method: 'post',
  })
}
// 获取子库存
export function getSubInventory(data) {
  return request({
    url: baseUrl.dfp + '/oemAddressInventoryLog/transitStockDropdown',
    method: 'post',
    data
  })
}
// 获取车长(米)
export function getVehicleLength(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getVehicleLength',
    method: 'get',
    params
  })
}

// 修改
export function updateApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/update',
    method: 'post',
    data
  })
}
// 新增
export function createApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/create',
    method: 'post',
    data
  })
}
// 批量新增
export function createBatchApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/doCreateBatch',
    method: 'post',
    data
  })
}

// 删除
export function deleteApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/delete',
    method: 'post',
    data
  })
}
// 详情
export function detailApi(id) {
  return request({
    url: baseUrl.dfp + "/deliveryDockingOrder/detail/" + id,
    method: "get",
  });
}
// 本厂编码下拉
export function getProductList(params) {
  return request({
    url: baseUrl.mds + "/newProductStockPoint/dropDownAll",
    method: "get",
    params,
  });
}
// 本厂编码下拉 - 新
export function getProductListNew(params) {
  return request({
    url: baseUrl.mds + "/newProductStockPoint/selectProductCodeLike",
    method: "get",
    params,
  });
}
// 子信息详情查询
export function selectDockingOrderDetail(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/selectDockingOrderDetail',
    method: 'get',
    params
  })
}
// 发货对接单 导入
export function uploadFile(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/importExcel',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data,
  })
}

// 发布
export function publish(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/publish',
    method: 'get',
    params
  })
}
// 发布-传后端产品编码和发货数量
export function publishProduct(deliveryDockingNumber,data) {
  return request({
    url: baseUrl.dfp + `/deliveryDockingOrder/publish?deliveryDockingNumber=${deliveryDockingNumber}`,
    method: 'post',
    data
  })
}
// 手动同步
export function sync(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/sync',
    method: 'get',
    params
  })
}
// 撤回发布
export function doWithDrawPublish(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/doWithDrawPublish',
    method: 'get',
    params
  })
}

// 对接单状态查询
export function getStatusByDeliveryDockingNumber(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getStatusByDeliveryDockingNumber',
    method: 'get',
    params
  })
}
// 对接单下拉
export function deliveryDockingOrderDropdown(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/dropdown',
    method: 'get',
    params
  })
}


// 发货对接单详情 产品编码校验
export function checkTallyOrderMode(params) {
  return request({
    url: baseUrl.mds + '/newProductStockPoint/checkTallyOrderMode',
    method: 'get',
    params
  })
}



// 对接单详情
// 箱数计算
export function getBoxQuantity(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/getBoxQuantity',
    method: 'get',
    params
  })
}

// 发货对接单详情
export function deleteDetailApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/doDeleteBatch',
    method: 'post',
    data
  })
}
// 新增
export function createDetailApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/create',
    method: 'post',
    data
  })
}

export function createBatchDetailApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/doCreateBatch',
    method: 'post',
    data
  })
}
// 修改
export function updateDetailApi(data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/update',
    method: 'post',
    data
  })
}
// 查询
export function pageDetailApi(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/page',
    method: 'get',
    params
  })
}

// 获取箱子类型(器具)通过本厂编码
export function getBoxTypeByProductCode(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getBoxTypeByProductCode',
    method: 'get',
    params
  })
}

// 获取本厂编码-通过主机厂
export function getProductCodeByOemCodeToVehicleCode(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getProductCodeByOemCodeToVehicleCode',
    method: 'get',
    params
  })
}

// 获取箱子类型(器具)通过本厂编码 code
export function getBoxType(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getBoxType',
    method: 'get',
    params
  })
}

// 获取箱子类型(器具)通过本厂编码 code
export function getBoxTypeDropDown(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getBoxTypeDropDown',
    method: 'get',
    params
  })
}

// Edi判断数量
export function compare(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/compare',
    method: 'get',
    params
  })
}

// 保存
export function saveForm(params, data) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrderDetail/save',
    method: 'post',
    data,
    params,
  })
}

// 运输工具名称-通过承运人名称
export function getCarrierName(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getConveyanceByCode',
    method: 'get',
    params
  })
}

// 获取运输条款-通过主机厂编码
export function getTransitClause(params) {
  return request({
    url: baseUrl.dfp + '/oem/getTransitClauseByCodes',
    method: 'get',
    params
  })
}

// 通过 deliveryDockingNumber 查发货对接单详情
export function getDetailByDockingNumber(params) {
  return request({
    url: baseUrl.dfp + '/deliveryDockingOrder/getMainDetailByDockingNumber',
    method: 'get',
    params
  })
}

// 发货对接单 导出模板
export function exportDeliveryTemplate(type) {
  let urlData = `${baseUrl.dfp}/deliveryDockingOrder/exportTemplate?objectType=${type}`
  Axios.get(urlData, {
    responseType: "blob",
    headers: {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    },
  }).then((res) => {
    let a = document.createElement("a");
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers["content-disposition"];
    const fileName = window.decodeURI(contentDisposition.split('=')[1]);
    console.log(contentDisposition, fileName)
    a.setAttribute("href", objUrl);
    a.setAttribute("download", fileName);
    a.click();
  });
}