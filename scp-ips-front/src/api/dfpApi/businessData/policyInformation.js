import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'
const currentPath = baseUrl.dfp
const currentPathAuth = baseUrl.auth

// create
export function createApi(data) {
  return request({
    url: currentPath + '/policyInformation/create',
    method: 'post',
    data,
  })
}
// // delete
// export function deleteApi(data) {
//   return request({
//     url: currentPath + '/policyInformation/delete',
//     method: 'post',
//     data,
//   })
// }

// delete
export function deleteApi(data) {
  return request({
    url: currentPath + '/policyInformation/deleteByVersion',
    method: 'post',
    data,
  })
}

// update
export function updateApi(data) {
  return request({
    url: currentPath + '/policyInformation/update',
    method: 'post',
    data,
  })
}

// detail
export function detailApi(data) {
  return request({
    url: currentPath + '/policyInformation/detail/' + data,
    method: 'get',
  })
}

// 根据编码获取值集下拉--通用
export function dropdownByCollectionCode(params) {
  return request({
    url: currentPathAuth + '/collectionValue/dropdownByCollectionCode',
    method: 'get',
    params,
  })
}
