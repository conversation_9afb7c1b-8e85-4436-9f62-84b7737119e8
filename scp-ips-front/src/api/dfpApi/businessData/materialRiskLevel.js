import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'
import qs from "autoprefixer/lib/brackets";
const currentPath = baseUrl.dfp
const currentPathAuth = baseUrl.auth

export function materialRiskLevelUpdate(data) {
  return request({
    url: baseUrl.dfp + '/materialRiskLevel/updateRiskLevel',
    method: 'post',
    params: data
  })
}

export function materialRiskLevelreCalculate() {
  return request({
    url: baseUrl.dfp + '/materialRiskLevel/reCalculate',
    method: 'post',
  })
}

export function riskLevelRulePage() {
  return request({
    url: baseUrl.dfp + '/riskLevelRule/page',
    method: 'get',
  })
}

export function getDetailsByRiskId(id) {
  return request({
    url: baseUrl.dfp + '/riskLevelRule/getDetailsByRiskId',
    method: 'get',
    params: { riskId: id }
  })
}


export function riskLevelRuleUpdate(data) {
  return request({
    url: baseUrl.dfp + '/riskLevelRuleDetail/update',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data
  })
}

export function batchUpdateLevel(data) {
  return request({
    url: baseUrl.dfp + '/materialRiskLevel/batchUpdateLevel',
    method: 'post',
    data
  })
}

