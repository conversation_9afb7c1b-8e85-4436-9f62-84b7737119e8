import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'
const currentPath = baseUrl.dfp
const currentPathAuth = baseUrl.auth

export function loadingDemandSubmission(data) {
  return request({
    url: baseUrl.dfp + '/loadingDemandSubmission/submission',
    method: 'post',
    // headers: {'Content-Type': 'multipart/form-data'},
    data
  })
}

export function deleteByVersion(data) {
  return request({
    url: baseUrl.dfp + '/loadingDemandSubmission/deleteByVersion',
    method: 'post',
    data
  })
}

// 查看源文件
export function downloadFlies(params) {
  return request({
    url: baseUrl.dfp + `/loadingDemandSubmission/download`,
    method: 'get',
    params
  })
}

// 修改需求提报
export function updateDemand(data) {
  return request({
    url: baseUrl.dfp + `/loadingDemandSubmission/update`,
    method: 'post',
    data
  })
}
// 上传文件
export function uploadFile(data) {
  return request({
    url: baseUrl.dfp + `/loadingDemandSubmission/upload`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}
//模板数据上传
export function importDemand(data) {
  return request({
    url: baseUrl.dfp + `/loadingDemandSubmission/importDemand`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}
//创建新版本
export function createNewVersion() {
  return request({
    url: baseUrl.dfp + `/loadingDemandSubmission/createNewVersion`,
    method: 'post',
  })
}

// 上传附件
export function uploadAttach(data) {
  return request({
    url: baseUrl.dfp + `/dfpDemandForecastAttachments/upload`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}
// 上传单个附件
export function uploadOneAttach(data) {
  return request({
    url: baseUrl.dfp + `/dfpDemandForecastAttachments/uploadFile`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}

//根据组织获取需求类型权限
export function getDemandTypeEnum() {
  return request({
    url: baseUrl.dfp + `/loadingDemandSubmission/getDemandTypeEnum`,
    method: 'get',
  })
}
export function syncEdi() {
  return request({
    url: baseUrl.dfp + '/loadingDemandSubmission/syncEdi',
    method: 'get',
  })
}
export function syncDemand(data) {
  return request({
    url: baseUrl.dfp + '/loadingDemandSubmission/syncDemand',
    method: 'post',
    data
  })
}
