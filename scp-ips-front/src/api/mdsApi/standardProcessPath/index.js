import request from "@/utils/requestMds/request";
import baseUrl from "@/utils/baseUrl";

// 工艺路径
export function detailStandardRouting(data) {
  return request({
    url: baseUrl.mds + "/standardRouting/detail/" + data,
    method: "get",
  });
}
export function detailSpecStandardRouting(data) {
  return request({
    url: baseUrl.mds + "/standardRouting/detailNew/" + data,
    method: "get",
  });
}

export function deleteStandardRouting(data) {
  return request({
    url: baseUrl.mds + "/standardRouting/delete",
    method: "post",
    data,
  });
}
export function createStandardRouting(data) {
  return request({
    url: baseUrl.mds + "/standardRouting/create",
    method: "post",
    data,
  });
}
export function createSpecStandardRouting(data) {
  return request({
    url: baseUrl.mds + "/standardRouting/createSpec",
    method: "post",
    data,
  });
}
// updateSpecStandardRouting createSpecStandardRouting detailSpecStandardRouting
export function updateStandardRouting(data) {
  return request({
    url: baseUrl.mds + "/standardRouting/update",
    method: "post",
    data,
  });
}
export function updateSpecStandardRouting(data) {
  return request({
    url: baseUrl.mds + "/standardRouting/updateSpec",
    method: "post",
    data,
  });
}

// 工艺路径步骤
export function detailStandardRoutingStep(data) {
  return request({
    url: baseUrl.mds + "/standardRoutingStep/detail/" + data,
    method: "get",
  });
}
export function detailSpecStandardRoutingStep(data) {
  return request({
    url: baseUrl.mds + "/standardRoutingStep/detailNew/" + data,
    method: "get",
  });
}
export function deleteStandardRoutingStep(data) {
  return request({
    url: baseUrl.mds + "/standardRoutingStep/delete",
    method: "post",
    data,
  });
}
export function createStandardRoutingStep(data) {
  return request({
    url: baseUrl.mds + "/standardRoutingStep/create",
    method: "post",
    data,
  });
}
export function createSpecStandardRoutingStep(data) {
  return request({
    url: baseUrl.mds + "/standardRoutingStep/createSpec",
    method: "post",
    data,
  });
}
export function updateStandardRoutingStep(data) {
  return request({
    url: baseUrl.mds + "/standardRoutingStep/update",
    method: "post",
    data,
  });
}
export function updateSpecStandardRoutingStep(data) {
  return request({
    url: baseUrl.mds + "/standardRoutingStep/updateSpec",
    method: "post",
    data,
  });
}
// 标准工艺代码

export function standardOperationDropdown(data) {
  return request({
    url: baseUrl.mds + "/standardStep/dropdown",
    method: "get",
  });
}
