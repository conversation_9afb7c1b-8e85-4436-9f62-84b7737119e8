import request from '@/utils/requestMds/request'
import baseUrl from '@/utils/baseUrl'

// 资源允许规则
export function detailSpecBatch(data) {
  return request({
    url: baseUrl.mds + '/specBatch/detail/' + data,
    method: 'get',
  })
}

export function deleteSpecBatch(data) {
  return request({
    url: baseUrl.mds + '/specBatch/delete',
    method: 'post',
    data,
  })
}

export function createSpecBatch(data) {
  return request({
    url: baseUrl.mds + '/specBatch/create',
    method: 'post',
    data,
  })
}
export function updateSpecBatch(data) {
  return request({
    url: baseUrl.mds + '/specBatch/update',
    method: 'post',
    data,
  })
}

export function detailSpecBatchNum(data) {
    return request({
      url: baseUrl.mds + '/specBatchNum/detail/' + data,
      method: 'get',
    })
  }
  
  export function deleteSpecBatchNum(data) {
    return request({
      url: baseUrl.mds + '/specBatchNum/delete',
      method: 'post',
      data,
    })
  }
  
  export function createSpecBatchNum(data) {
    return request({
      url: baseUrl.mds + '/specBatchNum/create',
      method: 'post',
      data,
    })
  }
  export function updateSpecBatchNum(data) {
    return request({
      url: baseUrl.mds + '/specBatchNum/update',
      method: 'post',
      data,
    })
  }
  // 公共接口 枚举获取
  export function dropdownEnum(params) {
    return request({
      url: baseUrl.mds + '/dropdown/enum',
      method: 'get',
      params,
    })
  }
