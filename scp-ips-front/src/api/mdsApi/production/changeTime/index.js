import request from '@/utils/requestMds/request'
import baseUrl from '@/utils/baseUrl';

// 物品切换相关
export function detailProductionChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/productionChangeoverRule/detail/'+data,
    method: 'get',
  })
}
export function deleteProductionChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/productionChangeoverRule/delete',
    method: 'post',
    data
  })
}
export function createProductionChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/productionChangeoverRule/create',
    method: 'post',
    data
  })
}
export function updateProductionChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/productionChangeoverRule/update',
    method: 'post',
    data
  })
}
// 矩阵表格查询
export function queryMatrixProduction(params) {
  return request({
    url: baseUrl.mds + '/productionChangeoverRule/queryMatrix',
    method: 'get',
    params
  })
}


// 工具切换相关
export function detailToolChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/toolChangeoverRule/detail/'+data,
    method: 'get',
  })
}
export function deleteToolChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/toolChangeoverRule/delete',
    method: 'post',
    data
  })
}
export function createToolChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/toolChangeoverRule/create',
    method: 'post',
    data
  })
}
export function updateToolChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/toolChangeoverRule/update',
    method: 'post',
    data
  })
}

// 规格切换相关
export function detailSpecChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/specChangeoverRule/detail/'+data,
    method: 'get',
  })
}
export function deleteSpecChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/specChangeoverRule/delete',
    method: 'post',
    data
  })
}
export function createSpecChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/specChangeoverRule/create',
    method: 'post',
    data
  })
}
export function updateSpecChangeoverRule(data) {
  return request({
    url: baseUrl.mds + '/specChangeoverRule/update',
    method: 'post',
    data
  })
}
