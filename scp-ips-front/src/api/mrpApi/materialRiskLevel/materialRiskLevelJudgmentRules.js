import request from '@/utils/requestMrp/request';
import baseUrl from '@/utils/baseUrl';
const currentPath = baseUrl.mrp;

//失效
export const failureApi = (data) => {
  return request({
    url: `${currentPath}/materialRiskLevelRule/loseEffectivenessBatch`,
    method: "post",
    data,
  });
};

//编辑
export const editApi = (data) => {
  return request({
    url: `${currentPath}/materialRiskLevelRule/update`,
    method: "post",
    data,
  });
};

// 添加
export const createApi = (data) => {
  return request({
    url: `${currentPath}/materialRiskLevelRule/create`,
    method: "post",
    data,
  });
};

// 删除
export function deleteApi(data) {
  return request({
    url: `${currentPath}/materialRiskLevelRule/delete`,
    method: 'post',
    data
  })
}

// 拆单
export const splitOrder = (data) => {
  return request({
    url: `${currentPath}/materialPlanNeed/splitTheOrder`,
    method: "post",
    data,
  });
};
