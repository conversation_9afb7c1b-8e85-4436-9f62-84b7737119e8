import request from "@/utils/requestMrp/request";
import baseUrl from "@/utils/baseUrl";

// 详情
export function detailApi(data) {
  return request({
    url: baseUrl.mrp + "/inventoryQuayDetail/detail/" + data,
    method: "get",
  });
}
// 删除
export function deleteApi(data) {
  return request({
    url: baseUrl.mrp + "/inventoryQuayDetail/delete",
    method: "post",
    data,
  });
}
// 新增
export function createApi(data) {
  return request({
    url: baseUrl.mrp + "/inventoryQuayDetail/create",
    method: "post",
    data,
  });
}
// 修改
export function updateApi(data) {
  return request({
    url: baseUrl.mrp + "/inventoryQuayDetail/update",
    method: "post",
    data,
  });
}

// 可用查找
export function availableSearch(params) {
  return request({
    url: baseUrl.mrp + "/inventoryQuayDetail/availableSearch",
    method: "get",
    params
  });
}

// 替代
export function alternative(data) {
  return request({
    url: baseUrl.mrp + "/inventoryQuayDetail/alternative",
    method: "post",
    data,
  });
}
export function syncQuayDetail(data) {
  return request({
    url: baseUrl.mrp + "/inventoryOceanFreight/sync",
    method: "post",
    data,
  });
}
//创建PO
export function bathcIssue(data) {
  return request({
    url: baseUrl.mrp + "/inventoryOceanFreight/batchCreatPo",
    method: "post",
    data,
  });
}
//自动创建PO
export function syncAutoCreatPo(data) {
  return request({
    url: baseUrl.mrp + "/inventoryOceanFreight/syncAutoCreatPo",
    method: "post",
    data,
  });
}

//批量修改
export function batchEditApi(data) {
  return request({
    url: baseUrl.mrp + "/inventoryQuayDetail/updateBatch",
    method: "post",
    data,
  });
}
