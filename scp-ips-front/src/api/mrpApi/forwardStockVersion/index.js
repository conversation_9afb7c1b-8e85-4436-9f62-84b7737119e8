import request from "@/utils/requestMrp/request";
import baseUrl from "@/utils/baseUrl";

// 期初库存版本

export function detailBohStockVersion(data) {
  return request({
    url: baseUrl.sds + "/bohStockVersion/detail/" + data,
    method: "get",
  });
}
// 删除
export function deleteBohStockVersion(data) {
  return request({
    url: baseUrl.sds + "/bohStockVersion/delete",
    method: "post",
    data,
  });
}
// 新增
export function createBohStockVersion(data) {
  return request({
    url: baseUrl.sds + "/bohStockVersion/create",
    method: "post",
    data,
  });
}
// 修改
export function updateBohStockVersion(data) {
  return request({
    url: baseUrl.sds + "/bohStockVersion/update",
    method: "post",
    data,
  });
}
// 下拉枚举获取
export function dropdownEnum(params) {
  return request({
    url: baseUrl.mrp + "/dropdown/enum",
    method: "get",
    params,
  });
}

// 下拉枚举获取
export function bohStockVersionDropdown() {
  return request({
    url: baseUrl.sds + "/bohStockVersion/dropdown",
    method: "get",
  });
}
