import request from '@/utils/requestMrp/request'
import baseUrl from '@/utils/baseUrl'
import Axios from 'axios'
const currentPath = baseUrl.mrp

export function updateApi(data) {
  return request({
    url: currentPath + '/glassSubstitutionRelationship/update',
    method: 'post',
    data,
  })
}

export function deleteApi(data) {
  return request({
    url: currentPath + '/glassSubstitutionRelationship/delete',
    method: 'post',
    data,
  })
}

// 导入
export function importData(data) {
  return request({
    url: currentPath + '/glassSubstitutionRelationship/upload',
    method: 'post',
    data
  })
}

// 数据生成
export function generateData(data) {
  return request({
    url: currentPath + '/glassSubstitutionRelationship/generateData',
    method: 'post',
    data
  })
}

// 导出模板
export function ExportTemplate() {
  let urlData = `${baseUrl.mrp}/glassSubstitutionRelationship/exportTemplate`
  Axios.get(urlData, {
    responseType: "blob",
    headers: {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    },
  }).then((res) => {
    let a = document.createElement("a");
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers["content-disposition"];
    const fileName = window.decodeURI(contentDisposition.split('filename=')[1]);
    a.setAttribute("href", objUrl);
    a.setAttribute("download", fileName);
    a.click();
  });
}

// 生产BOM下拉数据
export function dropDownProductBom(data) {
  return request({
    url: currentPath + '/glassSubstitutionRelationship/dropDownSubstituteProduct',
    method: 'post',
    data
  })
}

// 新增
export function addApi(data) {
  return request({
    url: currentPath + '/glassSubstitutionRelationship/create',
    method: 'post',
    data
  })
}

// 批量修改
export function batchUpdateApi(data) {
  return request({
    url: currentPath + '/glassSubstitutionRelationship/batchUpdate',
    method: 'post',
    data
  })
}