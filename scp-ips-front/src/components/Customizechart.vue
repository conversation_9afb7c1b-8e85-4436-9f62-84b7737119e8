<template>
  <div class="chart-container">
    <div class="container-box" ref="containerChart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "Customize<PERSON><PERSON>",
  props: {
    options: {
      type: Object,
      required: true,
      default: {},
    },
  },
  data() {
    return {
      myChart: null,
    };
  },
  // watch: {
  //   options: {
  //     handler: (val) => {
  //       this.initChart();
  //     },
  //     deep: true,
  //   }
  // },
  watch: {
    options() {
      this.initChart();
    },
  },  created() {},
  mounted() {
    // this.initChart();
    window.addEventListener('resize', this.handleResize);
  }, 
  beforeDestroy() {
    this.myChart && this.myChart.dispose();

    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
    initChart() {
console.log('渲染图表----------------------------------------------------------', this.options)


      
      if (this.myChart) {
        this.myChart.dispose();
      }
      let chartDom = this.$refs.containerChart
      chartDom.removeAttribute("_echarts_instance_");
      this.myChart = echarts.init(chartDom);

      // window.onresize = myChart.resize();
      this.myChart.resize();
      this.options && this.myChart.setOption(this.options);
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
  .container-box {
    width: 100%;
    height: 100%;
  }
}
</style>
