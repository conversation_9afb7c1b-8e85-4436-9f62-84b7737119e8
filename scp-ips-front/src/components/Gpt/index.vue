<template>
  <div style="display: inline-block;">
    <template>
      <el-dialog
        :visible.sync="visible"
        v-if="visible"
        title="IBP供应计划的智能助手"
        :before-close="handleCancel"
        id="ips-dialog"
        :footer="null"
        append-to-body
        width="550px"
      >
        <div class="talk_con">
          <div class="talk_show" id="words">
            <!-- <div :class="[i.person == 'gpt' ? 'atalk' : 'btalk']" v-for="i in chatList"> -->
            <div
              :class="[item.person === 'gpt' ? 'atalk' : 'btalk']"
              v-for="(item, index) in chatList"
              :key="index"
            >
              <!-- <span>{{ item.person }}：{{ item.say }}</span> -->
              <svg-icon
                class-name="international-icon"
                :icon-class="item.icon"
                style="font-size: 25px;"
                @click="handelGptBox"
              />
              <span>{{ item.say }}</span>
            </div>
          </div>
          <div class="talk_input">
            <textarea
              type="text"
              class="talk_word"
              id="talkwords"
              v-model="text1"
              @keyup="onKeyup"
            />
            <!-- 绑定单击监听,把value传到vue的chatList中 -->
            <!-- <input
              type="button"
              value="发送"
              class="talk_sub"
              id="talksub"
              @click="fnAdd"
            /> -->
            <div>
              <svg-icon
                class-name="international-icon"
                icon-class="feiji"
                style="font-size: 30px;"
                class="talk_sub"
                @click="fnAdd"
              />
            </div>
          </div>
        </div>
        <!-- <Chat></Chat> -->
      </el-dialog>
    </template>
    <div>
      <svg-icon
        class-name="international-icon"
        icon-class="gptIcon"
        style="font-size: 24px;"
        @click="handelGptBox"
      />
    </div>
  </div>
</template>

<script>
// import { getScenarios } from '@/api/user'
// import { createScenario } from "@/api/coordination.js";
import { scenariosByUser, createScenario } from '@/api/scenario'

export default {
  name: 'createScenario',
  components: {},
  data() {
    return {
      confirmLoading: false,
      dataSource: [],
      form: {},
      visible: false,
      sel1: 1,
      text1: '',
      chatList: [
        {
          icon: 'ai',
          person: 'gpt',
          say: '我是IBP供应计划的智能助手，你有什么要问的吗：',
        },
      ],
      rules: {
        sourceScenarioId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        targetScenarioId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
      },
    }
  },
  props: {
    editRows: {},
    title: '',
  },
  watch: {
    editRows() {
      this.form = this.editRows
    },
  },
  created() {},
  mounted() {},
  methods: {
    // 延时函数
    sleep(delaytime = 10000) {
      return new Promise((resolve) => setTimeout(resolve, delaytime))
    },
    // 同步遍历，自定义延时时间
    async delayDo(
      iterList,
      callback = (data) => console.log(`数据：${data}`),
      delaytimeList,
    ) {
      let len = iterList.length
      for (let i = 0; i < len; i++) {
        callback(iterList[i], i)

        await this.sleep(delaytimeList[i])
      }
    },
    // 逐字显示内容
    getChatContent(text, index) {
      this.timer = setInterval(() => {
        this.textCount++

        if (this.textCount == text.length + 1) {
          this.textCount = 0
          this.chatList.splice(index, 1, { content: text, status: false })
          clearInterval(this.timer)

          return
        }

        // 取字符串子串
        let nowStr = text.substring(0, this.textCount)
        this.chatList.splice(index, 1, { content: nowStr, status: true })
      }, 200)
    },
    // 点击开始聊天
    initGPT() {
      const delaytimeList = [16000, 11000, 16000, 16000, 5000, 7000]
      this.delayDo(
        this.chatBaseList,
        (item, i) => {
          this.getChatContent(item, i)
        },
        delaytimeList,
      )
    },
    onKeyup(e) {
      if (e.keyCode === 13) {
        this.fnAdd()
      }
    },
    fnAdd() {
      console.log(this.text1)
      if (!this.text1) {
        alert('请输入内容!')
        return
      }
      // 列表追加数据push()
      // this.chatList.push({ person: this.sel1 == 0 ? '山东销售' : '市场部', say: this.text1 });
      this.chatList.push({
        // person: this.sel1 == 0 ? 'gpt' : '市场部',
        icon: 'aiUser',
        person: 'user',
        say: this.text1,
      })
      // 每次输入内容后,清空输入栏数据
      this.text1 = ''
      console.log(this.chatList)
    },
    handelGptBox() {
      this.visible = true
    },
    handleCancel() {
      this.visible = false
      //   this.$parent.handleCancel()
    },
  },
}
</script>
<style lang="scss" scoped>
#components-a-popconfirm-demo-placement .ant-btn {
  width: 70px;
  text-align: center;
  padding: 0;
  margin-right: 8px;
  margin-bottom: 8px;
}
.talk_con {
  width: 100%;
  height: 420px;
  border: 1px solid #e4e3e3;
  background: #f1efef;
  border-radius: 8px;
}

.talk_show {
  width: 100%;
  //   width: 480px;
  height: 320px;
  //   border: 1px solid #666;
  //   background: #fff;
  //   margin: 10px auto 0;
  overflow: auto;
}

.talk_input {
  width: 580px;
  margin: 10px auto 0;
}

.whotalk {
  width: 80px;
  height: 30px;
  float: left;
  outline: none;
}

.talk_word {
  background: #fff;
  border: 1px solid transparent;
  border-radius: 8px;
  box-shadow: 0 16px 20px 0 rgba(174, 167, 223, 0.2);
  width: 70%;
  height: 70px;
  padding: 0px;
  float: left;
  margin-left: 10px;
  outline: none;
  text-indent: 10px;
  resize: none;
}

.talk_sub {
  //   width: 56px;
  //   height: 30px;
  float: left;
  margin-left: 12px;
  margin-top: 20px;
  //   border: 1px solid #fff;
  cursor: pointer;
}
// .talk_sub:hover {
//   background: yellowgreen;
// }

.atalk {
  margin: 10px;
}

.atalk span {
  display: inline-block;
  background: #90d0f5;
  border-radius: 10px;
  color: #fff;
  padding: 5px 10px;
}

.btalk {
  margin: 10px;
  text-align: right;
}

.btalk span {
  display: inline-block;
  background: #d4d0cc;
  border-radius: 10px;
  color: #fff;
  padding: 5px 10px;
}
</style>
<style>
.algorithmPredict .form .ant-form-inline .ant-form-item {
  margin-right: 0px;
}
.ant-divider-horizontal {
  margin: 15px 0 10px 0 !important;
}
.ant-collapse-content {
  height: 350px;
}
</style>
<style>
.algorithmPredict #yhl-table .header .btns {
  width: 100% !important;
}
</style>
