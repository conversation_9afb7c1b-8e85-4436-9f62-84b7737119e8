<template>
  <el-form
    :rules="rules"
    :model="form"
    ref="ruleForm"
    label-width="120px"
    label-position="right"
  >
    <el-row>
      <el-col :span="12">
        <el-form-item
          :label="$t('creatScenes_timeLimit')"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
          prop="timeLimit"
        >
          <el-input
            v-model="form.timeLimit"
            :placeholder="$t('inputHolder')"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="$t('creatScenes_productionSupplyAndDemandCouple')"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
        >
          <el-select
            :placeholder="$t('selectHolder')"
            v-model="form.productionSupplyAndDemandCouple"
            style="width: 100%;"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in prodOptions"
              :value="item.value"
              :key="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('material')"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
        >
          <el-checkbox
            :label="$t('creatScenes_onlyBottleneckMaterial')"
            v-model="form.onlyBottleneckMaterial"
            true-label="YES"
            false-label="NO"
          ></el-checkbox>
        </el-form-item>
        <el-form-item
          :label="$t('resource')"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
        >
          <el-checkbox
            :label="$t('creatScenes_onlyBottleneckResource')"
            v-model="form.onlyBottleneckResource"
            true-label="YES"
            false-label="NO"
          ></el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          :label="$t('creatScenes_disableConstraints')"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
        >
          <el-checkbox-group v-model="checkedOptions">
            <template v-for="d in options">
              <el-checkbox :label="d.value" :key="d.value" :value="d.value">
                {{ d.label }}
              </el-checkbox>
            </template>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
export default {
  name: 'coordination',
  components: {},
  data() {
    return {
      prodOptions: [
        { label: this.$t('correct'), value: 'YES' },
        { label: this.$t('deny'), value: 'NO' },
      ],
      checkedOptions: [],
      form: {
        timeLimit: 0,
        productionSupplyAndDemandCouple: 'NO',

        disableConstraints: {
          forbiddenPurchaseLimit: 'NO',
          forbiddenInventoryLimit: 'NO',
          forbiddenProductionLimit: 'NO',
          forbiddenTransportationLimit: 'NO',
        },
        onlyBottleneckMaterial: 'NO',
        onlyBottleneckResource: 'NO',
        // peggingDelay: "",
      },
      options: [
        {
          label: this.$t('creatScenes_forbiddenPurchaseLimit'),
          value: 'forbiddenPurchaseLimit',
        },
        {
          label: this.$t('creatScenes_forbiddenInventoryLimit'),
          value: 'forbiddenInventoryLimit',
        },
        {
          label: this.$t('creatScenes_forbiddenProductionLimit'),
          value: 'forbiddenProductionLimit',
        },
        {
          label: this.$t('creatScenes_forbiddenTransportationLimit'),
          value: 'forbiddenTransportationLimit',
        },
      ],
      rules: {
        sourceScenarioId: [
          {
            required: true,
            message: this.$t('verificationCode'),
            trigger: 'change',
          },
        ],
        scenarioName: [
          {
            required: true,
            message: this.$t('verificationCode'),
            trigger: 'change',
          },
        ],
      },
    }
  },
  props: {
    visible: '',
    editRows: {},
    title: '',
  },
  watch: {
    editRows() {
      this.form = this.editRows
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleChange(value) {
      this.form = {
        timeLimit: 0,
        productionSupplyAndDemandCouple: 'NO',

        disableConstraints: {
          forbiddenPurchaseLimit: 'NO',
          forbiddenInventoryLimit: 'NO',
          forbiddenProductionLimit: 'NO',
          forbiddenTransportationLimit: 'NO',
        },
        onlyBottleneckMaterial: 'NO',
        onlyBottleneckResource: 'NO',
        // peggingDelay: "",
      }
    },
    handleOk() {
      let _valid = false
      this.$refs.ruleForm.validate((valid) => {
        _valid = valid
        if (valid) {
          this.options.forEach((item) => {
            const key = item.value
            const index = this.checkedOptions.findIndex((c) => c == key)

            if (index > -1) {
              this.form.disableConstraints[key] = 'YES'
            } else {
              this.form.disableConstraints[key] = 'NO'
            }
          })
        } else {
          return false
        }
      })

      if (_valid) {
        return this.form
      }
    },
  },
}
</script>
<style lang="scss">
.coordination {
  .modules_name {
    font-weight: 600;
  }
}
</style>
