'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const version = new Date().getTime()

const name = defaultSettings.title || 'vue Admin Template' // page title
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin')

// const resourceHead =
//   process.env.NODE_ENV != 'development' ? '/' : 'https://bpim-test.fuyaogroup.com/'
// const resourceHead = process.env.NODE_ENV != 'development'?'/':'http://***********/'
const resourceHead = process.env.NODE_ENV != 'development'?'/':'http://localhost:8887/'
// CDN资源
const cdn = {
  //   css: [resourceHead+'element/index.css'],
  js: [
    resourceHead + 'vue/vue.min.js',
    // resourceHead+'element/index.js',
    resourceHead + 'echarts/echarts.min.js',
    resourceHead + 'lodash/lodash.min.js',
    resourceHead + 'moment/moment.min.js',
    resourceHead + 'moment/locale/zh-cn.js',
    // resourceHead+'yhlcomponents/package/dist/yhlcomponents-ui.js',
    // resourceHead+'yhlcomponents-lowcode/package/dist/yhlcomponents-lowcode-ui.js',
    // resourceHead + 'yhlcomponents-gantt/package/dist/yhlcomponents-gantt-ui.js',
  ],
}
module.exports = {
  publicPath: '/',
  outputDir: 'app-ips-front',
  assetsDir: 'static',
  // lintOnSave: process.env.NODE_ENV === 'development',
  lintOnSave: false,
  productionSourceMap: false,
  devServer: {
    port: 8887,
    open: true,
    overlay: {
      warnings: false,
      errors: true,
    },
    historyApiFallback: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8760', // 一体化平台测试环境
        // target: 'https://bpim-api-test.fuyaogroup.com', // 一体化平台测试环境
        changeOrigin: true,
        ws: true,
        // secure: true,
        pathRewrite: { '^/api': '/' }, //目前可以不放开特殊需要可配置
      },
    },
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
      },
    },
    plugins: [new MonacoWebpackPlugin()],
    output: {
      filename: `js/[name].${version}.js`,
      chunkFilename: `js/[name].${version}.js`,
    },
    // output: {
    //   filename: `js/[name].js`,
    //   chunkFilename: `js/[name].js`,
    // },
    externals: {
      vue: 'Vue',
      lodash: '_',
      moment: 'moment',
      // 'element-ui':'ElementUI',
      // 'yhlcomponents-lowcode': 'yhlcomponentsLcdp',
      // 'yhlcomponents': 'yhlcomponents',
      echarts: 'echarts',
      // 'yhlcomponents-codemirror': 'yhlcomponentsCodemirror',
    },
  },
  css: {
    extract: {
      filename: `css/[name].${version}.css`,
      chunkFilename: `css/[name].${version}.css`,
    },
  },
  chainWebpack(config) {
    // 这里的作用是在后面index.html页面中通过link，script标签加载这些cdn链接。
    config.plugin('html').tap((args) => {
      args[0].cdn = cdn
      return args
    })
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial',
      },
    ])

    config.plugins.delete('prefetch')

    config.module.rule('svg').exclude.add(resolve('src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end()

    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap((options) => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config.when(process.env.NODE_ENV !== 'development', (config) => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            inline: /runtime\..*\.js$/,
          },
        ])
      config.plugin('CompressionWebpackPlugin').use(CompressionWebpackPlugin, [
        {
          filename: '[path].gz[query]',
          algorithm: 'gzip',
          test: /\.js$|\.css/, //匹配文件名
          threshold: 10240, //对超过10k的数据压缩
          minRatio: 0.8,
        },
      ])
      //   config
      //   .plugin('webpack-bundle-analyzer')
      //   .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
      //   .end()
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          codemirror: {
            name: 'chunk-codemirror',
            priority: 50,
            test: /[\\/]node_modules[\\/]_?yhlcomponents-codemirror(.*)/,
            enforce: true,
          },
          lowcode: {
            name: 'chunk-lowcode',
            priority: 40,
            test: /[\\/]node_modules[\\/]_?yhlcomponents-lowcode(.*)/,
            enforce: true,
          },
          yhlcomponents: {
            name: 'chunk-yhlcomponents',
            priority: 30,
            test: /[\\/]node_modules[\\/]_?yhlcomponents(.*)/,
            enforce: true,
          },
          //   elementUI: {
          //     name: 'chunk-elementUI',
          //     priority: 100,
          //     test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
          //     // enforce: true,
          //   },
          //   echarts: {
          //     name: 'chunk-echarts',
          //     priority: 110,
          //     test: /[\\/]node_modules[\\/]_?echarts(.*)/,
          //     // enforce: true,
          //   },
          workflow: {
            name: 'chunk-workflow',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?workflow-bpmn-modeler(.*)/,
            enforce: true,
          },
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial',
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'),
            minChunks: 2,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      })
      config.optimization.runtimeChunk('single')
      //去除debugger 和console
      config.optimization.minimizer('terser').tap((args) => {
        args[0].terserOptions.compress.drop_console = true
        args[0].terserOptions.compress.drop_debugger = true
        args[0].terserOptions.compress.pure_funcs = ['console.log']
        args[0].terserOptions.output = {
          comments: false,
        }
        return args
      })
    })
  },
}
